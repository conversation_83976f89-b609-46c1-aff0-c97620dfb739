import './globals.css'
import { Inter } from 'next/font/google'
import { Toaster } from 'react-hot-toast'
import <PERSON><PERSON><PERSON>nstall from '@/components/features/PWAInstall'

const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter',
})

export const metadata = {
  title: 'Limitless Options - Trading Hub',
  description: 'Professional trading checklist, journal, and analytics platform for Limitless Options community',
  keywords: 'trading, checklist, journal, forex, stocks, limitless options, trading hub, analytics',
  authors: [{ name: 'Limitless Options' }],
  creator: 'Limitless Options',
  publisher: 'Limitless Options',
  robots: 'index, follow',
  openGraph: {
    title: 'Limitless Options - Trading Hub',
    description: 'Professional trading checklist, journal, and analytics platform',
    type: 'website',
    locale: 'en_US',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Limitless Options - Trading Hub',
    description: 'Professional trading checklist, journal, and analytics platform',
  },
  manifest: '/manifest.json',
  appleWebApp: {
    capable: true,
    statusBarStyle: 'default',
    title: 'Trading Hub',
  },
  formatDetection: {
    telephone: false,
  },
}

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#3b82f6' },
    { media: '(prefers-color-scheme: dark)', color: '#1e40af' },
  ],
}

export default function RootLayout({ children }) {
  return (
    <html lang="en" className="h-full">
      <body className={`${inter.className} h-full antialiased bg-gradient-to-br from-[#0A0F0F] via-[#0C1612] to-[#0E1D1A]`}>
        <div className="min-h-screen">
          {children}
          <PWAInstall />
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: 'rgba(245, 245, 241, 0.95)',
                color: '#0A0F0F',
                border: '1px solid rgba(244, 196, 106, 0.3)',
                borderRadius: '16px',
                fontSize: '14px',
                fontWeight: '600',
                boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2)',
                backdropFilter: 'blur(20px)',
              },
              success: {
                iconTheme: {
                  primary: '#1FC77D',
                  secondary: '#F5F5F1',
                },
              },
              error: {
                iconTheme: {
                  primary: '#F25D5D',
                  secondary: '#F5F5F1',
                },
              },
            }}
          />
        </div>
      </body>
    </html>
  )
}
