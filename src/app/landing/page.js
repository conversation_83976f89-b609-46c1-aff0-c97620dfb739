'use client'

import { motion } from 'framer-motion'
import HeroSection from '@/components/sections/HeroSection'
import FeaturesGrid from '@/components/sections/FeaturesGrid'
import MentorshipCard from '@/components/sections/MentorshipCard'
import CTASection from '@/components/sections/CTASection'

export default function LandingPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-brand-dark-primary via-brand-dark-accent to-brand-dark-secondary">
      {/* Hero Section */}
      <HeroSection />
      
      {/* Features Grid */}
      <FeaturesGrid />
      
      {/* Mentorship Section */}
      <MentorshipCard />
      
      {/* Final CTA */}
      <CTASection />
    </div>
  )
}
