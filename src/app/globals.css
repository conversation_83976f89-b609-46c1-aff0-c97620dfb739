@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap');

@layer base {
  * {
    @apply border-gray-700;
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    @apply bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50 text-gray-900 font-sans transition-colors duration-300;
    font-feature-settings: "rlig" 1, "calt" 1;
    background-attachment: fixed;
    /* Subtle grain texture overlay */
    background-image:
      radial-gradient(circle at 1px 1px, rgba(0,0,0,0.02) 1px, transparent 0);
    background-size: 20px 20px;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    @apply w-2;
  }

  ::-webkit-scrollbar-track {
    background: rgba(243, 244, 246, 0.8);
  }

  ::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #3b82f6, #2563eb);
    border-radius: 10px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #2563eb, #1d4ed8);
  }

  /* Selection styles */
  ::selection {
    background: rgba(59, 130, 246, 0.3);
    color: #1f2937;
  }
}

@layer components {
  /* Premium Glass morphism effect */
  .glass-effect {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.9);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }

  /* Enhanced glass with gradient border */
  .glass-gradient {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(12px);
    border: 1px solid rgba(59, 130, 246, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    position: relative;
  }

  /* Premium Trading Brand Colors */
  .brand-gold {
    color: #F4C46A;
  }

  .brand-emerald {
    color: #1FC77D;
  }

  .brand-red {
    color: #F25D5D;
  }

  .brand-ivory {
    color: #F5F5F1;
  }

  .brand-gray {
    color: #9CA3AF;
  }

  /* Premium Gradient Text */
  .gradient-text {
    background: linear-gradient(135deg, #F4C46A 0%, #1FC77D 50%, #F5F5F1 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Glow Button Effect */
  .glow-button {
    background: linear-gradient(135deg, #1FC77D, #16a34a);
    box-shadow: 0 0 20px rgba(31, 199, 125, 0.3);
    transition: all 0.3s ease;
  }

  .glow-button:hover {
    transform: scale(1.05);
    box-shadow: 0 0 30px rgba(31, 199, 125, 0.5);
  }

  /* Gold Accent Button */
  .gold-button {
    background: linear-gradient(135deg, #F4C46A, #d97706);
    color: #0A0F0F;
    box-shadow: 0 0 15px rgba(244, 196, 106, 0.3);
    transition: all 0.3s ease;
  }

  .gold-button:hover {
    transform: scale(1.05);
    box-shadow: 0 0 25px rgba(244, 196, 106, 0.5);
  }

  /* Premium Animations */
  @keyframes breathe {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  @keyframes glow-pulse {
    0%, 100% { box-shadow: 0 0 20px rgba(31, 199, 125, 0.3); }
    50% { box-shadow: 0 0 30px rgba(31, 199, 125, 0.6); }
  }

  @keyframes slide-up {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-breathe {
    animation: breathe 3s ease-in-out infinite;
  }

  .animate-float {
    animation: float 4s ease-in-out infinite;
  }

  .animate-glow-pulse {
    animation: glow-pulse 2s ease-in-out infinite;
  }

  .animate-slide-up {
    animation: slide-up 0.6s ease-out;
  }

  /* Hover Effects */
  .hover-lift {
    transition: all 0.3s ease;
  }

  .hover-lift:hover {
    transform: translateY(-5px);
  }

  /* Scroll Reveal Animation */
  .scroll-reveal {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease-out;
  }

  .scroll-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
  }

  .glass-gradient::before {
    content: '';
    position: absolute;
    inset: 0;
    padding: 1px;
    background: linear-gradient(135deg, rgba(255,255,255,0.3), rgba(255,255,255,0.1));
    border-radius: inherit;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: xor;
    -webkit-mask-composite: xor;
  }

  /* Neumorphism effect */
  .neumorphic {
    @apply bg-gray-100 dark:bg-dark-900;
    box-shadow:
      8px 8px 16px rgba(209, 213, 219, 0.3),
      -8px -8px 16px rgba(255, 255, 255, 0.7);
  }

  .dark .neumorphic {
    box-shadow:
      8px 8px 16px rgba(15, 23, 42, 0.5),
      -8px -8px 16px rgba(30, 41, 59, 0.3);
  }

  /* Gradient text */
  .gradient-text {
    @apply bg-gradient-to-r from-primary-600 via-primary-500 to-accent-500 bg-clip-text text-transparent;
  }

  /* Custom range slider */
  .slider {
    @apply appearance-none bg-gray-200 dark:bg-dark-700 rounded-lg h-2 outline-none;
  }

  .slider::-webkit-slider-thumb {
    @apply appearance-none w-5 h-5 bg-gradient-to-r from-primary-500 to-primary-600 rounded-full cursor-pointer shadow-lg;
  }

  .slider::-moz-range-thumb {
    @apply w-5 h-5 bg-gradient-to-r from-primary-500 to-primary-600 rounded-full cursor-pointer border-0 shadow-lg;
  }

  /* Loading shimmer effect */
  .shimmer {
    @apply relative overflow-hidden;
  }

  .shimmer::before {
    @apply absolute inset-0 bg-gradient-to-r from-transparent via-white/60 to-transparent;
    content: '';
    animation: shimmer 2s infinite;
  }

  .dark .shimmer::before {
    @apply via-gray-700/60;
  }

  /* Floating elements */
  .float {
    animation: float 3s ease-in-out infinite;
  }

  /* Glow effects */
  .glow-primary {
    @apply shadow-lg;
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }

  .glow-success {
    @apply shadow-lg;
    box-shadow: 0 0 20px rgba(34, 197, 94, 0.3);
  }

  .glow-warning {
    @apply shadow-lg;
    box-shadow: 0 0 20px rgba(245, 158, 11, 0.3);
  }

  .glow-danger {
    @apply shadow-lg;
    box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
  }

  /* Text clamp utilities */
  .line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

@layer utilities {
  /* Text shadows */
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  /* Custom animations */
  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-bounce-slow {
    animation: bounce 2s infinite;
  }

  .animate-wiggle {
    animation: wiggle 1s ease-in-out infinite;
  }

  /* Backdrop filters */
  .backdrop-blur-xs {
    backdrop-filter: blur(2px);
  }

  /* Custom transforms */
  .transform-gpu {
    transform: translateZ(0);
  }

  /* Safe area padding for mobile */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* Focus styles */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:focus:ring-offset-dark-800;
  }

  /* Smooth transitions */
  .transition-smooth {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Hide scrollbar but keep functionality */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Enhanced hover effects */
  .hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  }

  /* Gradient animations */
  .gradient-shift {
    background-size: 200% 200%;
    animation: gradientShift 4s ease infinite;
  }

  /* Pulse glow effect */
  .pulse-glow {
    animation: pulseGlow 2s ease-in-out infinite alternate;
  }
}

/* Enhanced Keyframe Animations */
@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes wiggle {
  0%, 100% { transform: rotate(0deg); }
  25% { transform: rotate(-3deg); }
  75% { transform: rotate(3deg); }
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes pulseGlow {
  0% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.3); }
  100% { box-shadow: 0 0 30px rgba(59, 130, 246, 0.6); }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
