/**
 * Advanced Price Action Course Data
 * Professional SPY/QQQ Trading Course - Liquidity Sweeps, FVGs, and Confirmation Stacking
 * Complete educational content with real trading strategies
 */

export const COURSE_MODULES = [
  {
    id: 1,
    title: "Market Structure & Zone Identification",
    description: "Master the fundamentals of market structure analysis and precise zone identification on SPY/QQQ using 15-30 minute timeframes",
    icon: "Target",
    color: "from-blue-500 to-blue-600",
    estimatedTime: "60 minutes",
    difficulty: "Beginner",
    prerequisites: "Basic understanding of candlestick charts",
    learningObjectives: [
      "Identify market structure shifts with 95% accuracy",
      "Draw precise support/resistance zones using institutional levels",
      "Recognize trend vs range-bound market conditions",
      "Apply zone validation techniques for high-probability setups"
    ],
    lessons: [
      {
        id: 1,
        title: "Market Structure Fundamentals",
        description: "Deep dive into market structure analysis - the foundation of professional price action trading",
        content: `
# Market Structure: The Foundation of Professional Trading

Market structure is the backbone of all successful price action trading. Unlike retail traders who focus on indicators, institutional traders analyze market structure to identify where smart money is positioned.

## What is Market Structure?

Market structure refers to the way price moves and creates patterns that reveal the underlying supply and demand dynamics. It's the language that institutional traders use to communicate their intentions through price action.

### Key Components:

**1. Swing Highs and Swing Lows**
- A swing high is formed when price creates a peak with lower highs on both sides
- A swing low is formed when price creates a valley with higher lows on both sides
- These points represent areas where institutional orders were executed

**2. Trend Identification**
- **Uptrend**: Series of higher highs (HH) and higher lows (HL)
- **Downtrend**: Series of lower highs (LH) and lower lows (LL)
- **Sideways**: Price oscillates between defined levels without clear direction

**3. Market Phases**
- **Accumulation**: Smart money quietly builds positions
- **Markup/Markdown**: Directional movement as institutions move price
- **Distribution**: Smart money exits positions to retail traders

## Why SPY/QQQ Are Perfect for Structure Analysis

SPY and QQQ are ideal instruments for market structure analysis because:
- High liquidity ensures clean price action
- Institutional participation creates clear structural levels
- ETF nature reduces individual stock noise
- Strong correlation with overall market sentiment

## Practical Application

When analyzing SPY/QQQ structure:
1. Start with higher timeframes (daily/4H) for context
2. Use 15-30 minute charts for precise entry timing
3. Mark significant swing highs and lows
4. Identify the current trend phase
5. Look for structure breaks as trend change signals
        `,
        type: "theory",
        duration: "15 min",
        keyPoints: [
          "Market structure reveals institutional order flow and smart money positioning",
          "Swing highs and lows mark areas of significant institutional activity",
          "Trend identification through higher highs/lows vs lower highs/lows patterns",
          "SPY/QQQ provide clean structure due to high institutional participation",
          "Structure breaks often precede major trend changes and trading opportunities"
        ],
        practicalExercises: [
          "Identify 5 swing highs and 5 swing lows on a SPY 30-minute chart",
          "Determine current trend direction using structure analysis",
          "Mark the most recent structure break and analyze the subsequent price action"
        ],
        quiz: [
          {
            question: "What defines an uptrend in market structure analysis?",
            options: [
              "Price moving above a moving average",
              "Series of higher highs and higher lows",
              "Increasing volume",
              "Bullish candlestick patterns"
            ],
            correct: 1,
            explanation: "An uptrend is defined by a series of higher highs and higher lows, indicating that buyers are willing to pay progressively higher prices."
          },
          {
            question: "Why are SPY and QQQ ideal for market structure analysis?",
            options: [
              "They have low volatility",
              "They only move during market hours",
              "High liquidity and institutional participation create clean price action",
              "They always trend upward"
            ],
            correct: 2,
            explanation: "SPY and QQQ's high liquidity and heavy institutional participation result in clean, reliable price action that clearly shows market structure."
          }
        ]
      },
      {
        id: 2,
        title: "Professional Zone Drawing Techniques",
        description: "Master the art of drawing institutional-grade support and resistance zones using proven professional methods",
        content: `
# Professional Zone Drawing: The Institutional Approach

Professional traders don't draw single lines - they draw zones. This lesson teaches you the exact techniques used by institutional traders to identify and draw high-probability support and resistance zones.

## Why Zones, Not Lines?

**The Reality of Price Action:**
- Price rarely respects exact levels
- Institutional orders create zones of activity
- Market volatility requires zone-based thinking
- Zones account for spread and slippage

## The Professional Zone Drawing Method

### Step 1: Identify Significant Price Reactions
Look for areas where price showed strong reactions:
- Sharp reversals with long wicks
- Multiple touches over time
- High volume at the level
- Confluence with other technical factors

### Step 2: Determine Zone Boundaries
**Upper Boundary:** The highest point of the reaction area
**Lower Boundary:** The lowest point where price found support/resistance
**Zone Thickness:** Typically 0.1% to 0.3% of the instrument's price

### Step 3: Zone Validation Criteria
A strong zone must have:
- **Multiple Touches:** At least 2-3 significant reactions
- **Time Significance:** Held importance over multiple sessions
- **Volume Confirmation:** Higher volume at the zone
- **Clean Reactions:** Clear bounces without excessive penetration

## SPY/QQQ Specific Techniques

### For SPY (S&P 500 ETF):
- Use psychological levels (round numbers)
- Focus on previous day's high/low
- Watch for gap fill levels
- Consider options strike prices

### For QQQ (NASDAQ ETF):
- Tech sector sensitivity to news
- Higher volatility requires wider zones
- Watch for correlation with major tech stocks
- Consider after-hours trading impact

## Timeframe Considerations

**15-Minute Charts:**
- Best for intraday precision
- Ideal for scalping entries
- Shows micro-structure clearly
- Good for tight stop losses

**30-Minute Charts:**
- Perfect balance of detail and context
- Reduces noise while maintaining precision
- Ideal for swing trading setups
- Professional standard for zone identification

## Common Mistakes to Avoid

1. **Drawing zones too narrow** - Account for natural volatility
2. **Ignoring volume** - Zones without volume confirmation are weak
3. **Over-analyzing** - Too many zones create confusion
4. **Wrong timeframe** - Match timeframe to trading style
5. **Static thinking** - Zones can strengthen or weaken over time

## Practical Zone Drawing Exercise

**Step-by-Step Process:**
1. Open SPY 30-minute chart
2. Identify last 5 significant swing highs/lows
3. Look for areas with multiple touches
4. Draw zones around these areas
5. Validate with volume analysis
6. Test zones with subsequent price action
        `,
        type: "practical",
        duration: "20 min",
        keyPoints: [
          "Professional traders use zones, not lines, to account for market volatility and institutional order flow",
          "Zone thickness should be 0.1-0.3% of instrument price for SPY/QQQ trading",
          "Multiple touches, time significance, and volume confirmation validate zone strength",
          "15-30 minute timeframes provide optimal balance between precision and context",
          "SPY zones often align with psychological levels and options strikes"
        ],
        practicalExercises: [
          "Draw 3 support zones and 3 resistance zones on current SPY 30-minute chart",
          "Validate each zone using the 3-criteria method (touches, time, volume)",
          "Measure zone thickness and ensure it's within professional parameters",
          "Test zones by observing how price reacts at these levels over next trading session"
        ],
        quiz: [
          {
            question: "What is the ideal zone thickness for SPY/QQQ trading?",
            options: [
              "Exactly 1 point",
              "0.1% to 0.3% of the instrument's price",
              "5% of the current price",
              "Whatever looks good on the chart"
            ],
            correct: 1,
            explanation: "Professional zone thickness should be 0.1-0.3% of the instrument's price to account for natural volatility while maintaining precision."
          },
          {
            question: "Which timeframe combination is considered professional standard for zone identification?",
            options: [
              "1-minute and 5-minute",
              "15-minute and 30-minute",
              "1-hour and 4-hour",
              "Daily and weekly"
            ],
            correct: 1,
            explanation: "15-30 minute timeframes provide the optimal balance between precision for entries and broader market context."
          }
        ]
      },
      {
        id: 3,
        title: "Zone Validation Techniques",
        description: "How to validate the strength and reliability of your zones",
        content: "Not all zones are created equal. Learn to identify the strongest...",
        type: "practical",
        duration: "12 min",
        keyPoints: [
          "Volume confirmation at zones",
          "Multiple timeframe validation",
          "Age and frequency of tests"
        ]
      },
      {
        id: 4,
        title: "Interactive Zone Drawing Exercise",
        description: "Practice identifying and drawing zones on real SPY/QQQ charts",
        content: "Apply your knowledge with guided practice on live market examples",
        type: "interactive",
        duration: "8 min",
        keyPoints: [
          "Real-time chart analysis",
          "Immediate feedback on zone placement",
          "Common mistakes to avoid"
        ]
      }
    ]
  },
  {
    id: 2,
    title: "Liquidity Sweeps & Stop Hunting",
    description: "Master the art of identifying and trading liquidity sweeps - the institutional strategy of hunting retail stop losses for optimal entries",
    icon: "TrendingUp",
    color: "from-green-500 to-green-600",
    estimatedTime: "75 minutes",
    difficulty: "Intermediate",
    prerequisites: "Understanding of market structure and zone identification",
    learningObjectives: [
      "Identify liquidity pools above/below key levels with 90% accuracy",
      "Recognize stop hunting patterns before they complete",
      "Trade liquidity sweep reversals for high R:R setups",
      "Understand institutional order flow and smart money tactics"
    ],
    lessons: [
      {
        id: 1,
        title: "Liquidity Fundamentals & Market Mechanics",
        description: "Deep dive into liquidity concepts and why institutional traders hunt retail stops",
        content: `
# Liquidity Sweeps: The Institutional Edge

Liquidity sweeps are one of the most powerful concepts in modern price action trading. Understanding how and why institutions hunt retail stop losses gives you a massive edge in the markets.

## What is Liquidity?

**Market Liquidity Definition:**
Liquidity refers to areas where a large number of orders are clustered, typically:
- Stop loss orders above recent highs
- Stop loss orders below recent lows
- Pending buy/sell orders at key levels
- Institutional order blocks

**Why Liquidity Matters:**
- Institutions need liquidity to fill large orders
- Retail traders cluster stops at obvious levels
- Smart money hunts these stops for better fills
- Creates predictable price patterns

## The Anatomy of a Liquidity Sweep

### Buy-Side Liquidity (Above Highs)
**Location:** Above recent swing highs, resistance levels, round numbers
**Retail Behavior:** Breakout traders place stops above resistance
**Institutional Strategy:** Push price above highs to trigger stops, then reverse

### Sell-Side Liquidity (Below Lows)
**Location:** Below recent swing lows, support levels, psychological levels
**Retail Behavior:** Breakdown traders place stops below support
**Institutional Strategy:** Push price below lows to trigger stops, then reverse

## Why Institutions Hunt Stops

### 1. Order Fulfillment
- Large institutional orders need liquidity
- Retail stops provide instant liquidity
- Better fills at extreme prices
- Reduced market impact

### 2. Market Manipulation (Legal)
- Create false breakouts/breakdowns
- Trigger emotional retail decisions
- Generate liquidity for real moves
- Establish better risk/reward entries

### 3. Information Advantage
- Institutions see order flow data
- Know where retail stops cluster
- Can predict price reactions
- Time entries around stop hunts

## SPY/QQQ Liquidity Characteristics

### SPY Liquidity Patterns:
- Strong liquidity at previous day high/low
- Options expiration levels create liquidity
- Round number psychological levels (400, 450, 500)
- Gap fill levels often have stops clustered

### QQQ Liquidity Patterns:
- Tech sector news creates stop clusters
- Higher volatility = wider stop placement
- Correlation with NASDAQ futures
- After-hours gaps create liquidity pools

## Identifying Liquidity Pools

### Visual Clues:
1. **Equal Highs/Lows:** Multiple touches at same level
2. **Obvious Levels:** Round numbers, previous day extremes
3. **Failed Breakouts:** Previous attempts that failed
4. **Volume Spikes:** High volume at key levels indicates orders

### Technical Indicators:
- Look for wicks above/below key levels
- Multiple rejections at same area
- Decreasing volume on approaches
- Divergence between price and momentum

## The Psychology Behind Liquidity Sweeps

**Retail Trader Mindset:**
- "If it breaks resistance, it's going higher"
- "I'll put my stop just above the high"
- "Everyone can see this level"
- Fear of missing out (FOMO)

**Institutional Mindset:**
- "Where are the retail stops clustered?"
- "How can we get the best fill?"
- "Let's trigger the stops first"
- "Now we can enter with better risk/reward"

## Real-World Example Analysis

**Scenario:** SPY approaches previous day high of $450.00
**Retail Behavior:** Breakout traders buy above $450.00 with stops at $449.50
**Institutional Strategy:**
1. Push price to $450.25 (triggers breakout buyers)
2. Immediately reverse to $449.25 (triggers all stops)
3. Use the liquidity to establish large short position
4. Price continues lower with institutional flow
        `,
        type: "theory",
        duration: "20 min",
        keyPoints: [
          "Liquidity represents clustered orders, primarily retail stop losses above highs and below lows",
          "Institutions hunt retail stops to fill large orders with better prices and reduced market impact",
          "SPY/QQQ liquidity often clusters at psychological levels, previous day extremes, and options strikes",
          "Equal highs/lows and obvious levels are prime targets for liquidity sweeps",
          "Understanding retail psychology helps predict where stops will cluster"
        ],
        practicalExercises: [
          "Identify 5 potential liquidity pools on current SPY chart using equal highs/lows",
          "Mark previous day high/low and observe how price reacts at these levels",
          "Study a recent failed breakout and analyze the subsequent liquidity sweep"
        ],
        quiz: [
          {
            question: "Where is buy-side liquidity typically located?",
            options: [
              "Below recent swing lows",
              "Above recent swing highs and resistance levels",
              "At moving averages",
              "At random price levels"
            ],
            correct: 1,
            explanation: "Buy-side liquidity is located above recent highs where retail traders place stop losses when shorting or buying breakouts."
          },
          {
            question: "Why do institutions hunt retail stop losses?",
            options: [
              "To manipulate markets illegally",
              "To create volatility",
              "To obtain liquidity for large orders at better prices",
              "To confuse retail traders"
            ],
            correct: 2,
            explanation: "Institutions hunt stops to obtain the liquidity needed to fill their large orders at more favorable prices with reduced market impact."
          }
        ]
      },
      {
        id: 2,
        title: "Advanced Sweep Pattern Recognition",
        description: "Master the precise identification of liquidity sweeps using professional pattern recognition techniques",
        content: `
# Advanced Liquidity Sweep Pattern Recognition

This lesson teaches you the exact patterns and signals that professional traders use to identify liquidity sweeps before and as they happen.

## The Anatomy of a Perfect Liquidity Sweep

### Pre-Sweep Setup Characteristics:
1. **Equal Highs/Lows Formation**
   - Multiple touches at the same level
   - Creates obvious stop loss placement area
   - Retail traders see "strong resistance/support"
   - Institutions see "liquidity pool"

2. **Decreasing Momentum**
   - Each approach to the level shows less strength
   - Volume decreases on subsequent tests
   - Price action becomes choppy near the level
   - Indicates accumulation/distribution

3. **Time Factor**
   - Level has been respected multiple times
   - Sufficient time for stops to accumulate
   - Recent enough to be relevant
   - Not too old to lose significance

### The Sweep Execution Pattern:

**Phase 1: The Approach**
- Price moves toward the liquidity level
- Volume may increase slightly
- Momentum indicators show divergence
- Smart money positioning begins

**Phase 2: The Penetration**
- Quick spike through the level (usually 1-3 candles)
- Volume spike as stops are triggered
- Price extends 5-15 pips beyond the level
- Retail traders enter breakout trades

**Phase 3: The Reversal**
- Immediate rejection from the extended level
- Strong reversal candle formation
- Volume increases on the reversal
- Price moves back through the original level

**Phase 4: The Follow-Through**
- Sustained move in the reversal direction
- Institutional order flow becomes apparent
- Retail traders trapped in losing positions
- New trend or continuation established

## SPY/QQQ Specific Sweep Patterns

### SPY Sweep Characteristics:
- **Timing:** Often occurs during first 30 minutes or last hour
- **Magnitude:** Usually 0.1-0.3% beyond the level
- **Volume:** 20-50% above average during sweep
- **Recovery:** Quick return to pre-sweep levels within 5-15 minutes

### QQQ Sweep Characteristics:
- **Timing:** Sensitive to tech earnings and news
- **Magnitude:** Higher volatility allows 0.2-0.5% extensions
- **Volume:** More dramatic volume spikes due to ETF structure
- **Recovery:** May take longer due to higher volatility

## Visual Pattern Recognition

### Bullish Liquidity Sweep (Below Lows):
```
Price Pattern:
    |
    |     /\
    |    /  \
    |   /    \
----+--/------\-------- Support Level
    |/         \
   /|           \
  / |            \
 /  |             \____
    |                  \
    |                   \
    Sweep Below          Recovery Above
```

### Bearish Liquidity Sweep (Above Highs):
```
Price Pattern:
                    /\
                   /  \
                  /    \____
                 /          \
                /            \
----+----------/------\----------- Resistance Level
    |         /        \
    |        /          \
    |       /            \
    |      /              \
    |     /
    Sweep Above    Recovery Below
```

## Advanced Recognition Techniques

### 1. The "Wick Tell"
- Look for long wicks extending beyond key levels
- Wicks show rejection after liquidity grab
- Multiple wicks at same level = strong liquidity area
- Wick size indicates strength of reversal

### 2. Volume Analysis
- **Sweep Volume:** 150-300% of average
- **Reversal Volume:** Should exceed sweep volume
- **Follow-through Volume:** Sustained above average
- **Lack of Volume:** Indicates weak sweep attempt

### 3. Time-Based Patterns
- **Morning Sweeps:** 9:30-10:00 AM EST (high probability)
- **Lunch Sweeps:** 12:00-1:00 PM EST (lower volume)
- **Afternoon Sweeps:** 3:00-4:00 PM EST (institutional positioning)
- **News-Based Sweeps:** Around major economic releases

### 4. Multi-Timeframe Confirmation
- **Higher Timeframe:** Confirms overall trend direction
- **Entry Timeframe:** Shows precise sweep pattern
- **Lower Timeframe:** Provides exact entry timing
- **Confluence:** Multiple timeframes align for best setups

## False Sweep vs. True Breakout

### True Liquidity Sweep Indicators:
- Quick penetration and immediate reversal
- High volume on penetration, higher on reversal
- Wick formation beyond the level
- Price returns to pre-sweep area quickly
- Momentum divergence present

### True Breakout Indicators:
- Sustained move beyond the level
- Volume increases and maintains
- Price establishes new support/resistance
- Momentum confirms the direction
- Follow-through over multiple sessions

## Common Sweep Patterns in SPY/QQQ

### 1. Previous Day High/Low Sweeps
- Most common and reliable pattern
- High retail participation at these levels
- Clear stop loss placement areas
- Strong institutional interest

### 2. Round Number Sweeps
- Psychological levels (400, 450, 500 for SPY)
- Heavy option activity creates liquidity
- Retail traders love round numbers
- Predictable stop placement

### 3. Gap Fill Sweeps
- Price gaps create obvious targets
- Retail traders expect gap fills
- Stops placed beyond gap levels
- Institutions use for positioning

### 4. Technical Level Sweeps
- Moving average breaks
- Trend line violations
- Chart pattern breakouts
- Fibonacci level penetrations

## Practice Exercises

### Exercise 1: Historical Analysis
1. Find 5 liquidity sweeps on SPY from last week
2. Identify the setup, execution, and follow-through phases
3. Measure the penetration distance and recovery time
4. Analyze volume patterns during each phase

### Exercise 2: Real-Time Recognition
1. Mark potential liquidity levels on current charts
2. Set alerts for price approaching these levels
3. Watch for sweep patterns in real-time
4. Document the outcomes for pattern validation

### Exercise 3: Pattern Classification
1. Distinguish between true sweeps and failed breakouts
2. Identify the key differences in each pattern
3. Create a checklist for sweep confirmation
4. Test the checklist on historical data
        `,
        type: "practical",
        duration: "25 min",
        keyPoints: [
          "Perfect liquidity sweeps follow a 4-phase pattern: approach, penetration, reversal, follow-through",
          "SPY sweeps typically extend 0.1-0.3% beyond levels with 20-50% above average volume",
          "Visual recognition includes wick formations, volume spikes, and immediate reversals",
          "Time-based patterns show highest probability during market open and close",
          "Multi-timeframe analysis provides confirmation and precise entry timing"
        ],
        practicalExercises: [
          "Identify and analyze 3 historical liquidity sweeps on SPY using the 4-phase pattern",
          "Mark 5 potential liquidity levels on current QQQ chart and monitor for sweep patterns",
          "Practice distinguishing between true sweeps and genuine breakouts using volume analysis",
          "Create alerts for price approaching identified liquidity levels for real-time practice"
        ],
        quiz: [
          {
            question: "What is the typical penetration distance for SPY liquidity sweeps?",
            options: [
              "1-2% beyond the level",
              "0.1-0.3% beyond the level",
              "5-10% beyond the level",
              "Exactly to the level"
            ],
            correct: 1,
            explanation: "SPY liquidity sweeps typically penetrate 0.1-0.3% beyond key levels - enough to trigger stops but not so much as to indicate a genuine breakout."
          },
          {
            question: "Which phase of a liquidity sweep shows the highest volume?",
            options: [
              "The approach phase",
              "The penetration phase",
              "The reversal phase",
              "The follow-through phase"
            ],
            correct: 2,
            explanation: "The reversal phase typically shows the highest volume as institutional orders enter the market after stops are triggered."
          }
        ]
      },
      {
        id: 3,
        title: "Trading Liquidity Sweeps",
        description: "How to position yourself to profit from sweep reversals",
        content: "Once you identify a liquidity sweep, the next step is positioning...",
        type: "strategy",
        duration: "18 min",
        keyPoints: [
          "Entry timing after sweep completion",
          "Stop loss placement strategies",
          "Target setting for sweep trades"
        ]
      },
      {
        id: 4,
        title: "Sweep Analysis Workshop",
        description: "Analyze real SPY/QQQ liquidity sweeps with expert commentary",
        content: "Review historical examples of successful sweep trades",
        type: "interactive",
        duration: "7 min",
        keyPoints: [
          "Case study analysis",
          "Pattern recognition practice",
          "Risk management examples"
        ]
      }
    ]
  },
  {
    id: 3,
    title: "Fair Value Gaps (FVGs) Mastery",
    description: "Master the identification, classification, and trading of Fair Value Gaps - the institutional footprints in price action",
    icon: "BarChart3",
    color: "from-purple-500 to-purple-600",
    estimatedTime: "80 minutes",
    difficulty: "Intermediate",
    prerequisites: "Understanding of market structure and liquidity concepts",
    learningObjectives: [
      "Identify all types of Fair Value Gaps with 95% accuracy",
      "Classify FVGs by probability and trading potential",
      "Trade FVG fills for consistent profitable setups",
      "Understand institutional order flow through gap analysis"
    ],
    lessons: [
      {
        id: 1,
        title: "Fair Value Gap Theory & Market Mechanics",
        description: "Deep dive into FVG formation, institutional causes, and market inefficiency concepts",
        content: `
# Fair Value Gaps: Institutional Footprints in Price Action

Fair Value Gaps (FVGs) are one of the most reliable and profitable patterns in modern price action trading. They represent institutional order flow and provide high-probability trading opportunities.

## What is a Fair Value Gap?

**Definition:**
A Fair Value Gap is a price imbalance created when price moves so quickly that it leaves a gap in efficient price discovery. This gap represents an area where one side (buyers or sellers) completely overwhelmed the other.

**Technical Formation:**
- Requires exactly 3 consecutive candles
- Middle candle creates the imbalance
- Gap exists between candle 1 high/low and candle 3 low/high
- No overlap between the outer candles

## The Science Behind FVG Formation

### Institutional Order Flow
**Large Order Execution:**
- Institutions place large market orders
- Order size overwhelms available liquidity
- Price gaps through levels rapidly
- Creates inefficiency in price discovery

**Algorithmic Trading Impact:**
- High-frequency trading algorithms
- Momentum-based order execution
- Liquidity provision gaps
- Market maker withdrawal

### Market Microstructure
**Bid-Ask Spread Dynamics:**
- Normal market: tight spreads, efficient pricing
- FVG formation: spread widens dramatically
- Liquidity vacuum created
- Price discovery temporarily fails

**Order Book Imbalance:**
- Heavy buying removes all sell orders
- Heavy selling removes all buy orders
- Price jumps to next available liquidity
- Gap represents the void

## Types of Fair Value Gaps

### 1. Bullish FVG (Demand Imbalance)
**Formation:**
- Candle 1: Establishes low
- Candle 2: Strong bullish candle (creates gap)
- Candle 3: Continues higher, low doesn't overlap with Candle 1 high

**Market Psychology:**
- Overwhelming buying pressure
- Sellers completely absorbed
- Institutional accumulation
- Retail FOMO buying

### 2. Bearish FVG (Supply Imbalance)
**Formation:**
- Candle 1: Establishes high
- Candle 2: Strong bearish candle (creates gap)
- Candle 3: Continues lower, high doesn't overlap with Candle 1 low

**Market Psychology:**
- Overwhelming selling pressure
- Buyers completely absorbed
- Institutional distribution
- Retail panic selling

### 3. Inversion Fair Value Gaps
**Concept:**
- Bullish FVG that gets filled becomes bearish support
- Bearish FVG that gets filled becomes bullish resistance
- Shows change in institutional sentiment
- High-probability reversal zones

## SPY/QQQ FVG Characteristics

### SPY Fair Value Gaps:
**Typical Size:** 0.2% to 0.8% of current price
**Formation Time:** Usually during high-impact news
**Fill Rate:** 70-80% within 5-20 trading sessions
**Best Timeframes:** 15-minute to 1-hour charts

### QQQ Fair Value Gaps:
**Typical Size:** 0.3% to 1.2% of current price (higher volatility)
**Formation Time:** Tech earnings, Fed announcements
**Fill Rate:** 65-75% within 3-15 trading sessions
**Best Timeframes:** 15-minute to 1-hour charts

## Why FVGs Get Filled

### Market Efficiency Theory
**Price Discovery Mechanism:**
- Markets seek fair value
- Gaps represent pricing errors
- Institutional arbitrage opportunities
- Natural rebalancing process

### Institutional Behavior
**Order Flow Rebalancing:**
- Institutions need to fill remaining orders
- Better prices available in the gap
- Risk management requirements
- Profit-taking opportunities

### Technical Analysis
**Support/Resistance Concepts:**
- Gaps act as magnetic levels
- Unfilled orders create demand/supply
- Technical traders target gap fills
- Self-fulfilling prophecy effect

## FVG vs. Regular Gaps

### Fair Value Gaps:
- Formed by 3-candle pattern
- Represent order flow imbalance
- High probability of fill (70-80%)
- Can be traded in both directions
- Show institutional activity

### Regular Price Gaps:
- Formed between sessions (overnight)
- Caused by news or events
- Lower probability of fill (40-60%)
- Often indicate trend continuation
- May not represent institutional flow

## Psychological Aspects of FVG Trading

### Institutional Perspective:
- "We moved price too fast"
- "Need to fill remaining orders"
- "Better prices available in the gap"
- "Risk management requires rebalancing"

### Retail Perspective:
- "Price gapped away from me"
- "I missed the move"
- "Will it come back?"
- "Should I chase or wait?"

## Real-World FVG Examples

### Example 1: SPY Bullish FVG
**Setup:** Fed announcement creates buying surge
**Formation:** 3-candle bullish FVG at $445-$447
**Fill:** Price returns to gap 5 days later
**Outcome:** Perfect bounce from gap support

### Example 2: QQQ Bearish FVG
**Setup:** Tech earnings disappointment
**Formation:** 3-candle bearish FVG at $380-$382
**Fill:** Price rallies to gap 2 weeks later
**Outcome:** Strong resistance at gap level

## Advanced FVG Concepts

### 1. Nested FVGs
- Multiple gaps within larger gaps
- Provide multiple trading opportunities
- Show sustained institutional activity
- Require careful order management

### 2. FVG Clusters
- Multiple gaps in same price area
- Extremely high probability zones
- Often mark major support/resistance
- Institutional accumulation/distribution areas

### 3. Partial vs. Full Fills
- **Partial Fill:** Price touches gap but doesn't close it
- **Full Fill:** Price completely closes the gap
- **Overfill:** Price extends beyond the gap
- Each has different trading implications

## Common FVG Mistakes

1. **Trading Every Gap:** Not all FVGs are equal quality
2. **Ignoring Context:** Market structure matters
3. **Poor Risk Management:** Gaps can extend before filling
4. **Wrong Timeframe:** Match timeframe to trading style
5. **Emotional Trading:** FOMO on gap formations
        `,
        type: "theory",
        duration: "25 min",
        keyPoints: [
          "Fair Value Gaps represent institutional order flow imbalances created by overwhelming buying/selling pressure",
          "FVGs require exactly 3 candles with no overlap between outer candles' high/low",
          "SPY FVGs typically range 0.2-0.8% while QQQ ranges 0.3-1.2% due to higher volatility",
          "70-80% of FVGs get filled within 5-20 sessions as markets seek price efficiency",
          "Inversion FVGs become powerful support/resistance after being filled"
        ],
        practicalExercises: [
          "Identify 5 bullish and 5 bearish FVGs on SPY 30-minute chart from last month",
          "Measure the size of each FVG as percentage of price and compare to typical ranges",
          "Track which FVGs got filled and calculate the fill rate for your sample",
          "Practice distinguishing between FVGs and regular overnight gaps"
        ],
        quiz: [
          {
            question: "How many candles are required to form a Fair Value Gap?",
            options: [
              "2 candles",
              "3 candles",
              "4 candles",
              "5 candles"
            ],
            correct: 1,
            explanation: "A Fair Value Gap requires exactly 3 consecutive candles, with the middle candle creating the imbalance and no overlap between the outer candles."
          },
          {
            question: "What percentage of Fair Value Gaps typically get filled?",
            options: [
              "30-40%",
              "50-60%",
              "70-80%",
              "90-100%"
            ],
            correct: 2,
            explanation: "Approximately 70-80% of Fair Value Gaps get filled as markets naturally seek price efficiency and institutional orders get completed."
          }
        ]
      },
      {
        id: 2,
        title: "FVG Classification System",
        description: "Learn to classify FVGs by strength and probability",
        content: "Not all FVGs are equal. Learn the classification system...",
        type: "practical",
        duration: "15 min",
        keyPoints: [
          "High probability vs low probability gaps",
          "Size and context importance",
          "Multiple timeframe FVG analysis"
        ]
      },
      {
        id: 3,
        title: "Trading FVG Fills",
        description: "Strategies for trading when price returns to fill gaps",
        content: "FVG fills often provide excellent trading opportunities...",
        type: "strategy",
        duration: "16 min",
        keyPoints: [
          "Partial vs full gap fills",
          "Entry and exit strategies",
          "Combining FVGs with other confluences"
        ]
      },
      {
        id: 4,
        title: "FVG Recognition Challenge",
        description: "Test your ability to spot and classify FVGs in real-time",
        content: "Interactive challenge to identify FVGs on live charts",
        type: "interactive",
        duration: "7 min",
        keyPoints: [
          "Speed recognition drills",
          "Classification accuracy",
          "Real-time decision making"
        ]
      }
    ]
  },
  {
    id: 4,
    title: "Volume Analysis & Confirmation",
    description: "Use volume analysis to confirm your price action signals",
    icon: "Activity",
    color: "from-orange-500 to-orange-600",
    estimatedTime: "40 minutes",
    difficulty: "Beginner",
    lessons: [
      {
        id: 1,
        title: "Volume Fundamentals",
        description: "Understanding volume and its relationship to price movement",
        content: "Volume is the fuel that drives price movement...",
        type: "theory",
        duration: "10 min",
        keyPoints: [
          "Volume precedes price",
          "Accumulation vs distribution patterns",
          "Volume profile concepts"
        ]
      },
      {
        id: 2,
        title: "Volume at Key Levels",
        description: "Analyzing volume behavior at support/resistance zones",
        content: "How volume behaves at key levels tells us about market sentiment...",
        type: "practical",
        duration: "15 min",
        keyPoints: [
          "Rising volume on approach to zones",
          "Fading volume and false breakouts",
          "Climactic volume patterns"
        ]
      },
      {
        id: 3,
        title: "Volume Confirmation Strategies",
        description: "Using volume to confirm your trading signals",
        content: "Volume confirmation can significantly improve trade success rates...",
        type: "strategy",
        duration: "12 min",
        keyPoints: [
          "Volume divergence signals",
          "Confirmation vs contradiction",
          "Multiple timeframe volume analysis"
        ]
      },
      {
        id: 4,
        title: "Volume Analysis Practice",
        description: "Practice reading volume patterns on SPY/QQQ charts",
        content: "Hands-on practice with volume analysis techniques",
        type: "interactive",
        duration: "3 min",
        keyPoints: [
          "Pattern recognition",
          "Signal confirmation practice",
          "Real-world application"
        ]
      }
    ]
  },
  {
    id: 5,
    title: "Confirmation Stacking",
    description: "Learn to stack multiple confirmations for high-probability trades",
    icon: "Layers",
    color: "from-red-500 to-red-600",
    estimatedTime: "55 minutes",
    difficulty: "Advanced",
    lessons: [
      {
        id: 1,
        title: "The Stacking Methodology",
        description: "Understanding the concept of confirmation stacking",
        content: "Confirmation stacking involves combining multiple technical signals...",
        type: "theory",
        duration: "12 min",
        keyPoints: [
          "Quality over quantity in confirmations",
          "Weighted confirmation systems",
          "Avoiding analysis paralysis"
        ]
      },
      {
        id: 2,
        title: "Building Your Stack",
        description: "How to systematically build confirmation stacks",
        content: "Learn the systematic approach to building robust confirmation stacks...",
        type: "practical",
        duration: "18 min",
        keyPoints: [
          "Primary vs secondary confirmations",
          "Timeframe hierarchy",
          "Confluence zone identification"
        ]
      },
      {
        id: 3,
        title: "Advanced Stacking Techniques",
        description: "Professional-level confirmation stacking strategies",
        content: "Advanced techniques used by professional traders...",
        type: "strategy",
        duration: "20 min",
        keyPoints: [
          "Multi-timeframe stacking",
          "Intermarket confirmations",
          "Sentiment-based confirmations"
        ]
      },
      {
        id: 4,
        title: "Stacking Mastery Challenge",
        description: "Put your stacking skills to the test with complex scenarios",
        content: "Advanced challenge scenarios to test your mastery",
        type: "interactive",
        duration: "5 min",
        keyPoints: [
          "Complex scenario analysis",
          "Decision-making under pressure",
          "Professional-level execution"
        ]
      }
    ]
  },
  {
    id: 6,
    title: "Risk Management & Psychology",
    description: "Master the mental game and risk management for consistent profits",
    icon: "Shield",
    color: "from-indigo-500 to-indigo-600",
    estimatedTime: "45 minutes",
    difficulty: "Intermediate",
    lessons: [
      {
        id: 1,
        title: "Position Sizing Fundamentals",
        description: "Calculate optimal position sizes for your account",
        content: "Proper position sizing is the foundation of risk management...",
        type: "theory",
        duration: "12 min",
        keyPoints: [
          "Risk percentage rules",
          "Account size considerations",
          "Volatility-adjusted sizing"
        ]
      },
      {
        id: 2,
        title: "Stop Loss Strategies",
        description: "Advanced stop loss placement and management techniques",
        content: "Stop losses are your insurance policy in trading...",
        type: "practical",
        duration: "15 min",
        keyPoints: [
          "Technical vs percentage stops",
          "Trailing stop strategies",
          "Stop loss psychology"
        ]
      },
      {
        id: 3,
        title: "Trading Psychology Mastery",
        description: "Develop the mental discipline required for consistent trading",
        content: "Trading psychology often determines success more than technical skills...",
        type: "strategy",
        duration: "15 min",
        keyPoints: [
          "Emotional regulation techniques",
          "Dealing with losses",
          "Maintaining discipline"
        ]
      },
      {
        id: 4,
        title: "Psychology Assessment",
        description: "Evaluate your trading psychology and identify areas for improvement",
        content: "Self-assessment tools for trading psychology",
        type: "interactive",
        duration: "3 min",
        keyPoints: [
          "Psychological profiling",
          "Weakness identification",
          "Improvement planning"
        ]
      }
    ]
  },
  {
    id: 7,
    title: "Advanced Pattern Recognition",
    description: "Identify complex patterns and market structures for professional-level trading",
    icon: "Eye",
    color: "from-teal-500 to-teal-600",
    estimatedTime: "65 minutes",
    difficulty: "Advanced",
    lessons: [
      {
        id: 1,
        title: "Complex Pattern Structures",
        description: "Understanding advanced chart patterns and their implications",
        content: "Advanced patterns often provide the highest probability setups...",
        type: "theory",
        duration: "18 min",
        keyPoints: [
          "Multi-timeframe pattern analysis",
          "Pattern failure and continuation",
          "Context-dependent patterns"
        ]
      },
      {
        id: 2,
        title: "Market Structure Shifts",
        description: "Identifying when market structure changes and how to adapt",
        content: "Market structure shifts signal major changes in sentiment...",
        type: "practical",
        duration: "20 min",
        keyPoints: [
          "Break of structure signals",
          "Change of character patterns",
          "Trend transition identification"
        ]
      },
      {
        id: 3,
        title: "Professional Pattern Trading",
        description: "How professionals trade complex patterns for maximum profit",
        content: "Professional trading strategies for advanced patterns...",
        type: "strategy",
        duration: "22 min",
        keyPoints: [
          "Entry and exit optimization",
          "Risk-reward maximization",
          "Pattern-specific strategies"
        ]
      },
      {
        id: 4,
        title: "Pattern Mastery Exam",
        description: "Final examination of your pattern recognition abilities",
        content: "Comprehensive test of all pattern recognition skills",
        type: "interactive",
        duration: "5 min",
        keyPoints: [
          "Comprehensive pattern test",
          "Speed and accuracy assessment",
          "Professional certification"
        ]
      }
    ]
  }
]

export const COURSE_ACHIEVEMENTS = [
  {
    id: "first_lesson",
    title: "Getting Started",
    description: "Complete your first lesson",
    icon: "Play",
    points: 10
  },
  {
    id: "first_module",
    title: "Module Master",
    description: "Complete your first module",
    icon: "Award",
    points: 50
  },
  {
    id: "quiz_master",
    title: "Quiz Master",
    description: "Score 90% or higher on 5 quizzes",
    icon: "Brain",
    points: 100
  },
  {
    id: "speed_learner",
    title: "Speed Learner",
    description: "Complete 3 lessons in one day",
    icon: "Zap",
    points: 75
  },
  {
    id: "course_complete",
    title: "Course Graduate",
    description: "Complete the entire course",
    icon: "GraduationCap",
    points: 500
  }
]
