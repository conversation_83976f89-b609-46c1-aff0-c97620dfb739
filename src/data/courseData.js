/**
 * Advanced Price Action Course Data
 * Professional SPY/QQQ Trading Course - Liquidity Sweeps, FVGs, and Confirmation Stacking
 * Complete educational content with real trading strategies
 */

export const COURSE_MODULES = [
  {
    id: 1,
    title: "Market Structure & Liquidity Fundamentals",
    description: "Master liquidity sweeps, stop hunts, and institutional market mechanics on SPY/QQQ. Learn how smart money operates and how to identify their footprints.",
    icon: "Target",
    color: "from-blue-500 to-blue-600",
    estimatedTime: "75 minutes",
    difficulty: "Beginner",
    prerequisites: "Basic understanding of candlestick charts and support/resistance",
    learningObjectives: [
      "Identify liquidity sweeps and stop hunts with 90% accuracy",
      "Understand institutional market mechanics and smart money behavior",
      "Recognize the difference between true breakouts and false breakouts",
      "Apply liquidity-based trading strategies on SPY/QQQ",
      "Master the concept of 'price seeks liquidity before direction'"
    ],
    lessons: [
      {
        id: 1,
        title: "Understanding Liquidity Sweeps & Stop Hunts",
        description: "Learn how institutional players manipulate price to grab liquidity and how to identify these moves on SPY/QQQ",
        content: `
# Liquidity Sweeps: The Institutional Edge

## What Does "Sweeping Liquidity" Mean?

In trading, a **liquidity sweep** refers to price briefly breaching a key level – often a prior session's high or low – to trigger stop orders clustered there. For example, sweeping the previous day's high/low means price pokes above the prior day's high (or below the low), activating stop-losses and pending orders in that area, before often snapping back.

This phenomenon is commonly known as a **stop hunt** or **liquidity grab**. It's not just random volatility; it's usually caused by large players exploiting those liquidity pockets.

## Why Do Institutions/Algos Do This?

Big market participants (like institutions or algorithms) need substantial liquidity to fill large orders without moving the market too much against themselves. Running price into an area with many resting orders supplies that liquidity.

In a liquidity sweep, significant players deliberately drive price through a known level to trigger clusters of orders (stops, breakout entries) – creating a surge of executions they can use to enter or exit positions.

**Key Concept**: They use the liquidity from trapped traders (the "fish") to fill their nets.

Once those orders are triggered (buy stops above highs or sell stops below lows), the market often reverses direction abruptly because the smart money has taken the opposite side of those orders.

## The Golden Rule: Price Seeks Liquidity Before Direction

Price will usually either:
- **Fake-out and reverse** after grabbing liquidity
- **Break out and continue** if genuine buying/selling pressure exists

## How to Spot a Liquidity Sweep

### 1. Wick Beyond a Key Level
Price breaks the previous session's high/low momentarily, then retreats back within the prior range. On a chart this appears as a sharp wick poking through resistance or support.

### 2. Volume Surge
Liquidity sweeps trigger a burst of trading volume. All those stop orders getting hit creates a large volume spike.

### 3. Failed Breakout Behavior
Following the initial poke through the level, momentum stalls out. Price quickly falls back below the taken high or back above the swept low.

### 4. Stop Clusters on Chart
Markets often target equal highs or equal lows (retail traders' stop placements). When these get taken out by just a small margin and reverse, you've spotted a liquidity sweep.

## Real SPY/QQQ Examples

**Morning Gap Sweep**: SPY gaps up at open, takes out yesterday's high by $0.50, then immediately reverses as institutional sellers absorb the breakout buying.

**Overnight Low Hunt**: QQQ drops in pre-market, sweeps the previous day's low, then rockets higher as smart money accumulates shares from panicked sellers.
        `,
        type: "theory",
        duration: "18 min",
        keyPoints: [
          "Liquidity sweeps are deliberate moves by institutions to grab stop orders",
          "Price seeks liquidity before establishing true direction",
          "Volume spikes and wicks are key indicators of sweeps",
          "SPY/QQQ provide excellent examples due to high institutional activity"
        ],
        practicalExercises: [
          "Identify 3 liquidity sweeps on a SPY daily chart from the past month",
          "Mark areas where stop orders likely cluster (equal highs/lows)",
          "Analyze volume patterns during suspected liquidity sweeps"
        ],
        quiz: [
          {
            question: "What is the primary purpose of a liquidity sweep?",
            options: [
              "To create volatility in the market",
              "To allow institutions to fill large orders using retail stop orders",
              "To test technical support and resistance levels",
              "To signal the start of a new trend"
            ],
            correct: 1,
            explanation: "Institutions use liquidity sweeps to fill large orders by triggering clusters of retail stop orders, providing them with the liquidity they need without moving the market against themselves."
          },
          {
            question: "Which chart pattern typically indicates a liquidity sweep has occurred?",
            options: [
              "A long wick beyond a key level with quick reversal",
              "A strong breakout with high volume continuation",
              "A gradual move through resistance with steady volume",
              "A sideways consolidation pattern"
            ],
            correct: 0,
            explanation: "A long wick (or spike) beyond a key level followed by a quick reversal is the classic signature of a liquidity sweep, showing price briefly grabbed stops before reversing."
          },
          {
            question: "According to the golden rule, what does price seek before establishing direction?",
            options: [
              "Technical confirmation",
              "Volume validation",
              "Liquidity",
              "Trend continuation"
            ],
            correct: 2,
            explanation: "The golden rule states that 'price seeks liquidity before direction' - meaning price will often move to areas where stop orders cluster before establishing its true directional bias."
          }
        ]
      },
      {
        id: 2,
        title: "Trading After Liquidity Sweeps: Reversal vs Continuation",
        description: "Learn how to distinguish between false breakouts and genuine breakouts after liquidity sweeps, and how to trade both scenarios",
        content: `
# Trading After Liquidity Sweeps: Reversal vs Continuation

Once a liquidity sweep has occurred, two scenarios are in play – a reversal (mean-reversion back into the range) or a successful breakout (continuation of the trend). Understanding which scenario is unfolding is crucial for profitable trading.

## Reversal Setup: Stop Hunt and Rejection

This is the classic outcome of a liquidity sweep. After price wicks beyond the prior high/low and falls back, your bias flips in the opposite direction of the wick.

### Identifying a Reversal Setup

**1. The Wick Formation**
- Price briefly breaks a key level (previous high/low)
- Immediately reverses back within the prior range
- Creates a long wick or spike on the chart
- Often accompanied by high volume

**2. Confirmation Signals**
- **Market Structure Break**: Look for a break of a short-term swing low (if a high was swept) or swing high (if a low was swept)
- **Candlestick Patterns**: Engulfing candles, pin bars, or doji formations at the sweep level
- **Volume Analysis**: Volume spike on the sweep followed by sustained volume on the reversal

### Trading the Reversal

**Entry Strategy:**
- Wait for confirmation before entering
- Don't trade the exact top/bottom of the sweep
- Enter on the structure break in the reversal direction

**Example: SPY High Sweep Reversal**
1. SPY rallies and takes out yesterday's high by $0.50
2. Price immediately reverses with a long upper wick
3. Wait for SPY to break below a recent swing low (confirmation)
4. Enter short position (put options) with stop above the sweep high
5. Target the opposite side of the range or previous support

## Continuation Setup: Breakout with Follow-Through

Not every push through a previous high/low is a fake-out. Sometimes the market intends to run further, and the liquidity sweep is the ignition of a larger move.

### Identifying a Continuation Setup

**1. Strong Follow-Through**
- Price holds above the broken level (for upward breakouts)
- Candles close firmly in the breakout direction
- Only shallow pullbacks that stay above the old high (now support)

**2. Volume Confirmation**
- Sustained volume on subsequent bars
- Not just a one-off stop trigger
- Continued participation showing genuine interest

### Trading the Continuation

**Entry Strategy:**
- Wait for a retest of the broken level
- Look for the old resistance to act as new support
- Enter on the bounce with stops below the retest low

**Example: QQQ Low Sweep Continuation**
1. QQQ drops and sweeps the previous day's low
2. Price quickly recovers and closes above the old low
3. Wait for a pullback to retest the broken low as support
4. Enter long position (call options) on the bounce
5. Stop below the retest low, target higher resistance

## The Professional Approach: Confirmation Stacking

Never trade a liquidity sweep in isolation. Stack multiple confirmations:

### Primary Confirmations
- **Price Action**: Structure breaks, candlestick patterns
- **Volume**: Spikes on sweeps, sustained volume on follow-through
- **Time**: How quickly the reversal/continuation occurs

### Secondary Confirmations
- **Higher Timeframe Context**: Is this aligned with the bigger picture?
- **Market Sentiment**: Risk-on vs risk-off environment
- **Options Flow**: Unusual activity in puts/calls

## Real-World Examples

### SPY Reversal Example (Bearish)
- **Setup**: SPY approaches yesterday's high at $450
- **Sweep**: Price hits $450.50, creating a wick
- **Confirmation**: 5-minute bearish engulfing + break of swing low
- **Entry**: Short at $449.50 after confirmation
- **Result**: Drop to $445 for 1% profit

### QQQ Continuation Example (Bullish)
- **Setup**: QQQ tests overnight low at $350
- **Sweep**: Brief drop to $349.80, then recovery
- **Confirmation**: Strong close above $350 + volume increase
- **Entry**: Long on retest of $350 support
- **Result**: Rally to $355 for 1.4% profit

## Key Takeaways

- Liquidity sweeps provide directional bias, not immediate entries
- Always wait for confirmation before trading
- Reversals are more common than continuations
- Stack multiple confirmations for higher probability setups
- Use proper risk management with stops beyond the sweep levels
        `,
        type: "strategy",
        duration: "22 min",
        keyPoints: [
          "Liquidity sweeps can lead to either reversals or continuations",
          "Confirmation is essential - never trade the sweep itself",
          "Reversal setups are more common than continuation setups",
          "Volume and price action provide the best confirmation signals",
          "Stack multiple confirmations for higher probability trades"
        ],
        practicalExercises: [
          "Identify 3 liquidity sweeps on SPY/QQQ charts and classify as reversal or continuation",
          "Practice waiting for confirmation signals before entering trades",
          "Analyze volume patterns during sweep reversals vs continuations",
          "Create a checklist of confirmation signals for your trading plan"
        ],
        quiz: [
          {
            question: "What should you do immediately after identifying a liquidity sweep?",
            options: [
              "Enter a trade in the opposite direction",
              "Enter a trade in the same direction",
              "Wait for confirmation signals",
              "Close all existing positions"
            ],
            correct: 2,
            explanation: "Never trade immediately after a liquidity sweep. Always wait for confirmation signals like structure breaks, volume confirmation, or candlestick patterns."
          },
          {
            question: "Which scenario is more common after a liquidity sweep?",
            options: [
              "Continuation breakouts",
              "Reversal setups",
              "Sideways consolidation",
              "Gap formations"
            ],
            correct: 1,
            explanation: "Reversal setups are more common after liquidity sweeps because most sweeps are designed to grab stops and then reverse, not to signal genuine breakouts."
          },
          {
            question: "What is the best confirmation for a liquidity sweep reversal?",
            options: [
              "A single large volume bar",
              "Price returning to the sweep level",
              "Market structure break in the opposite direction",
              "A gap in the opposite direction"
            ],
            correct: 2,
            explanation: "A market structure break (like breaking a swing low after a high sweep) provides the strongest confirmation that the reversal is genuine and not just a temporary pullback."
          }
        ]
      },
      {
        id: 3,
        title: "Zone Validation Techniques",
        description: "How to validate the strength and reliability of your zones",
        content: "Not all zones are created equal. Learn to identify the strongest...",
        type: "practical",
        duration: "12 min",
        keyPoints: [
          "Volume confirmation at zones",
          "Multiple timeframe validation",
          "Age and frequency of tests"
        ]
      },
      {
        id: 4,
        title: "Interactive Zone Drawing Exercise",
        description: "Practice identifying and drawing zones on real SPY/QQQ charts",
        content: "Apply your knowledge with guided practice on live market examples",
        type: "interactive",
        duration: "8 min",
        keyPoints: [
          "Real-time chart analysis",
          "Immediate feedback on zone placement",
          "Common mistakes to avoid"
        ]
      }
    ]
  },
  {
    id: 2,
    title: "Fair Value Gaps (FVGs) Mastery",
    description: "Master Fair Value Gaps - the institutional footprints left by rapid price movements and how to trade them for consistent profits",
    icon: "BarChart3",
    color: "from-green-500 to-green-600",
    estimatedTime: "80 minutes",
    difficulty: "Intermediate",
    prerequisites: "Understanding of liquidity sweeps and market structure",
    learningObjectives: [
      "Identify and mark Fair Value Gaps with 95% accuracy",
      "Understand the difference between HTF and LTF FVGs",
      "Trade FVG fills and rejections for high-probability setups",
      "Master Inversion Fair Value Gaps (IFVGs) for advanced entries",
      "Combine FVGs with liquidity sweeps for confluence trading"
    ],
    lessons: [
      {
        id: 1,
        title: "Fair Value Gap Theory & Formation",
        description: "Deep dive into FVG formation, institutional causes, and market inefficiency concepts",
        content: `
# Fair Value Gaps: Institutional Footprints in Price Action

A Fair Value Gap (FVG) is a price range on a chart where an inefficient move occurred – essentially, a section where little or no trading took place. These gaps represent areas where fair value may have temporarily changed, and markets tend to revert to fill these inefficiencies over time.

## What is a Fair Value Gap?

**Definition:**
A Fair Value Gap appears within a three-candle sequence where one large momentum candle creates a "void" between the wick of the first candle and the wick of the third candle. The second candle (the big move) is so large that the third candle's low (in an up move) is still above the first candle's high, leaving a gap in between.

**Key Concept:**
This gap represents a price area where fair value may have temporarily changed – price zoomed in one direction without adequate two-way trading at those levels.

## The Three-Candle Rule

### Bullish FVG Formation:
1. **Candle 1**: Creates a high at a certain level
2. **Candle 2**: Large bullish candle that gaps up significantly
3. **Candle 3**: Low is still above Candle 1's high
4. **Result**: Gap between Candle 1 high and Candle 3 low = Bullish FVG

### Bearish FVG Formation:
1. **Candle 1**: Creates a low at a certain level
2. **Candle 2**: Large bearish candle that gaps down significantly
3. **Candle 3**: High is still below Candle 1's low
4. **Result**: Gap between Candle 1 low and Candle 3 high = Bearish FVG

## Why Do FVGs Matter?

### Market Inefficiency Theory
- FVGs highlight areas where price moved too fast
- Represent zones with insufficient two-way trading
- Markets have "memory" of unfilled orders in these ranges
- Price often returns to rebalance these inefficiencies

### Institutional Perspective
- Large orders couldn't be filled during rapid moves
- Institutions may have unfilled orders in FVG zones
- Smart money often waits for price to return to these levels
- FVGs act like magnets for future price action

## Types of Fair Value Gaps

### 1. Bullish FVG (Buy Side Imbalance)
- **Formation**: Left by strong upward movement
- **Expectation**: Acts as support when price returns
- **Trading**: Look for buying opportunities on first touch
- **Invalidation**: Price closes below the gap

### 2. Bearish FVG (Sell Side Imbalance)
- **Formation**: Left by strong downward movement
- **Expectation**: Acts as resistance when price returns
- **Trading**: Look for selling opportunities on first touch
- **Invalidation**: Price closes above the gap

## SPY/QQQ FVG Characteristics

### SPY FVG Patterns:
- Often form during earnings reactions
- News-driven gaps create large FVGs
- Options expiration can trigger FVG formation
- Market open gaps frequently leave FVGs

### QQQ FVG Patterns:
- Tech sector news creates significant FVGs
- Higher volatility = larger gap formations
- After-hours trading often leaves gaps
- Correlation with NASDAQ futures gaps

## FVG Validation Criteria

### Strong FVGs Have:
1. **Clean Formation**: Clear three-candle pattern
2. **Significant Size**: Gap represents meaningful price range
3. **Volume Context**: High volume on the gap-creating candle
4. **Timeframe Relevance**: Higher timeframes = stronger FVGs

### Weak FVGs Show:
- Overlapping wicks between candles
- Very small gap size
- Low volume on formation
- Multiple gaps in same area

## Real-World FVG Example

**SPY Bullish FVG Formation:**
1. **9:30 AM**: SPY opens at $450, creates high at $450.50
2. **9:31 AM**: Strong buying pushes SPY from $450.75 to $452.25
3. **9:32 AM**: Pullback finds support at $451.00
4. **Result**: Bullish FVG from $450.50 to $451.00

**Expected Behavior:**
- Price may return to $450.50-$451.00 zone
- First touch often provides buying opportunity
- Zone acts as support for future moves
- Invalidated if price closes below $450.50
        `,
        type: "theory",
        duration: "22 min",
        keyPoints: [
          "Fair Value Gaps represent price inefficiencies where insufficient two-way trading occurred",
          "FVGs form through the three-candle rule with clear gap between first and third candle wicks",
          "Bullish FVGs act as support zones, bearish FVGs act as resistance zones",
          "Higher timeframe FVGs are more significant and reliable than lower timeframe gaps",
          "SPY/QQQ FVGs often form during news events, market opens, and earnings reactions"
        ],
        practicalExercises: [
          "Identify 5 Fair Value Gaps on SPY daily chart using the three-candle rule",
          "Mark bullish and bearish FVGs with different colors on your charts",
          "Observe how price reacts when returning to previously identified FVG zones"
        ],
        quiz: [
          {
            question: "What defines a valid Fair Value Gap formation?",
            options: [
              "Any gap between two candles",
              "A three-candle pattern where the middle candle creates a gap between the first and third candle wicks",
              "A gap that forms at market open",
              "Any price movement with high volume"
            ],
            correct: 1,
            explanation: "A valid FVG requires a three-candle pattern where the large middle candle creates a clear gap between the first candle's high/low and the third candle's low/high."
          },
          {
            question: "How should a bullish FVG behave when price returns to it?",
            options: [
              "Price should break through immediately",
              "Price should act as resistance",
              "Price should find support and potentially bounce",
              "Price should create more gaps"
            ],
            correct: 2,
            explanation: "A bullish FVG should act as a support zone when price returns to it, as the gap represents an area where buyers may step in to fill the inefficiency."
          },
          {
            question: "When is a Fair Value Gap considered invalidated?",
            options: [
              "After one touch",
              "When price closes through the gap completely",
              "After 24 hours",
              "When volume decreases"
            ],
            correct: 1,
            explanation: "An FVG is invalidated when price closes completely through the gap, indicating the inefficiency has been filled and the zone no longer holds significance."
          }
        ]
      },
      {
        id: 2,
        title: "Higher Timeframe FVGs & Multi-Timeframe Analysis",
        description: "Master the power of Higher Timeframe Fair Value Gaps and learn to combine multiple timeframes for precision entries",
        content: `
# Higher Timeframe FVGs: The Institutional Magnets

Higher timeframe Fair Value Gaps are among the most powerful tools in a professional trader's arsenal. These gaps act like magnets, drawing price back to fill inefficiencies left by rapid institutional moves.

## Why Higher Timeframes Matter

### Significance Hierarchy:
- **Weekly FVGs**: Extremely powerful, may take months to fill
- **Daily FVGs**: Very significant, often filled within days/weeks
- **4-Hour FVGs**: Strong levels, usually filled within days
- **1-Hour FVGs**: Moderate significance, filled within hours/days
- **15-Min FVGs**: Lower significance, often filled quickly

### Volume and Participation:
Higher timeframe moves involve:
- More institutional participation
- Larger order sizes
- Greater market impact
- Broader market awareness
- Stronger magnetic effect

## HTF FVG vs LTF FVG Comparison

### Higher Timeframe FVGs (Daily+):
**Advantages:**
- Higher probability of being filled
- Stronger support/resistance when reached
- Better risk/reward opportunities
- Less noise and false signals
- Institutional relevance

**Characteristics:**
- Take longer to reach
- Provide major turning points
- Often align with other key levels
- Create significant price reactions

### Lower Timeframe FVGs (1H and below):
**Advantages:**
- More frequent opportunities
- Faster fills and reactions
- Good for scalping strategies
- Quick feedback on trades

**Disadvantages:**
- Higher noise ratio
- More false signals
- Weaker reactions
- Less institutional relevance

## Multi-Timeframe FVG Analysis

### The Professional Approach:
1. **Mark HTF FVGs first** (Daily, 4H, 1H)
2. **Use LTF for entry timing** (15M, 5M)
3. **Combine with other confluences**
4. **Prioritize HTF over LTF**

### Confluence Stacking:
**High-Probability Setup:**
- Daily FVG zone
- + Previous day high/low
- + Volume profile level
- + Liquidity sweep area
- = Maximum confluence

## SPY/QQQ HTF FVG Patterns

### SPY Daily FVG Characteristics:
- **Formation**: Often during earnings, Fed announcements, major news
- **Size**: Typically $2-8 gaps on daily charts
- **Fill Rate**: 85-90% eventually get filled
- **Timeframe**: Usually filled within 1-4 weeks
- **Reaction**: Strong bounces/rejections on first touch

### QQQ Daily FVG Characteristics:
- **Formation**: Tech earnings, guidance changes, sector rotation
- **Size**: Typically $3-12 gaps due to higher volatility
- **Fill Rate**: 80-85% eventually get filled
- **Timeframe**: May take longer due to trend strength
- **Reaction**: More volatile reactions, wider zones needed

## Trading HTF FVGs: The Professional Method

### Step 1: Identification
- Scan daily/4H charts for clean FVG formations
- Mark gap boundaries clearly
- Note the context (news, earnings, etc.)
- Assess gap size and significance

### Step 2: Patience
- Wait for price to approach the HTF FVG
- Don't chase - let the gap come to you
- Monitor lower timeframes for entry signals
- Prepare for potential strong reactions

### Step 3: Entry Timing
- Use 15M/5M charts for precise entries
- Look for additional confirmations:
  - Lower timeframe structure breaks
  - Volume increases
  - Candlestick patterns
  - Momentum divergences

### Step 4: Risk Management
- Stop loss beyond the FVG zone
- Take profits at logical levels
- Trail stops as trade develops
- Respect the power of HTF levels

## Real-World HTF FVG Example

**SPY Daily Bullish FVG Setup:**
- **Formation**: Fed announcement creates gap from $445-$448
- **Wait Period**: 2 weeks for price to return
- **Entry Signal**: 15M bullish engulfing at $446 (within FVG)
- **Confirmation**: Volume spike + break of 15M structure
- **Result**: Bounce to $452 for 1.3% profit
- **Risk**: Stop at $444 (below FVG) for 0.4% risk
- **R:R Ratio**: 3.25:1

## Key HTF FVG Rules

1. **Higher timeframe always wins** - HTF FVG overrides LTF signals
2. **First touch is strongest** - Best reactions occur on initial contact
3. **Partial fills are common** - Price may only fill 50-70% of gap
4. **Context matters** - Consider overall market trend and sentiment
5. **Patience pays** - Wait for proper setups, don't force trades
        `,
        type: "practical",
        duration: "25 min",
        keyPoints: [
          "Perfect liquidity sweeps follow a 4-phase pattern: approach, penetration, reversal, follow-through",
          "SPY sweeps typically extend 0.1-0.3% beyond levels with 20-50% above average volume",
          "Visual recognition includes wick formations, volume spikes, and immediate reversals",
          "Time-based patterns show highest probability during market open and close",
          "Multi-timeframe analysis provides confirmation and precise entry timing"
        ],
        practicalExercises: [
          "Identify and analyze 3 historical liquidity sweeps on SPY using the 4-phase pattern",
          "Mark 5 potential liquidity levels on current QQQ chart and monitor for sweep patterns",
          "Practice distinguishing between true sweeps and genuine breakouts using volume analysis",
          "Create alerts for price approaching identified liquidity levels for real-time practice"
        ],
        quiz: [
          {
            question: "What is the typical penetration distance for SPY liquidity sweeps?",
            options: [
              "1-2% beyond the level",
              "0.1-0.3% beyond the level",
              "5-10% beyond the level",
              "Exactly to the level"
            ],
            correct: 1,
            explanation: "SPY liquidity sweeps typically penetrate 0.1-0.3% beyond key levels - enough to trigger stops but not so much as to indicate a genuine breakout."
          },
          {
            question: "Which phase of a liquidity sweep shows the highest volume?",
            options: [
              "The approach phase",
              "The penetration phase",
              "The reversal phase",
              "The follow-through phase"
            ],
            correct: 2,
            explanation: "The reversal phase typically shows the highest volume as institutional orders enter the market after stops are triggered."
          }
        ]
      },
      {
        id: 3,
        title: "Trading Liquidity Sweeps",
        description: "How to position yourself to profit from sweep reversals",
        content: "Once you identify a liquidity sweep, the next step is positioning...",
        type: "strategy",
        duration: "18 min",
        keyPoints: [
          "Entry timing after sweep completion",
          "Stop loss placement strategies",
          "Target setting for sweep trades"
        ]
      },
      {
        id: 4,
        title: "Sweep Analysis Workshop",
        description: "Analyze real SPY/QQQ liquidity sweeps with expert commentary",
        content: "Review historical examples of successful sweep trades",
        type: "interactive",
        duration: "7 min",
        keyPoints: [
          "Case study analysis",
          "Pattern recognition practice",
          "Risk management examples"
        ]
      }
    ]
  },
  {
    id: 3,
    title: "Confirmation Stacking & Multi-Factor Analysis",
    description: "Master the art of stacking multiple confirmations for high-probability trades. Learn to combine price action, volume, and technical analysis for professional-level precision.",
    icon: "Layers",
    color: "from-purple-500 to-purple-600",
    estimatedTime: "85 minutes",
    difficulty: "Advanced",
    prerequisites: "Understanding of liquidity sweeps and Fair Value Gaps",
    learningObjectives: [
      "Stack 3+ confirmations for every trade setup",
      "Master market structure confirmations (BOS/CHoCH)",
      "Integrate volume profile and order flow analysis",
      "Combine multiple timeframes for precision entries",
      "Develop a systematic approach to trade validation"
    ],
    lessons: [
      {
        id: 1,
        title: "Confirmation Stacking Fundamentals",
        description: "Learn the professional approach to stacking multiple confirmations for high-probability trade setups",
        content: `
# Confirmation Stacking: The Professional Edge

Even when you have a strong level or setup in mind (be it a liquidity sweep or an FVG), jumping in without confirmation can be risky. Confirmation stacking means waiting for multiple signals to line up in your favor before committing to a trade.

## The Philosophy of Confluence

**Core Principle:**
Rather than relying on a single indicator or one pattern, you look for an agreement among several independent clues – what traders often call confluence. The idea is to filter out low-quality setups and only act when many things point to the same conclusion.

**Think of it this way:**
Each confirmation is like a piece of a puzzle. One piece alone doesn't show the whole picture, but when several pieces fit together, you have a clearer image of where price might go.

## The Five Pillars of Confirmation

### 1. Market Structure & Price Action
This refers to analyzing how price swings (highs and lows) are behaving to confirm a trend change or continuation.

**Break of Structure (BOS):**
- Price takes out a significant previous high or low in the direction of a trend
- Confirms that trend's strength
- Shows institutional participation

**Change of Character (CHoCH):**
- Early sign of a possible trend reversal
- First break of a minor swing level against the trend
- Indicates potential shift in market sentiment

**Example:** If QQQ has been making higher highs and higher lows (uptrend) and then suddenly makes a lower low, that's a bearish CHoCH signaling the uptrend may be done.

### 2. Volume Profile & Range Context
Volume Profile shows how volume has been distributed at each price, giving insight into what prices the market deems "fair" vs "extreme."

**Key Elements:**
- **Point of Control (POC):** Price with highest traded volume
- **Value Area (VA):** Price range where ~70% of volume occurred
- **Value Area High/Low (VAH/VAL):** Boundaries of fair value

**Application:** If SPY rejects from yesterday's Value Area High after a liquidity sweep, that's confluence supporting a reversal trade.

### 3. Order Flow Tools (Advanced)
Real-time confirmation of what's happening under the hood through futures DOM, time and sales, or heatmap platforms.

**Signals to Watch:**
- Absorption of selling/buying at key levels
- Cumulative volume delta divergences
- Large limit orders on the book
- Aggressive vs passive order flow

### 4. Indicators & Overlays
Traditional technical indicators can be part of your confirmation stack, especially ones that measure trend or mean reversion.

**VWAP (Volume Weighted Average Price):**
- Intraday equilibrium level
- Reclaiming VWAP after a sweep adds confidence
- Acts as dynamic support/resistance

**Moving Averages:**
- 21 EMA, 50 EMA for trend confirmation
- Dynamic support on pullbacks
- Confluence with other levels

### 5. Liquidity & HTF Levels
Combining liquidity sweeps and HTF FVGs as part of confirmation checklist.

**High-Probability Setup:**
- Liquidity sweep at HTF FVG
- + Market structure confirmation
- + Volume profile level
- + VWAP reclaim
- = Maximum confluence

## The Professional Confirmation Checklist

### Minimum Requirements:
**For Entry:** At least 2-3 solid confirmations
**For High-Conviction Trades:** 4+ confirmations aligned

### Example Checklist:
1. ✓ Liquidity sweep occurred
2. ✓ Price action confirmation (CHoCH/BOS)
3. ✓ Volume profile level confluence
4. ✓ HTF FVG zone
5. ✓ VWAP reclaim/rejection

## Real-World Confirmation Stacking Example

### SPY Bearish Setup:
**Setup:** SPY approaches yesterday's high at $450

**Confirmations:**
1. **Liquidity Sweep:** Price hits $450.50, sweeps stops
2. **HTF Level:** Daily bearish FVG zone at $450-451
3. **Price Action:** 5-minute bearish engulfing + CHoCH
4. **Volume Profile:** Rejection from yesterday's VAH
5. **Volume:** Spike on sweep, sustained on reversal

**Entry:** Short at $449.50 after all confirmations align
**Stop:** $451 (above sweep high)
**Target:** $445 (previous support)
**Result:** 1% profit with 0.3% risk = 3.3:1 R/R

## Avoiding Analysis Paralysis

### Balance is Key:
- Too few confirmations = low probability
- Too many confirmations = missed opportunities
- Sweet spot: 2-3 strong confirmations

### Weighting Confirmations:
**Primary (Must Have):**
- Price action signal (structure break)
- Key level confluence (liquidity/FVG)

**Secondary (Nice to Have):**
- Volume confirmation
- Indicator alignment
- Higher timeframe context

## Common Confirmation Mistakes

1. **Forcing Confluence:** Seeing confirmations that aren't really there
2. **Over-Analysis:** Requiring too many signals
3. **Ignoring Context:** Not considering overall market environment
4. **Static Thinking:** Not adapting to changing market conditions
5. **Confirmation Bias:** Only seeing signals that support your bias
- Unfilled orders create demand/supply
- Technical traders target gap fills
- Self-fulfilling prophecy effect

## FVG vs. Regular Gaps

### Fair Value Gaps:
- Formed by 3-candle pattern
- Represent order flow imbalance
- High probability of fill (70-80%)
- Can be traded in both directions
- Show institutional activity

### Regular Price Gaps:
- Formed between sessions (overnight)
- Caused by news or events
- Lower probability of fill (40-60%)
- Often indicate trend continuation
- May not represent institutional flow

## Psychological Aspects of FVG Trading

### Institutional Perspective:
- "We moved price too fast"
- "Need to fill remaining orders"
- "Better prices available in the gap"
- "Risk management requires rebalancing"

### Retail Perspective:
- "Price gapped away from me"
- "I missed the move"
- "Will it come back?"
- "Should I chase or wait?"

## Real-World FVG Examples

### Example 1: SPY Bullish FVG
**Setup:** Fed announcement creates buying surge
**Formation:** 3-candle bullish FVG at $445-$447
**Fill:** Price returns to gap 5 days later
**Outcome:** Perfect bounce from gap support

### Example 2: QQQ Bearish FVG
**Setup:** Tech earnings disappointment
**Formation:** 3-candle bearish FVG at $380-$382
**Fill:** Price rallies to gap 2 weeks later
**Outcome:** Strong resistance at gap level

## Advanced FVG Concepts

### 1. Nested FVGs
- Multiple gaps within larger gaps
- Provide multiple trading opportunities
- Show sustained institutional activity
- Require careful order management

### 2. FVG Clusters
- Multiple gaps in same price area
- Extremely high probability zones
- Often mark major support/resistance
- Institutional accumulation/distribution areas

### 3. Partial vs. Full Fills
- **Partial Fill:** Price touches gap but doesn't close it
- **Full Fill:** Price completely closes the gap
- **Overfill:** Price extends beyond the gap
- Each has different trading implications

## Common FVG Mistakes

1. **Trading Every Gap:** Not all FVGs are equal quality
2. **Ignoring Context:** Market structure matters
3. **Poor Risk Management:** Gaps can extend before filling
4. **Wrong Timeframe:** Match timeframe to trading style
5. **Emotional Trading:** FOMO on gap formations
        `,
        type: "theory",
        duration: "25 min",
        keyPoints: [
          "Fair Value Gaps represent institutional order flow imbalances created by overwhelming buying/selling pressure",
          "FVGs require exactly 3 candles with no overlap between outer candles' high/low",
          "SPY FVGs typically range 0.2-0.8% while QQQ ranges 0.3-1.2% due to higher volatility",
          "70-80% of FVGs get filled within 5-20 sessions as markets seek price efficiency",
          "Inversion FVGs become powerful support/resistance after being filled"
        ],
        practicalExercises: [
          "Identify 5 bullish and 5 bearish FVGs on SPY 30-minute chart from last month",
          "Measure the size of each FVG as percentage of price and compare to typical ranges",
          "Track which FVGs got filled and calculate the fill rate for your sample",
          "Practice distinguishing between FVGs and regular overnight gaps"
        ],
        quiz: [
          {
            question: "How many candles are required to form a Fair Value Gap?",
            options: [
              "2 candles",
              "3 candles",
              "4 candles",
              "5 candles"
            ],
            correct: 1,
            explanation: "A Fair Value Gap requires exactly 3 consecutive candles, with the middle candle creating the imbalance and no overlap between the outer candles."
          },
          {
            question: "What percentage of Fair Value Gaps typically get filled?",
            options: [
              "30-40%",
              "50-60%",
              "70-80%",
              "90-100%"
            ],
            correct: 2,
            explanation: "Approximately 70-80% of Fair Value Gaps get filled as markets naturally seek price efficiency and institutional orders get completed."
          }
        ]
      },
      {
        id: 2,
        title: "FVG Classification System",
        description: "Learn to classify FVGs by strength and probability",
        content: "Not all FVGs are equal. Learn the classification system...",
        type: "practical",
        duration: "15 min",
        keyPoints: [
          "High probability vs low probability gaps",
          "Size and context importance",
          "Multiple timeframe FVG analysis"
        ]
      },
      {
        id: 3,
        title: "Trading FVG Fills",
        description: "Strategies for trading when price returns to fill gaps",
        content: "FVG fills often provide excellent trading opportunities...",
        type: "strategy",
        duration: "16 min",
        keyPoints: [
          "Partial vs full gap fills",
          "Entry and exit strategies",
          "Combining FVGs with other confluences"
        ]
      },
      {
        id: 4,
        title: "FVG Recognition Challenge",
        description: "Test your ability to spot and classify FVGs in real-time",
        content: "Interactive challenge to identify FVGs on live charts",
        type: "interactive",
        duration: "7 min",
        keyPoints: [
          "Speed recognition drills",
          "Classification accuracy",
          "Real-time decision making"
        ]
      }
    ]
  },
  {
    id: 4,
    title: "Volume Analysis & Confirmation",
    description: "Use volume analysis to confirm your price action signals",
    icon: "Activity",
    color: "from-orange-500 to-orange-600",
    estimatedTime: "40 minutes",
    difficulty: "Beginner",
    lessons: [
      {
        id: 1,
        title: "Volume Fundamentals",
        description: "Understanding volume and its relationship to price movement",
        content: "Volume is the fuel that drives price movement...",
        type: "theory",
        duration: "10 min",
        keyPoints: [
          "Volume precedes price",
          "Accumulation vs distribution patterns",
          "Volume profile concepts"
        ]
      },
      {
        id: 2,
        title: "Volume at Key Levels",
        description: "Analyzing volume behavior at support/resistance zones",
        content: "How volume behaves at key levels tells us about market sentiment...",
        type: "practical",
        duration: "15 min",
        keyPoints: [
          "Rising volume on approach to zones",
          "Fading volume and false breakouts",
          "Climactic volume patterns"
        ]
      },
      {
        id: 3,
        title: "Volume Confirmation Strategies",
        description: "Using volume to confirm your trading signals",
        content: "Volume confirmation can significantly improve trade success rates...",
        type: "strategy",
        duration: "12 min",
        keyPoints: [
          "Volume divergence signals",
          "Confirmation vs contradiction",
          "Multiple timeframe volume analysis"
        ]
      },
      {
        id: 4,
        title: "Volume Analysis Practice",
        description: "Practice reading volume patterns on SPY/QQQ charts",
        content: "Hands-on practice with volume analysis techniques",
        type: "interactive",
        duration: "3 min",
        keyPoints: [
          "Pattern recognition",
          "Signal confirmation practice",
          "Real-world application"
        ]
      }
    ]
  },
  {
    id: 5,
    title: "Confirmation Stacking",
    description: "Learn to stack multiple confirmations for high-probability trades",
    icon: "Layers",
    color: "from-red-500 to-red-600",
    estimatedTime: "55 minutes",
    difficulty: "Advanced",
    lessons: [
      {
        id: 1,
        title: "The Stacking Methodology",
        description: "Understanding the concept of confirmation stacking",
        content: "Confirmation stacking involves combining multiple technical signals...",
        type: "theory",
        duration: "12 min",
        keyPoints: [
          "Quality over quantity in confirmations",
          "Weighted confirmation systems",
          "Avoiding analysis paralysis"
        ]
      },
      {
        id: 2,
        title: "Building Your Stack",
        description: "How to systematically build confirmation stacks",
        content: "Learn the systematic approach to building robust confirmation stacks...",
        type: "practical",
        duration: "18 min",
        keyPoints: [
          "Primary vs secondary confirmations",
          "Timeframe hierarchy",
          "Confluence zone identification"
        ]
      },
      {
        id: 3,
        title: "Advanced Stacking Techniques",
        description: "Professional-level confirmation stacking strategies",
        content: "Advanced techniques used by professional traders...",
        type: "strategy",
        duration: "20 min",
        keyPoints: [
          "Multi-timeframe stacking",
          "Intermarket confirmations",
          "Sentiment-based confirmations"
        ]
      },
      {
        id: 4,
        title: "Stacking Mastery Challenge",
        description: "Put your stacking skills to the test with complex scenarios",
        content: "Advanced challenge scenarios to test your mastery",
        type: "interactive",
        duration: "5 min",
        keyPoints: [
          "Complex scenario analysis",
          "Decision-making under pressure",
          "Professional-level execution"
        ]
      }
    ]
  },
  {
    id: 6,
    title: "Risk Management & Psychology",
    description: "Master the mental game and risk management for consistent profits",
    icon: "Shield",
    color: "from-indigo-500 to-indigo-600",
    estimatedTime: "45 minutes",
    difficulty: "Intermediate",
    lessons: [
      {
        id: 1,
        title: "Position Sizing Fundamentals",
        description: "Calculate optimal position sizes for your account",
        content: "Proper position sizing is the foundation of risk management...",
        type: "theory",
        duration: "12 min",
        keyPoints: [
          "Risk percentage rules",
          "Account size considerations",
          "Volatility-adjusted sizing"
        ]
      },
      {
        id: 2,
        title: "Stop Loss Strategies",
        description: "Advanced stop loss placement and management techniques",
        content: "Stop losses are your insurance policy in trading...",
        type: "practical",
        duration: "15 min",
        keyPoints: [
          "Technical vs percentage stops",
          "Trailing stop strategies",
          "Stop loss psychology"
        ]
      },
      {
        id: 3,
        title: "Trading Psychology Mastery",
        description: "Develop the mental discipline required for consistent trading",
        content: "Trading psychology often determines success more than technical skills...",
        type: "strategy",
        duration: "15 min",
        keyPoints: [
          "Emotional regulation techniques",
          "Dealing with losses",
          "Maintaining discipline"
        ]
      },
      {
        id: 4,
        title: "Psychology Assessment",
        description: "Evaluate your trading psychology and identify areas for improvement",
        content: "Self-assessment tools for trading psychology",
        type: "interactive",
        duration: "3 min",
        keyPoints: [
          "Psychological profiling",
          "Weakness identification",
          "Improvement planning"
        ]
      }
    ]
  },
  {
    id: 7,
    title: "Advanced Pattern Recognition",
    description: "Identify complex patterns and market structures for professional-level trading",
    icon: "Eye",
    color: "from-teal-500 to-teal-600",
    estimatedTime: "65 minutes",
    difficulty: "Advanced",
    lessons: [
      {
        id: 1,
        title: "Complex Pattern Structures",
        description: "Understanding advanced chart patterns and their implications",
        content: "Advanced patterns often provide the highest probability setups...",
        type: "theory",
        duration: "18 min",
        keyPoints: [
          "Multi-timeframe pattern analysis",
          "Pattern failure and continuation",
          "Context-dependent patterns"
        ]
      },
      {
        id: 2,
        title: "Market Structure Shifts",
        description: "Identifying when market structure changes and how to adapt",
        content: "Market structure shifts signal major changes in sentiment...",
        type: "practical",
        duration: "20 min",
        keyPoints: [
          "Break of structure signals",
          "Change of character patterns",
          "Trend transition identification"
        ]
      },
      {
        id: 3,
        title: "Professional Pattern Trading",
        description: "How professionals trade complex patterns for maximum profit",
        content: "Professional trading strategies for advanced patterns...",
        type: "strategy",
        duration: "22 min",
        keyPoints: [
          "Entry and exit optimization",
          "Risk-reward maximization",
          "Pattern-specific strategies"
        ]
      },
      {
        id: 4,
        title: "Pattern Mastery Exam",
        description: "Final examination of your pattern recognition abilities",
        content: "Comprehensive test of all pattern recognition skills",
        type: "interactive",
        duration: "5 min",
        keyPoints: [
          "Comprehensive pattern test",
          "Speed and accuracy assessment",
          "Professional certification"
        ]
      }
    ]
  }
]

export const COURSE_ACHIEVEMENTS = [
  {
    id: "first_lesson",
    title: "Getting Started",
    description: "Complete your first lesson",
    icon: "Play",
    points: 10
  },
  {
    id: "first_module",
    title: "Module Master",
    description: "Complete your first module",
    icon: "Award",
    points: 50
  },
  {
    id: "quiz_master",
    title: "Quiz Master",
    description: "Score 90% or higher on 5 quizzes",
    icon: "Brain",
    points: 100
  },
  {
    id: "speed_learner",
    title: "Speed Learner",
    description: "Complete 3 lessons in one day",
    icon: "Zap",
    points: 75
  },
  {
    id: "course_complete",
    title: "Course Graduate",
    description: "Complete the entire course",
    icon: "GraduationCap",
    points: 500
  }
]
