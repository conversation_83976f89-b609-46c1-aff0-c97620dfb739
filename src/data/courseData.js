/**
 * Advanced Price Action Course Data
 * Professional SPY/QQQ Trading Course - Liquidity Sweeps, FVGs, and Confirmation Stacking
 * Complete educational content with real trading strategies
 */

export const COURSE_MODULES = [
  {
    id: 1,
    title: "Market Structure & Liquidity Fundamentals",
    description: "Master the institutional approach to reading market structure. Learn how professional traders identify liquidity zones and predict price movement on SPY/QQQ.",
    icon: "Target",
    color: "from-blue-500 to-blue-600",
    estimatedTime: "90 minutes",
    difficulty: "Beginner",
    prerequisites: "Basic understanding of candlestick charts and support/resistance levels",
    learningObjectives: [
      "Decode institutional market structure patterns with professional precision",
      "Identify high-probability liquidity zones where smart money operates",
      "Distinguish between genuine breakouts and institutional stop hunts",
      "Apply market structure analysis to SPY/QQQ for consistent profits",
      "Master the professional trader's mindset: 'liquidity drives direction'"
    ],
    lessons: [
      {
        id: 1,
        title: "Institutional Market Structure: Reading the Smart Money Footprints",
        description: "Discover how professional traders analyze market structure to predict institutional moves and identify high-probability trading opportunities",
        imageUrl: "https://images.unsplash.com/photo-*************-9c2a0a7236a3?w=800&h=400&fit=crop",
        content: `
# Chapter 1: Institutional Market Structure Analysis

*"The market is a voting machine in the short run, but a weighing machine in the long run. Understanding who's voting and why they're weighing gives you the ultimate edge."* - Professional Trading Axiom

---

## Section 1.1: The Professional Trader's Perspective

### What Separates Institutional Traders from Retail?

Professional institutional traders operate with a fundamentally different approach than retail traders. While retail traders often focus on indicators and patterns, institutions analyze **market structure** - the underlying framework that reveals where smart money is positioned and where they're likely to move next.

**Key Institutional Advantages:**
- Access to order flow data showing real-time buying/selling pressure
- Understanding of where retail stops cluster (their liquidity targets)
- Ability to move markets through large position sizes
- Advanced risk management systems that retail traders lack

### The Market Structure Hierarchy

Market structure operates on multiple levels, each providing different insights:

**1. Primary Structure (Daily/Weekly)**
- Major swing highs and lows that define long-term trends
- Key institutional accumulation and distribution zones
- Primary support and resistance levels that matter to big money

**2. Secondary Structure (4H/1H)**
- Intermediate swings that show institutional positioning
- Areas where institutions build or reduce positions
- Critical levels for swing trading opportunities

**3. Tertiary Structure (15M/5M)**
- Short-term swings for precise entry and exit timing
- Scalping opportunities within larger institutional moves
- Fine-tuning entries for optimal risk/reward ratios

---

## Section 1.2: Market Structure Components

### Higher Highs and Higher Lows (Uptrend Structure)

In a healthy uptrend, price creates a series of higher highs (HH) and higher lows (HL). This pattern indicates:

- **Institutional Accumulation**: Smart money is building long positions
- **Retail FOMO**: Fear of missing out drives retail buying at higher prices
- **Momentum Continuation**: Each pullback finds buyers at higher levels

**Professional Trading Insight**: The quality of higher lows is more important than higher highs. Strong higher lows with volume support indicate institutional backing.

### Lower Highs and Lower Lows (Downtrend Structure)

In a bearish market structure, price creates lower highs (LH) and lower lows (LL):

- **Institutional Distribution**: Smart money is reducing long positions or building shorts
- **Retail Denial**: Retail traders often buy "dips" that continue falling
- **Momentum Breakdown**: Each rally fails at lower levels

### Sideways Structure (Accumulation/Distribution)

When price moves sideways between defined levels:

- **Accumulation Phase**: Institutions quietly build positions before markup
- **Distribution Phase**: Institutions exit positions before markdown
- **Retail Confusion**: Sideways action frustrates retail traders into poor decisions

---

## Section 1.3: SPY/QQQ Specific Characteristics

### SPY (S&P 500 ETF) Structure Patterns

**Market Hours Behavior:**
- **9:30-10:30 AM**: Initial balance formation, often with false breakouts
- **10:30 AM-3:00 PM**: Trending moves or range-bound consolidation
- **3:00-4:00 PM**: Institutional positioning for overnight holds

**Key SPY Levels:**
- **Psychological Levels**: Round numbers (400, 450, 500) act as magnets
- **Previous Day Extremes**: High/low from prior session are critical
- **Weekly/Monthly Levels**: Major institutional reference points

### QQQ (Nasdaq 100 ETF) Structure Patterns

**Technology Sector Sensitivity:**
- More volatile than SPY due to growth stock concentration
- Reacts strongly to tech earnings and guidance changes
- Higher beta creates larger structure swings

**QQQ-Specific Considerations:**
- **After-hours Impact**: Tech stocks trade actively after market close
- **Correlation Breaks**: Sometimes diverges from SPY during sector rotation
- **Momentum Extremes**: Can extend further than SPY in both directions

---

## Section 1.4: Professional Structure Analysis Techniques

### The Three-Timeframe Approach

**1. Higher Timeframe Context (Daily/4H)**
- Identifies the primary trend direction
- Shows major institutional positioning
- Provides overall market bias for trading decisions

**2. Entry Timeframe Analysis (1H/30M)**
- Pinpoints specific entry and exit opportunities
- Shows intermediate structure breaks and confirmations
- Balances precision with broader market context

**3. Execution Timeframe Precision (15M/5M)**
- Fine-tunes exact entry and exit points
- Manages risk with tight stop-loss placement
- Maximizes risk/reward ratios through precise timing

### Structure Quality Assessment

**Strong Structure Characteristics:**
- Clear, well-defined swing points with significant price separation
- Volume confirmation at key structural levels
- Multiple timeframe alignment showing consistent patterns
- Clean breaks with follow-through momentum

**Weak Structure Warning Signs:**
- Overlapping swing points creating confusion
- Low volume at critical structural levels
- Conflicting signals across different timeframes
- Failed breaks with immediate reversals

---

## Section 1.5: Practical Application Framework

### Daily Structure Analysis Routine

**Morning Preparation (Pre-Market):**
1. Identify overnight structure changes in SPY/QQQ
2. Mark key levels from previous session's structure
3. Note any gaps or significant news that might affect structure
4. Plan potential scenarios based on structure analysis

**Intraday Monitoring:**
1. Track real-time structure development
2. Identify structure breaks as they occur
3. Assess the quality and follow-through of breaks
4. Adjust trading bias based on evolving structure

**End-of-Day Review:**
1. Analyze how structure played out during the session
2. Identify successful and failed structure predictions
3. Update key levels for next trading session
4. Document lessons learned for continuous improvement

### Risk Management Through Structure

**Structure-Based Stop Placement:**
- Place stops beyond significant structure levels
- Use structure to determine position sizing
- Adjust stops as structure evolves intraday

**Profit Target Selection:**
- Target next significant structure level
- Use structure to determine risk/reward ratios
- Scale out at multiple structure-based targets
        `,
        type: "foundational",
        duration: "35 min",
        sections: [
          {
            title: "The Professional Trader's Perspective",
            duration: "8 min",
            keyPoints: [
              "Institutional vs retail trading approaches",
              "Market structure hierarchy and timeframe analysis",
              "Professional advantages in market analysis"
            ]
          },
          {
            title: "Market Structure Components",
            duration: "12 min",
            keyPoints: [
              "Higher highs/higher lows in uptrend analysis",
              "Lower highs/lower lows in downtrend identification",
              "Sideways structure and accumulation/distribution phases"
            ]
          },
          {
            title: "SPY/QQQ Specific Analysis",
            duration: "10 min",
            keyPoints: [
              "SPY market hours behavior and key levels",
              "QQQ technology sector sensitivity patterns",
              "ETF-specific structure characteristics"
            ]
          },
          {
            title: "Professional Application Framework",
            duration: "5 min",
            keyPoints: [
              "Three-timeframe analysis approach",
              "Daily structure analysis routine",
              "Risk management through structure"
            ]
          }
        ],
        keyPoints: [
          "Market structure reveals institutional positioning and smart money flow",
          "Three-timeframe analysis provides context, entry signals, and execution precision",
          "SPY/QQQ structure patterns differ due to sector composition and volatility",
          "Professional traders use structure for risk management and profit targeting",
          "Quality structure assessment separates high-probability from low-probability setups"
        ],
        practicalExercises: [
          "Analyze current SPY daily chart and identify primary, secondary, and tertiary structure levels",
          "Compare SPY vs QQQ structure patterns over the past week and note differences",
          "Practice the three-timeframe approach: Daily context → 1H entries → 15M execution",
          "Create a daily structure analysis routine and apply it for one full trading week"
        ],
        quiz: [
          {
            question: "What is the most important difference between institutional and retail trading approaches?",
            options: [
              "Institutions use more indicators and technical analysis tools",
              "Institutions focus on market structure while retail focuses on indicators",
              "Institutions trade larger position sizes with more capital",
              "Institutions have access to better charting software"
            ],
            correct: 1,
            explanation: "The fundamental difference is analytical approach: institutions analyze market structure to understand where smart money is positioned, while retail traders typically rely on lagging indicators and patterns."
          },
          {
            question: "In the three-timeframe approach, what is the primary purpose of the higher timeframe analysis?",
            options: [
              "To find exact entry and exit points",
              "To identify the primary trend direction and institutional positioning",
              "To determine precise stop-loss placement",
              "To calculate position sizing for trades"
            ],
            correct: 1,
            explanation: "Higher timeframe analysis (Daily/4H) provides the overall market context, showing the primary trend direction and major institutional positioning that guides all trading decisions."
          },
          {
            question: "What makes QQQ structure analysis different from SPY?",
            options: [
              "QQQ has lower volatility and smaller price movements",
              "QQQ only trades during regular market hours",
              "QQQ is more sensitive to technology sector news and has higher volatility",
              "QQQ structure patterns are identical to SPY patterns"
            ],
            correct: 2,
            explanation: "QQQ's concentration in technology stocks makes it more volatile than SPY and highly sensitive to tech sector news, earnings, and guidance changes, creating different structure patterns."
          },
          {
            question: "What characterizes 'strong structure' in professional analysis?",
            options: [
              "Many overlapping swing points with frequent reversals",
              "Clear, well-defined swing points with volume confirmation and timeframe alignment",
              "Low volume at structural levels with minimal price separation",
              "Conflicting signals across different timeframes"
            ],
            correct: 1,
            explanation: "Strong structure features clear swing points with significant price separation, volume confirmation at key levels, multiple timeframe alignment, and clean breaks with follow-through."
          },
          {
            question: "During SPY's typical trading day, when do the most significant institutional positioning moves occur?",
            options: [
              "During the first 30 minutes after market open",
              "During the lunch hour (12:00-1:00 PM)",
              "During the final hour (3:00-4:00 PM) for overnight positioning",
              "Institutional moves are evenly distributed throughout the day"
            ],
            correct: 2,
            explanation: "The final trading hour (3:00-4:00 PM) is when institutions make their most significant positioning moves, preparing for overnight holds and next-day strategies."
          }
        ]
      },
      {
        id: 2,
        title: "Liquidity Sweeps: The Professional's Guide to Reading Institutional Moves",
        description: "Master the art of identifying and trading liquidity sweeps - the institutional strategy that moves markets and creates the highest probability trading opportunities",
        imageUrl: "https://images.unsplash.com/photo-1590283603385-17ffb3a7f29f?w=800&h=400&fit=crop",
        content: `
# Chapter 2: Liquidity Sweeps - Decoding Institutional Market Manipulation

*"The best trades come from understanding where the weak hands are positioned and how the strong hands will exploit them."* - Professional Trading Principle

---

## Section 2.1: The Institutional Liquidity Strategy

### What Are Liquidity Sweeps?

A **liquidity sweep** is a deliberate institutional strategy where large market participants drive price through obvious levels to trigger clusters of retail stop orders. This isn't random market movement - it's calculated exploitation of predictable retail behavior.

**The Institutional Advantage:**
- Institutions know where retail stops cluster (obvious levels)
- They have the capital to move price temporarily
- They use retail liquidity to fill their large orders
- They profit from the subsequent reversal

### Why Institutions Need Liquidity

**The Large Order Problem:**
When institutions need to buy or sell millions of dollars worth of stock, they face a critical challenge:

- **Market Impact**: Large orders move prices against them
- **Slippage**: Poor fills reduce profitability
- **Visibility**: Other institutions can front-run their orders
- **Timing**: They need liquidity when they want it, not when it's naturally available

**The Liquidity Solution:**
By triggering retail stops, institutions create artificial liquidity pools they can exploit for better fills.

---

## Section 2.2: The Anatomy of a Professional Liquidity Sweep

### Phase 1: The Setup (Accumulation)
**Duration**: Hours to days
**Characteristics**:
- Price consolidates near a key level
- Retail traders place obvious stops just beyond the level
- Institutional algorithms identify the stop clusters
- Volume decreases as the setup develops

**Professional Insight**: The longer the consolidation, the more stops accumulate, creating a larger liquidity pool for institutions to exploit.

### Phase 2: The Hunt (Execution)
**Duration**: Minutes to hours
**Characteristics**:
- Price accelerates toward the target level
- Momentum builds as breakout traders join
- Volume increases as the level approaches
- Retail FOMO (fear of missing out) intensifies

**Key Indicators**:
- Sudden volume spikes without news catalysts
- Price acceleration into obvious levels
- Breakout confirmation signals triggering

### Phase 3: The Sweep (Liquidity Grab)
**Duration**: Seconds to minutes
**Characteristics**:
- Price penetrates the level by a small margin (typically 0.1-0.3% for SPY)
- Massive volume spike as stops trigger
- Institutions absorb the triggered orders
- Price immediately stalls or reverses

**Critical Measurements**:
- **SPY Penetration**: Usually $0.50-$1.50 beyond level
- **QQQ Penetration**: Usually $0.75-$2.25 beyond level
- **Volume Spike**: 200-500% of average volume
- **Time Duration**: Rarely sustains beyond 5-15 minutes

### Phase 4: The Reversal (Institutional Profit)
**Duration**: Minutes to hours
**Characteristics**:
- Sharp reversal back through the swept level
- Higher volume than the initial sweep
- Sustained momentum in the reversal direction
- Retail traders trapped in losing positions

**Professional Recognition**:
- Volume on reversal exceeds sweep volume
- Price moves decisively away from swept level
- Previous support becomes resistance (or vice versa)
- Follow-through confirms institutional participation

---

## Section 2.3: SPY/QQQ Specific Sweep Patterns

### SPY Liquidity Characteristics

**High-Probability Sweep Levels:**
- **Previous Day High/Low**: Most reliable sweep targets
- **Round Numbers**: $400, $450, $500 act as psychological magnets
- **Weekly/Monthly Extremes**: Major institutional reference points
- **Gap Levels**: Overnight gaps create obvious targets

**SPY Sweep Timing Patterns:**
- **9:30-10:00 AM**: Morning volatility creates sweep opportunities
- **11:30 AM-12:30 PM**: Pre-lunch positioning sweeps
- **3:00-4:00 PM**: End-of-day institutional positioning
- **Economic Releases**: News-driven sweeps during data releases

### QQQ Unique Sweep Characteristics

**Technology Sector Amplification:**
- Higher volatility creates larger sweep distances
- After-hours news impacts next-day sweep probability
- Earnings season increases sweep frequency
- Growth stock sensitivity amplifies reversal moves

**QQQ-Specific Patterns:**
- **Tech Earnings Sweeps**: Pre/post earnings volatility
- **Fed Meeting Sweeps**: Interest rate sensitivity
- **Sector Rotation Sweeps**: Growth vs value transitions
- **Options Expiration Sweeps**: Monthly/weekly expiry effects
        `,
        type: "advanced",
        duration: "40 min",
        sections: [
          {
            title: "The Institutional Liquidity Strategy",
            duration: "10 min",
            keyPoints: [
              "Understanding why institutions need liquidity sweeps",
              "The large order problem and institutional solutions",
              "How institutions exploit predictable retail behavior"
            ]
          },
          {
            title: "Anatomy of Professional Liquidity Sweeps",
            duration: "15 min",
            keyPoints: [
              "Four-phase sweep analysis: Setup, Hunt, Sweep, Reversal",
              "Critical measurements for SPY/QQQ sweep identification",
              "Professional recognition techniques and timing"
            ]
          },
          {
            title: "SPY/QQQ Specific Sweep Patterns",
            duration: "15 min",
            keyPoints: [
              "High-probability sweep levels and timing patterns",
              "Technology sector amplification in QQQ sweeps",
              "Market hours behavior and institutional positioning"
            ]
          }
        ],
        keyPoints: [
          "Liquidity sweeps are calculated institutional strategies, not random market movements",
          "Four-phase anatomy: Setup → Hunt → Sweep → Reversal provides professional framework",
          "SPY penetrations typically $0.50-$1.50, QQQ $0.75-$2.25 beyond key levels",
          "Volume spikes of 200-500% average confirm institutional participation",
          "Timing patterns reveal institutional positioning strategies throughout trading day"
        ],
        practicalExercises: [
          "Identify and analyze 5 liquidity sweeps on SPY using the four-phase framework",
          "Measure penetration distances and volume spikes on recent QQQ sweeps",
          "Track sweep timing patterns during different market hours for one week",
          "Create alerts for high-probability sweep levels based on previous day extremes"
        ],
        quiz: [
          {
            question: "What is the primary reason institutions execute liquidity sweeps?",
            options: [
              "To create market volatility for profit",
              "To trigger retail stops and use that liquidity for large order fills",
              "To test technical support and resistance levels",
              "To signal major trend changes to other institutions"
            ],
            correct: 1,
            explanation: "Institutions execute liquidity sweeps to solve the large order problem - they trigger retail stops to create artificial liquidity pools they can use for better fills on their large orders."
          },
          {
            question: "What is the typical penetration distance for SPY liquidity sweeps?",
            options: [
              "$0.10-$0.25 beyond the level",
              "$0.50-$1.50 beyond the level",
              "$2.00-$3.00 beyond the level",
              "$5.00+ beyond the level"
            ],
            correct: 1,
            explanation: "SPY liquidity sweeps typically penetrate $0.50-$1.50 beyond key levels - enough to trigger stops but not so much as to indicate a genuine breakout attempt."
          },
          {
            question: "During which phase of a liquidity sweep does the highest volume typically occur?",
            options: [
              "Phase 1: The Setup",
              "Phase 2: The Hunt",
              "Phase 3: The Sweep",
              "Phase 4: The Reversal"
            ],
            correct: 3,
            explanation: "Phase 4 (The Reversal) typically shows the highest volume as institutional orders enter the market after stops are triggered, often exceeding the volume from the initial sweep."
          },
          {
            question: "What makes QQQ sweeps different from SPY sweeps?",
            options: [
              "QQQ sweeps are smaller and less volatile",
              "QQQ sweeps only occur during regular trading hours",
              "QQQ sweeps have larger penetration distances due to higher volatility",
              "QQQ sweeps are less reliable than SPY sweeps"
            ],
            correct: 2,
            explanation: "QQQ's higher volatility due to technology sector concentration creates larger sweep penetration distances ($0.75-$2.25 vs SPY's $0.50-$1.50) and more dramatic reversal moves."
          },
          {
            question: "When do the highest probability liquidity sweeps typically occur on SPY?",
            options: [
              "During overnight trading sessions",
              "During lunch hours (12:00-1:00 PM)",
              "During morning volatility (9:30-10:00 AM) and end-of-day positioning (3:00-4:00 PM)",
              "Sweeps occur randomly throughout the trading day"
            ],
            correct: 2,
            explanation: "The highest probability sweeps occur during morning volatility (9:30-10:00 AM) when stops accumulate overnight, and during end-of-day positioning (3:00-4:00 PM) when institutions position for overnight holds."
          }
        ]
      },
      {
        id: 3,
        title: "Zone Validation Techniques",
        description: "How to validate the strength and reliability of your zones",
        imageUrl: "https://images.unsplash.com/photo-1590283603385-17ffb3a7f29f?w=800&h=400&fit=crop",
        content: `
# Zone Validation: Separating High-Probability from Low-Probability Levels

Not all liquidity zones are created equal. The difference between profitable and unprofitable trading often comes down to your ability to distinguish between strong, reliable zones and weak ones that are likely to fail.

## The Professional Zone Validation Framework

### 1. Volume Confirmation Analysis

**High-Volume Zone Formation:**
- Zones created with 150%+ above average volume carry more weight
- Institutional participation creates lasting support/resistance
- Volume spikes during zone formation indicate real buying/selling interest
- Low volume zones often get broken easily

**Volume at Zone Tests:**
- First test should show declining volume (profit-taking)
- Subsequent tests with increasing volume suggest accumulation/distribution
- Volume divergence at zones often signals potential breaks

### 2. Multiple Timeframe Validation

**The 3-Timeframe Rule:**
- Daily zones override 4-hour zones
- 4-hour zones override 1-hour zones  
- 1-hour zones override 15-minute zones
- Always trade in direction of higher timeframe zones

**Confluence Stacking:**
- Daily resistance + 4H resistance = High probability short zone
- Weekly support + Daily support = Extremely strong support
- Conflicting timeframes = Avoid or wait for resolution

### 3. Age and Test Frequency

**Fresh vs Aged Zones:**
- Fresh zones (untested): Highest probability on first touch
- Tested once: Still valid but lower probability
- Multiple tests: Weakening, look for breaks
- Ancient zones: Often forgotten by market, can be very powerful

**Touch Quality Assessment:**
- Clean bounces indicate respect for the zone
- Wicks through the zone show testing but holding
- Bodies through the zone indicate weakening
- Multiple body closes through = Zone invalidated

## SPY/QQQ Zone Validation Specifics

### SPY Zone Characteristics:
- **Morning Gap Zones**: High probability reversal areas
- **Previous Day High/Low**: Most reliable intraday zones  
- **Weekly/Monthly Levels**: Extremely strong institutional reference points
- **Options Strike Prices**: Magnetic effect during expiration week

### QQQ Zone Characteristics:
- **Tech Earnings Zones**: Created during earnings season, very reliable
- **After-Hours Gap Zones**: Strong due to limited liquidity during formation
- **Sector Rotation Zones**: Form during growth vs value transitions
- **Correlation Zones**: Areas where QQQ historically correlates with NASDAQ futures

## Advanced Zone Validation Techniques

### 1. Order Flow Confirmation
- Large orders resting at zone levels (Level 2 data)
- Absorption patterns when price approaches zones
- Cumulative delta divergences at zone boundaries
- Time and sales showing institutional activity

### 2. Market Structure Integration
- Zones that align with structure breaks are stronger
- Zones in trending markets vs ranging markets behave differently
- Break of structure through a zone invalidates it completely
- Change of character at zones signals potential reversals

### 3. Risk-Reward Assessment
- Minimum 2:1 risk-reward ratio for zone trades
- Account for spread and slippage in calculations
- Position size based on zone strength and confidence level
- Multiple profit targets using zone clusters

## Real-World Zone Validation Example

**SPY Daily Support Zone Validation:**

**Setup:** SPY approaching $445 support zone from October lows

**Validation Checklist:**
✅ **Volume**: Zone created with 200% above average volume
✅ **Timeframe**: Daily and Weekly support confluence  
✅ **Age**: Tested once successfully, holding for 3 weeks
✅ **Structure**: Aligns with major swing low structure
✅ **Context**: Market in uptrend, looking for long opportunities

**Trade Execution:**
- Entry: $445.50 on first touch with bullish rejection candle
- Stop: $444.00 (below zone invalidation level)
- Target 1: $448.00 (previous resistance)
- Target 2: $451.00 (next major resistance zone)
- Risk-Reward: 1.5 risk for 6 reward = 4:1 ratio

**Outcome:** Zone held perfectly, target 1 hit in 2 days, target 2 hit in 1 week

## Common Zone Validation Mistakes

### 1. Ignoring Volume Context
- Trading zones formed on low volume
- Not considering volume at previous tests
- Missing institutional volume signatures

### 2. Wrong Timeframe Priority  
- Using lower timeframe zones against higher timeframe trends
- Not respecting higher timeframe zone confluences
- Trading against clear higher timeframe direction

### 3. Poor Risk Management
- Position sizing not based on zone strength
- Stop losses too tight for zone thickness
- Not accounting for fake-outs and whipsaws

### 4. Emotional Zone Selection
- Seeing zones that fit your bias rather than market reality
- Forcing trades when no valid zones exist
- Not waiting for proper zone validation criteria

## Zone Validation Scorecard

**Rate each criterion 1-5 and add up for total zone score:**

- Volume Confirmation (1-5)
- Timeframe Confluence (1-5)  
- Age and Test History (1-5)
- Market Structure Alignment (1-5)
- Risk-Reward Ratio (1-5)

**Scoring:**
- 20-25: Excellent zone, maximum position size
- 15-19: Good zone, standard position size
- 10-14: Marginal zone, reduced position size
- Below 10: Avoid trade, wait for better setup
        `,
        type: "practical",
        duration: "18 min",
        sections: [
          {
            title: "Volume Confirmation Analysis",
            duration: "5 min",
            keyPoints: [
              "High-volume zone formation indicates institutional participation",
              "Volume behavior at zone tests reveals market sentiment",
              "Low volume zones are more likely to break"
            ]
          },
          {
            title: "Multiple Timeframe Validation",
            duration: "6 min",
            keyPoints: [
              "Higher timeframe zones always override lower timeframes",
              "Confluence stacking creates high-probability setups",
              "Conflicting timeframes require patience for resolution"
            ]
          },
          {
            title: "Age and Test Frequency Assessment",
            duration: "4 min",
            keyPoints: [
              "Fresh zones have highest probability on first touch",
              "Multiple tests weaken zone reliability",
              "Ancient untested zones can be extremely powerful"
            ]
          },
          {
            title: "Advanced Validation Techniques",
            duration: "3 min",
            keyPoints: [
              "Order flow confirmation using Level 2 and T&S data",
              "Market structure integration for zone strength",
              "Professional risk-reward assessment methods"
            ]
          }
        ],
        keyPoints: [
          "Volume confirmation at zones separates institutional from retail levels",
          "Multiple timeframe validation prevents trading against major trends",
          "Age and frequency of tests determine zone reliability and strength",
          "SPY/QQQ zones have specific characteristics based on market structure",
          "Professional validation uses confluence scoring for objective assessment"
        ],
        practicalExercises: [
          "Create a zone validation scorecard and rate 5 current SPY zones",
          "Identify volume patterns at 3 recent QQQ support/resistance tests",
          "Practice the 3-timeframe validation on live charts for one trading week",
          "Build a confluence stack with daily, 4H, and 1H zones on current market"
        ],
        quiz: [
          {
            question: "What is the most important factor in zone validation?",
            options: [
              "The age of the zone",
              "Volume confirmation during zone formation and tests",
              "The number of times it's been tested",
              "The timeframe it was created on"
            ],
            correct: 1,
            explanation: "Volume confirmation is crucial as it indicates real institutional participation versus random price levels. High volume during formation and appropriate volume during tests separate strong zones from weak ones."
          },
          {
            question: "How should conflicting timeframe zones be handled?",
            options: [
              "Always trade the lower timeframe zone",
              "Always trade the higher timeframe zone",
              "Avoid trading until timeframes align or resolution occurs",
              "Trade both directions simultaneously"
            ],
            correct: 2,
            explanation: "When timeframes conflict (e.g., daily support vs 4H resistance), it's best to wait for resolution or clear alignment rather than fighting against higher timeframe trends."
          },
          {
            question: "What happens to zone reliability after multiple tests?",
            options: [
              "It increases with each test",
              "It stays the same regardless of tests",
              "It decreases with each test",
              "It only matters for weekly zones"
            ],
            correct: 2,
            explanation: "Zone reliability generally decreases with each test as it shows the level is under pressure. Multiple tests often lead to eventual breaks, so fresh zones typically offer the best probability."
          },
          {
            question: "What minimum risk-reward ratio should be used for zone trades?",
            options: [
              "1:1",
              "2:1",
              "3:1",
              "5:1"
            ],
            correct: 1,
            explanation: "A minimum 2:1 risk-reward ratio is essential for zone trading to account for the fact that not all zones will hold. This ensures profitability even with a 50% win rate."
          }
        ]
      },
      {
        id: 4,
        title: "Interactive Zone Drawing Exercise",
        description: "Practice identifying and drawing zones on real SPY/QQQ charts",
        imageUrl: "https://images.unsplash.com/photo-*************-9c2a0a7236a3?w=800&h=400&fit=crop",
        content: `
# Interactive Zone Drawing Mastery: Hands-On Practice

Apply your zone validation knowledge with guided practice on real SPY/QQQ charts. This interactive lesson will test your ability to identify, draw, and validate liquidity zones using professional techniques.

## Exercise Setup

### Chart Analysis Framework
You'll be analyzing real SPY and QQQ charts from recent trading sessions, identifying liquidity zones, and receiving immediate feedback on your zone placement accuracy.

**Tools You'll Practice With:**
- Volume analysis for zone validation
- Multiple timeframe confirmation
- Zone strength assessment
- Entry and exit planning

## Exercise 1: SPY Daily Zone Identification

### Chart Context: SPY Daily - Recent 3-Month Period

**Your Task:**
Identify and mark all significant liquidity zones on the SPY daily chart, then validate each zone using our professional framework.

**Step-by-Step Process:**

1. **Scan for High-Volume Reversal Areas**
   - Look for candles with 150%+ above average volume
   - Identify where significant reversals occurred
   - Mark potential support and resistance zones

2. **Apply the 3-Timeframe Rule**
   - Check weekly context for major levels
   - Confirm on 4-hour charts for precision
   - Ensure daily zones don't conflict with higher timeframes

3. **Validate Each Zone**
   - Volume confirmation during formation
   - Age and test frequency assessment
   - Market structure alignment check

**Common Student Mistakes to Avoid:**
- Drawing zones too narrow (single candle wicks)
- Ignoring volume context completely
- Missing obvious institutional levels
- Over-analyzing and marking too many zones

## Exercise 2: QQQ Intraday Zone Practice

### Chart Context: QQQ 4-Hour - Last 2 Weeks

**Your Task:**
Practice identifying intraday reversal zones and plan potential trades with proper risk management.

**Advanced Challenges:**
- Tech earnings impact on zone formation
- After-hours gap influence on zone placement
- Options expiration effects on zone behavior
- Correlation with broader market sentiment

**Zone Classification Exercise:**
Rate each identified zone from 1-10 based on:
- Volume confirmation (1-3 points)
- Timeframe confluence (1-3 points)
- Age and test history (1-2 points)
- Market structure alignment (1-2 points)

## Exercise 3: Real-Time Zone Monitoring

### Live Market Application

**Your Challenge:**
Using current market conditions, identify and monitor 3 high-probability zones on both SPY and QQQ.

**Monitoring Checklist:**
- [ ] Zone marked with precise boundaries
- [ ] Volume confirmation documented
- [ ] Timeframe confluence verified
- [ ] Risk-reward ratio calculated
- [ ] Entry and exit plan defined

**Real-Time Tracking:**
- Monitor price action as it approaches your zones
- Document actual vs expected behavior
- Note any institutional activity or news impact
- Adjust zone boundaries if market structure changes

## Exercise 4: Zone Trading Simulation

### Paper Trading Practice

**Scenario-Based Trading:**
Execute simulated trades based on your zone analysis using realistic market conditions.

**Trade Planning Template:**
```
Zone: [SPY/QQQ Level]
Direction: [Long/Short]
Entry: [Exact price]
Stop Loss: [Below/Above zone]
Target 1: [Next resistance/support]
Target 2: [Extended target]
Position Size: [Based on zone strength]
Risk-Reward: [Minimum 2:1]
```

**Performance Tracking:**
- Zone hit rate (% of zones that get touched)
- Trade win rate (% of profitable trades)
- Average risk-reward achieved
- Common mistake patterns identified

## Interactive Feedback System

### Immediate Accuracy Assessment

**Zone Placement Scoring:**
- **Excellent (90-100%)**: Zone boundaries align perfectly with institutional levels
- **Good (80-89%)**: Minor adjustments needed but overall correct
- **Acceptable (70-79%)**: Right concept but needs precision improvement
- **Needs Work (Below 70%)**: Fundamental zone identification issues

**Progressive Difficulty:**
- **Beginner**: Clear, obvious zones with strong volume confirmation
- **Intermediate**: Zones requiring timeframe analysis and validation
- **Advanced**: Complex market conditions with conflicting signals
- **Expert**: Real-time zone identification under pressure

## Common Zone Drawing Mistakes - Visual Examples

### Mistake #1: Zone Too Narrow
**Wrong:** Drawing zone around single candle wick
**Correct:** Zone encompasses the institutional activity area

### Mistake #2: Ignoring Volume
**Wrong:** Marking reversals without volume confirmation
**Correct:** Only marking high-volume institutional levels

### Mistake #3: Wrong Timeframe Priority
**Wrong:** Using 15-minute zones against daily trend
**Correct:** Respecting higher timeframe zone hierarchy

### Mistake #4: Over-Analysis
**Wrong:** Marking every minor swing as a zone
**Correct:** Only marking significant institutional levels

## Practice Performance Metrics

### Accuracy Targets:
- **Week 1**: 60% zone accuracy, focus on volume confirmation
- **Week 2**: 70% zone accuracy, add timeframe analysis
- **Week 3**: 80% zone accuracy, integrate market structure
- **Week 4**: 85%+ zone accuracy, real-time application

### Speed Targets:
- **Chart Scan**: Under 30 seconds for initial zone identification
- **Validation**: Under 2 minutes for complete zone analysis
- **Trade Plan**: Under 1 minute for entry/exit strategy

## Graduation Criteria

### Master Level Zone Drawing:
- [ ] 85%+ accuracy on zone placement
- [ ] Consistent volume validation application
- [ ] Proper timeframe hierarchy respect
- [ ] Real-time zone monitoring ability
- [ ] Profitable paper trading results

**Next Level:** Advance to real money trading with micro-positions to test zone analysis skills with actual market conditions.

## Interactive Challenge Questions

Test your understanding as you practice:

1. "This zone has low volume - should I trade it?"
2. "Daily shows resistance, 4H shows support - what do I do?"
3. "Price touched my zone but didn't bounce - is it invalidated?"
4. "I see 5 zones on this chart - should I mark them all?"
5. "The zone worked perfectly in backtest - why did it fail in real-time?"

**Pro Tip:** The best zone drawers spend 80% of their time waiting for the perfect setup and 20% actually drawing zones. Quality over quantity always wins in professional trading.
        `,
        type: "interactive",
        duration: "25 min",
        sections: [
          {
            title: "SPY Daily Zone Identification",
            duration: "8 min",
            keyPoints: [
              "High-volume reversal area identification",
              "3-timeframe rule application",
              "Professional zone validation process"
            ]
          },
          {
            title: "QQQ Intraday Zone Practice",
            duration: "6 min",
            keyPoints: [
              "Intraday reversal zone identification",
              "Tech sector specific considerations",
              "Zone strength classification system"
            ]
          },
          {
            title: "Real-Time Zone Monitoring",
            duration: "7 min",
            keyPoints: [
              "Live market zone tracking",
              "Real-time adjustment techniques",
              "Performance documentation methods"
            ]
          },
          {
            title: "Zone Trading Simulation",
            duration: "4 min",
            keyPoints: [
              "Paper trading execution",
              "Risk management application",
              "Performance tracking and improvement"
            ]
          }
        ],
        keyPoints: [
          "Real-time chart analysis builds pattern recognition skills",
          "Immediate feedback accelerates learning and identifies weak areas",
          "Common mistakes include narrow zones, ignoring volume, and over-analysis",
          "Progressive difficulty builds confidence from basic to expert level",
          "Performance metrics track accuracy, speed, and trading profitability"
        ],
        practicalExercises: [
          "Complete SPY daily zone identification on 3-month chart with 85% accuracy",
          "Practice QQQ 4-hour zone drawing for 2-week period with volume validation",
          "Monitor 3 live zones in real-time and document price action behavior",
          "Execute 10 paper trades based on zone analysis with 2:1 minimum R/R"
        ],
        quiz: [
          {
            question: "What's the most common mistake when drawing liquidity zones?",
            options: [
              "Making zones too wide",
              "Making zones too narrow around single candle wicks",
              "Using too much volume analysis",
              "Checking too many timeframes"
            ],
            correct: 1,
            explanation: "The most common mistake is drawing zones too narrow around single candle wicks instead of encompassing the entire institutional activity area where volume and rejection occurred."
          },
          {
            question: "How should conflicting timeframe zones be handled in practice?",
            options: [
              "Always prioritize the lower timeframe",
              "Trade both directions simultaneously",
              "Wait for timeframe alignment or resolution",
              "Ignore timeframe conflicts completely"
            ],
            correct: 2,
            explanation: "When timeframes conflict (e.g., daily resistance vs 4H support), it's best to wait for alignment or clear resolution rather than fighting against higher timeframe trends."
          },
          {
            question: "What accuracy target should beginners aim for in zone drawing?",
            options: [
              "95-100%",
              "85-90%",
              "70-80%",
              "50-60%"
            ],
            correct: 2,
            explanation: "Beginners should aim for 70-80% accuracy initially, focusing on volume confirmation and basic zone identification before advancing to more complex analysis."
          },
          {
            question: "When is a zone considered invalidated?",
            options: [
              "After being touched once",
              "When price closes through the zone with volume",
              "After 24 hours",
              "When volume decreases"
            ],
            correct: 1,
            explanation: "A zone is invalidated when price closes through it with significant volume, indicating the institutional level has been broken and is no longer respected."
          }
        ]
      }
    ]
  },
  {
    id: 2,
    title: "Fair Value Gaps (FVGs) Mastery",
    description: "Master Fair Value Gaps - the institutional footprints left by rapid price movements and how to trade them for consistent profits",
    icon: "BarChart3",
    color: "from-green-500 to-green-600",
    estimatedTime: "80 minutes",
    difficulty: "Intermediate",
    prerequisites: "Understanding of liquidity sweeps and market structure",
    learningObjectives: [
      "Identify and mark Fair Value Gaps with 95% accuracy",
      "Understand the difference between HTF and LTF FVGs",
      "Trade FVG fills and rejections for high-probability setups",
      "Master Inversion Fair Value Gaps (IFVGs) for advanced entries",
      "Combine FVGs with liquidity sweeps for confluence trading"
    ],
    lessons: [
      {
        id: 1,
        title: "Fair Value Gap Theory & Formation",
        description: "Deep dive into FVG formation, institutional causes, and market inefficiency concepts",
        content: `
# Fair Value Gaps: Institutional Footprints in Price Action

A Fair Value Gap (FVG) is a price range on a chart where an inefficient move occurred – essentially, a section where little or no trading took place. These gaps represent areas where fair value may have temporarily changed, and markets tend to revert to fill these inefficiencies over time.

## What is a Fair Value Gap?

**Definition:**
A Fair Value Gap appears within a three-candle sequence where one large momentum candle creates a "void" between the wick of the first candle and the wick of the third candle. The second candle (the big move) is so large that the third candle's low (in an up move) is still above the first candle's high, leaving a gap in between.

**Key Concept:**
This gap represents a price area where fair value may have temporarily changed – price zoomed in one direction without adequate two-way trading at those levels.

## The Three-Candle Rule

### Bullish FVG Formation:
1. **Candle 1**: Creates a high at a certain level
2. **Candle 2**: Large bullish candle that gaps up significantly
3. **Candle 3**: Low is still above Candle 1's high
4. **Result**: Gap between Candle 1 high and Candle 3 low = Bullish FVG

### Bearish FVG Formation:
1. **Candle 1**: Creates a low at a certain level
2. **Candle 2**: Large bearish candle that gaps down significantly
3. **Candle 3**: High is still below Candle 1's low
4. **Result**: Gap between Candle 1 low and Candle 3 high = Bearish FVG

## Why Do FVGs Matter?

### Market Inefficiency Theory
- FVGs highlight areas where price moved too fast
- Represent zones with insufficient two-way trading
- Markets have "memory" of unfilled orders in these ranges
- Price often returns to rebalance these inefficiencies

### Institutional Perspective
- Large orders couldn't be filled during rapid moves
- Institutions may have unfilled orders in FVG zones
- Smart money often waits for price to return to these levels
- FVGs act like magnets for future price action

## Types of Fair Value Gaps

### 1. Bullish FVG (Buy Side Imbalance)
- **Formation**: Left by strong upward movement
- **Expectation**: Acts as support when price returns
- **Trading**: Look for buying opportunities on first touch
- **Invalidation**: Price closes below the gap

### 2. Bearish FVG (Sell Side Imbalance)
- **Formation**: Left by strong downward movement
- **Expectation**: Acts as resistance when price returns
- **Trading**: Look for selling opportunities on first touch
- **Invalidation**: Price closes above the gap

## SPY/QQQ FVG Characteristics

### SPY FVG Patterns:
- Often form during earnings reactions
- News-driven gaps create large FVGs
- Options expiration can trigger FVG formation
- Market open gaps frequently leave FVGs

### QQQ FVG Patterns:
- Tech sector news creates significant FVGs
- Higher volatility = larger gap formations
- After-hours trading often leaves gaps
- Correlation with NASDAQ futures gaps

## FVG Validation Criteria

### Strong FVGs Have:
1. **Clean Formation**: Clear three-candle pattern
2. **Significant Size**: Gap represents meaningful price range
3. **Volume Context**: High volume on the gap-creating candle
4. **Timeframe Relevance**: Higher timeframes = stronger FVGs

### Weak FVGs Show:
- Overlapping wicks between candles
- Very small gap size
- Low volume on formation
- Multiple gaps in same area

## Real-World FVG Example

**SPY Bullish FVG Formation:**
1. **9:30 AM**: SPY opens at $450, creates high at $450.50
2. **9:31 AM**: Strong buying pushes SPY from $450.75 to $452.25
3. **9:32 AM**: Pullback finds support at $451.00
4. **Result**: Bullish FVG from $450.50 to $451.00

**Expected Behavior:**
- Price may return to $450.50-$451.00 zone
- First touch often provides buying opportunity
- Zone acts as support for future moves
- Invalidated if price closes below $450.50
        `,
        type: "theory",
        duration: "22 min",
        keyPoints: [
          "Fair Value Gaps represent price inefficiencies where insufficient two-way trading occurred",
          "FVGs form through the three-candle rule with clear gap between first and third candle wicks",
          "Bullish FVGs act as support zones, bearish FVGs act as resistance zones",
          "Higher timeframe FVGs are more significant and reliable than lower timeframe gaps",
          "SPY/QQQ FVGs often form during news events, market opens, and earnings reactions"
        ],
        practicalExercises: [
          "Identify 5 Fair Value Gaps on SPY daily chart using the three-candle rule",
          "Mark bullish and bearish FVGs with different colors on your charts",
          "Observe how price reacts when returning to previously identified FVG zones"
        ],
        quiz: [
          {
            question: "What defines a valid Fair Value Gap formation?",
            options: [
              "Any gap between two candles",
              "A three-candle pattern where the middle candle creates a gap between the first and third candle wicks",
              "A gap that forms at market open",
              "Any price movement with high volume"
            ],
            correct: 1,
            explanation: "A valid FVG requires a three-candle pattern where the large middle candle creates a clear gap between the first candle's high/low and the third candle's low/high."
          },
          {
            question: "How should a bullish FVG behave when price returns to it?",
            options: [
              "Price should break through immediately",
              "Price should act as resistance",
              "Price should find support and potentially bounce",
              "Price should create more gaps"
            ],
            correct: 2,
            explanation: "A bullish FVG should act as a support zone when price returns to it, as the gap represents an area where buyers may step in to fill the inefficiency."
          },
          {
            question: "When is a Fair Value Gap considered invalidated?",
            options: [
              "After one touch",
              "When price closes through the gap completely",
              "After 24 hours",
              "When volume decreases"
            ],
            correct: 1,
            explanation: "An FVG is invalidated when price closes completely through the gap, indicating the inefficiency has been filled and the zone no longer holds significance."
          }
        ]
      },
      {
        id: 2,
        title: "Higher Timeframe FVGs & Multi-Timeframe Analysis",
        description: "Master the power of Higher Timeframe Fair Value Gaps and learn to combine multiple timeframes for precision entries",
        content: `
# Higher Timeframe FVGs: The Institutional Magnets

Higher timeframe Fair Value Gaps are among the most powerful tools in a professional trader's arsenal. These gaps act like magnets, drawing price back to fill inefficiencies left by rapid institutional moves.

## Why Higher Timeframes Matter

### Significance Hierarchy:
- **Weekly FVGs**: Extremely powerful, may take months to fill
- **Daily FVGs**: Very significant, often filled within days/weeks
- **4-Hour FVGs**: Strong levels, usually filled within days
- **1-Hour FVGs**: Moderate significance, filled within hours/days
- **15-Min FVGs**: Lower significance, often filled quickly

### Volume and Participation:
Higher timeframe moves involve:
- More institutional participation
- Larger order sizes
- Greater market impact
- Broader market awareness
- Stronger magnetic effect

## HTF FVG vs LTF FVG Comparison

### Higher Timeframe FVGs (Daily+):
**Advantages:**
- Higher probability of being filled
- Stronger support/resistance when reached
- Better risk/reward opportunities
- Less noise and false signals
- Institutional relevance

**Characteristics:**
- Take longer to reach
- Provide major turning points
- Often align with other key levels
- Create significant price reactions

### Lower Timeframe FVGs (1H and below):
**Advantages:**
- More frequent opportunities
- Faster fills and reactions
- Good for scalping strategies
- Quick feedback on trades

**Disadvantages:**
- Higher noise ratio
- More false signals
- Weaker reactions
- Less institutional relevance

## Multi-Timeframe FVG Analysis

### The Professional Approach:
1. **Mark HTF FVGs first** (Daily, 4H, 1H)
2. **Use LTF for entry timing** (15M, 5M)
3. **Combine with other confluences**
4. **Prioritize HTF over LTF**

### Confluence Stacking:
**High-Probability Setup:**
- Daily FVG zone
- + Previous day high/low
- + Volume profile level
- + Liquidity sweep area
- = Maximum confluence

## SPY/QQQ HTF FVG Patterns

### SPY Daily FVG Characteristics:
- **Formation**: Often during earnings, Fed announcements, major news
- **Size**: Typically $2-8 gaps on daily charts
- **Fill Rate**: 85-90% eventually get filled
- **Timeframe**: Usually filled within 1-4 weeks
- **Reaction**: Strong bounces/rejections on first touch

### QQQ Daily FVG Characteristics:
- **Formation**: Tech earnings, guidance changes, sector rotation
- **Size**: Typically $3-12 gaps due to higher volatility
- **Fill Rate**: 80-85% eventually get filled
- **Timeframe**: May take longer due to trend strength
- **Reaction**: More volatile reactions, wider zones needed

## Trading HTF FVGs: The Professional Method

### Step 1: Identification
- Scan daily/4H charts for clean FVG formations
- Mark gap boundaries clearly
- Note the context (news, earnings, etc.)
- Assess gap size and significance

### Step 2: Patience
- Wait for price to approach the HTF FVG
- Don't chase - let the gap come to you
- Monitor lower timeframes for entry signals
- Prepare for potential strong reactions

### Step 3: Entry Timing
- Use 15M/5M charts for precise entries
- Look for additional confirmations:
  - Lower timeframe structure breaks
  - Volume increases
  - Candlestick patterns
  - Momentum divergences

### Step 4: Risk Management
- Stop loss beyond the FVG zone
- Take profits at logical levels
- Trail stops as trade develops
- Respect the power of HTF levels

## Real-World HTF FVG Example

**SPY Daily Bullish FVG Setup:**
- **Formation**: Fed announcement creates gap from $445-$448
- **Wait Period**: 2 weeks for price to return
- **Entry Signal**: 15M bullish engulfing at $446 (within FVG)
- **Confirmation**: Volume spike + break of 15M structure
- **Result**: Bounce to $452 for 1.3% profit
- **Risk**: Stop at $444 (below FVG) for 0.4% risk
- **R:R Ratio**: 3.25:1

## Key HTF FVG Rules

1. **Higher timeframe always wins** - HTF FVG overrides LTF signals
2. **First touch is strongest** - Best reactions occur on initial contact
3. **Partial fills are common** - Price may only fill 50-70% of gap
4. **Context matters** - Consider overall market trend and sentiment
5. **Patience pays** - Wait for proper setups, don't force trades
        `,
        type: "practical",
        duration: "25 min",
        keyPoints: [
          "Perfect liquidity sweeps follow a 4-phase pattern: approach, penetration, reversal, follow-through",
          "SPY sweeps typically extend 0.1-0.3% beyond levels with 20-50% above average volume",
          "Visual recognition includes wick formations, volume spikes, and immediate reversals",
          "Time-based patterns show highest probability during market open and close",
          "Multi-timeframe analysis provides confirmation and precise entry timing"
        ],
        practicalExercises: [
          "Identify and analyze 3 historical liquidity sweeps on SPY using the 4-phase pattern",
          "Mark 5 potential liquidity levels on current QQQ chart and monitor for sweep patterns",
          "Practice distinguishing between true sweeps and genuine breakouts using volume analysis",
          "Create alerts for price approaching identified liquidity levels for real-time practice"
        ],
        quiz: [
          {
            question: "What is the typical penetration distance for SPY liquidity sweeps?",
            options: [
              "1-2% beyond the level",
              "0.1-0.3% beyond the level",
              "5-10% beyond the level",
              "Exactly to the level"
            ],
            correct: 1,
            explanation: "SPY liquidity sweeps typically penetrate 0.1-0.3% beyond key levels - enough to trigger stops but not so much as to indicate a genuine breakout."
          },
          {
            question: "Which phase of a liquidity sweep shows the highest volume?",
            options: [
              "The approach phase",
              "The penetration phase",
              "The reversal phase",
              "The follow-through phase"
            ],
            correct: 2,
            explanation: "The reversal phase typically shows the highest volume as institutional orders enter the market after stops are triggered."
          }
        ]
      },
      {
        id: 3,
        title: "Trading Liquidity Sweeps",
        description: "How to position yourself to profit from sweep reversals",
        imageUrl: "https://images.unsplash.com/photo-1563013544-824ae1b704d3?w=800&h=400&fit=crop",
        content: `
# Trading Liquidity Sweeps: Capturing Institutional Reversals

Once you can identify liquidity sweeps with confidence, the next critical step is positioning yourself to profit from the inevitable reversal. This lesson covers the professional approach to entering, managing, and exiting sweep reversal trades.

## The Sweep Reversal Setup

### Understanding the Mechanics

**Why Sweeps Reverse:**
- Institutions have absorbed the available liquidity
- No more retail stops to trigger in that direction
- Smart money now has better fills and reverses direction
- Market efficiency demands price return to fair value

**The Professional Edge:**
While retail traders get stopped out during sweeps, professional traders position themselves for the reversal, using the retail pain as their profit opportunity.

## Entry Timing Strategies

### 1. The Immediate Reversal Entry

**Best For:** Strong, obvious sweeps with clear rejection

**Entry Criteria:**
- Sweep penetration confirmed (0.1-0.3% for SPY, 0.3-0.6% for QQQ)
- Immediate reversal candle formation
- Volume spike confirming institutional participation
- Price action showing clear rejection

**SPY Example:**
- SPY sweeps previous day high at $450.00, reaches $450.75
- Immediate bearish engulfing candle forms
- Volume doubles on the reversal candle
- Entry: $449.50 on break below reversal candle low

### 2. The Retest Entry

**Best For:** When you miss the immediate reversal

**Entry Strategy:**
- Wait for price to retest the swept level
- Look for rejection on lower volume
- Enter on break of retest low/high
- Safer but potentially smaller reward

**QQQ Example:**
- QQQ sweeps support at $375, drops to $373.50
- Price rallies back to retest $375 resistance
- Rejection occurs with decreasing volume
- Entry: $374.25 on break below retest low

### 3. The Structure Break Entry

**Best For:** Conservative traders wanting high confirmation

**Entry Requirements:**
- Sweep occurs and reverses
- Price breaks market structure in reversal direction
- Multiple timeframe confirmation
- Higher probability but may miss fastest moves

**Confirmation Levels:**
- Break of previous swing high/low
- Change of character (CHoCH) signal
- Multiple timeframe alignment
- Volume confirmation on structure break

## Stop Loss Placement Strategies

### 1. Beyond the Sweep High/Low

**Standard Approach:**
- Place stop 0.1-0.2% beyond the sweep extreme
- Accounts for false breakouts and whipsaws
- Gives trade room to breathe

**SPY Example:**
- Sweep reaches $450.75
- Stop loss at $451.00 (0.05% buffer)
- Risk: $1.50 per share

### 2. Structure-Based Stops

**Professional Method:**
- Stop beyond significant structure level
- May be wider but more logical
- Lower chance of being hit by noise

**Criteria:**
- Previous swing high/low
- Higher timeframe support/resistance
- Volume profile significant level
- Round number psychological level

### 3. Percentage-Based Stops

**For Volatile Conditions:**
- Use fixed percentage beyond entry
- Adjusts automatically for market volatility
- Consistent risk management approach

**Typical Ranges:**
- SPY: 0.3-0.5% beyond entry
- QQQ: 0.5-0.8% beyond entry
- Adjust based on current volatility

## Target Setting for Sweep Trades

### 1. Previous Structure Target

**Most Common Approach:**
- Target previous swing high/low
- Natural profit-taking level
- High probability of being reached

**Target Rationale:**
- Market memory of previous levels
- Institutional reference points
- Technical trader participation

### 2. Multiple Target Strategy

**Professional Risk Management:**

**Target 1 (50% position):** Near-term structure level
- Quick profit taking
- Reduces overall trade risk
- Locks in partial profits

**Target 2 (30% position):** Major structure level
- Larger profit potential
- Rides trending moves
- Captures extended moves

**Target 3 (20% position):** Extended target
- Maximum profit potential
- Captures momentum extremes
- Runner for exceptional moves

### 3. Risk-Reward Based Targets

**Minimum Standards:**
- 2:1 risk-reward ratio minimum
- 3:1 preferred for sweep trades
- Adjust based on market conditions

**Calculation Example:**
- Entry: $449.50
- Stop: $451.00 (Risk: $1.50)
- Target 1: $446.00 (Reward: $3.50 = 2.3:1)
- Target 2: $444.75 (Reward: $4.75 = 3.2:1)

## Advanced Sweep Trading Techniques

### 1. The Double Sweep Trade

**Setup:** Price sweeps, reverses, then sweeps again
**Strategy:** Fade the second sweep with higher confidence
**Edge:** Second sweep often creates exhaustion

### 2. Multi-Timeframe Sweep Confluence

**Approach:** Look for sweeps aligning across timeframes
**Example:** Daily sweep + 4H sweep + 1H sweep
**Result:** Extremely high probability reversal setup

### 3. News-Driven Sweep Fades

**Opportunity:** Market overreactions to news events
**Strategy:** Fade sweeps that occur on news spikes
**Timing:** Wait for initial volatility to subside

## Risk Management for Sweep Trades

### Position Sizing Guidelines

**Conservative (1-2% account risk):**
- New to sweep trading
- Learning phase
- Uncertain market conditions

**Standard (2-3% account risk):**
- Experienced sweep trader
- High-confidence setups
- Normal market conditions

**Aggressive (3-4% account risk):**
- Expert level trader
- Exceptional setups
- Favorable market environment

### Trade Management Rules

**Always:**
- Move stop to breakeven after 1:1 reward
- Take partial profits at logical levels
- Trail stop on extended moves
- Document all trades for review

**Never:**
- Risk more than planned amount
- Hold through opposite sweep
- Ignore stop loss levels
- Add to losing positions

## Common Sweep Trading Mistakes

### 1. Entering Too Early
**Problem:** Jumping in before sweep completion
**Solution:** Wait for full sweep and reversal confirmation

### 2. Stop Too Tight
**Problem:** Getting stopped by normal price action
**Solution:** Use proper buffer beyond sweep level

### 3. Target Too Aggressive
**Problem:** Missing profits due to unrealistic targets
**Solution:** Take profits at logical structure levels

### 4. Ignoring Market Context
**Problem:** Trading sweeps against major trends
**Solution:** Consider higher timeframe direction

### 5. Poor Position Sizing
**Problem:** Risking too much on single trade
**Solution:** Consistent risk management approach

## Real-World Trading Examples

### Example 1: SPY Bearish Sweep Trade

**Setup:** SPY breaks above previous day high during morning session
- **Sweep Level:** $450.25 (previous day high)
- **Penetration:** Reaches $450.85 (0.13% penetration)
- **Reversal Signal:** Bearish engulfing candle with 2x volume
- **Entry:** $449.75 on break of reversal low
- **Stop:** $451.25 (0.30% beyond sweep high)
- **Target 1:** $447.50 (previous 4H support)
- **Target 2:** $445.75 (daily support level)

**Outcome:** Target 1 hit in 2 hours, Target 2 hit next day
**Risk-Reward:** 1.5 risk for 4.75 total reward = 3.2:1

### Example 2: QQQ Bullish Sweep Trade

**Setup:** QQQ breaks below weekly support during tech earnings week
- **Sweep Level:** $374.50 (weekly support)
- **Penetration:** Reaches $373.75 (0.20% penetration)
- **Reversal Signal:** Bullish hammer with volume spike
- **Entry:** $375.25 on break above hammer high
- **Stop:** $373.25 (below sweep low)
- **Target 1:** $378.00 (4H resistance)
- **Target 2:** $381.50 (daily resistance)

**Outcome:** Target 1 hit same day, Target 2 hit in 3 days
**Risk-Reward:** 2.0 risk for 6.25 total reward = 3.1:1

## Psychology of Sweep Trading

### Mental Framework

**Patience:** Wait for high-quality setups
**Discipline:** Follow your rules exactly
**Confidence:** Trust your analysis when conditions align
**Flexibility:** Adapt to changing market conditions

### Emotional Management

**FOMO:** Don't chase missed sweeps
**Revenge Trading:** Don't trade after losses
**Overconfidence:** Maintain consistent position sizing
**Fear:** Take profits according to plan

**Professional Mindset:** "I'm not predicting the market, I'm reacting to institutional behavior with a statistical edge."

## Advanced Performance Metrics

### Track These Statistics:
- Sweep identification accuracy (% of real sweeps caught)
- Entry timing quality (immediate vs delayed)
- Stop loss hit rate (% of trades stopped out)
- Target achievement rate (% reaching each target)
- Average risk-reward achieved
- Monthly profitability from sweep trades

### Improvement Goals:
- 70%+ sweep identification accuracy
- 85%+ proper entry execution
- <25% stop loss hit rate
- 2.5:1+ average risk-reward
- Consistent monthly profitability

**Mastery Level:** When you can consistently identify and trade sweeps with 75%+ accuracy and 3:1+ average risk-reward across all market conditions.
        `,
        type: "strategy",
        duration: "28 min",
        sections: [
          {
            title: "Entry Timing Strategies",
            duration: "8 min",
            keyPoints: [
              "Immediate reversal entry for strong sweeps",
              "Retest entry for missed opportunities",
              "Structure break entry for high confirmation"
            ]
          },
          {
            title: "Stop Loss Placement Methods",
            duration: "6 min",
            keyPoints: [
              "Beyond sweep high/low placement",
              "Structure-based stop strategies",
              "Percentage-based stops for volatility"
            ]
          },
          {
            title: "Target Setting and Management",
            duration: "8 min",
            keyPoints: [
              "Previous structure target approach",
              "Multiple target strategy for optimization",
              "Risk-reward based target calculation"
            ]
          },
          {
            title: "Advanced Techniques and Psychology",
            duration: "6 min",
            keyPoints: [
              "Double sweep and multi-timeframe confluence",
              "Risk management and position sizing",
              "Common mistakes and emotional management"
            ]
          }
        ],
        keyPoints: [
          "Entry timing after sweep completion determines trade success",
          "Stop loss placement strategies balance safety with profit potential",
          "Target setting for sweep trades requires multiple structure levels",
          "Position sizing and risk management are crucial for long-term success",
          "Professional psychology focuses on statistical edge over predictions"
        ],
        practicalExercises: [
          "Identify 5 recent SPY liquidity sweeps and plan optimal entry/exit strategies",
          "Practice the three entry timing methods on paper trades for one week",
          "Calculate risk-reward ratios for 10 potential sweep reversal setups",
          "Track and analyze your sweep trading performance metrics for improvement"
        ],
        quiz: [
          {
            question: "What is the best entry timing for trading liquidity sweeps?",
            options: [
              "Immediately when the sweep starts",
              "After sweep completion and reversal confirmation",
              "During the penetration phase",
              "Before the sweep occurs"
            ],
            correct: 1,
            explanation: "The best entry timing is after sweep completion and reversal confirmation, ensuring the liquidity grab has occurred and institutional money is entering in the opposite direction."
          },
          {
            question: "Where should stop losses be placed for sweep reversal trades?",
            options: [
              "At the original sweep level",
              "0.1-0.3% beyond the sweep extreme with appropriate buffer",
              "At a fixed dollar amount",
              "At the previous day's close"
            ],
            correct: 1,
            explanation: "Stop losses should be placed 0.1-0.3% beyond the sweep extreme to account for normal market noise while protecting against genuine breakouts."
          },
          {
            question: "What is the minimum risk-reward ratio for sweep trades?",
            options: [
              "1:1",
              "2:1",
              "3:1",
              "5:1"
            ],
            correct: 1,
            explanation: "A minimum 2:1 risk-reward ratio is essential for sweep trading profitability, with 3:1 being preferred to account for the natural win rate of these setups."
          },
          {
            question: "What characterizes a high-quality sweep reversal setup?",
            options: [
              "Large penetration distance",
              "Low volume on the sweep",
              "Immediate reversal with volume confirmation",
              "Multiple sweeps in the same direction"
            ],
            correct: 2,
            explanation: "High-quality sweep reversals show immediate reversal with volume confirmation, indicating strong institutional participation in the opposite direction."
          }
        ]
      },
      {
        id: 4,
        title: "Sweep Analysis Workshop",
        description: "Analyze real SPY/QQQ liquidity sweeps with expert commentary",
        content: "Review historical examples of successful sweep trades",
        type: "interactive",
        duration: "7 min",
        keyPoints: [
          "Case study analysis",
          "Pattern recognition practice",
          "Risk management examples"
        ]
      }
    ]
  },
  {
    id: 3,
    title: "Confirmation Stacking & Multi-Factor Analysis",
    description: "Master the art of stacking multiple confirmations for high-probability trades. Learn to combine price action, volume, and technical analysis for professional-level precision.",
    icon: "Layers",
    color: "from-purple-500 to-purple-600",
    estimatedTime: "85 minutes",
    difficulty: "Advanced",
    prerequisites: "Understanding of liquidity sweeps and Fair Value Gaps",
    learningObjectives: [
      "Stack 3+ confirmations for every trade setup",
      "Master market structure confirmations (BOS/CHoCH)",
      "Integrate volume profile and order flow analysis",
      "Combine multiple timeframes for precision entries",
      "Develop a systematic approach to trade validation"
    ],
    lessons: [
      {
        id: 1,
        title: "Confirmation Stacking Fundamentals",
        description: "Learn the professional approach to stacking multiple confirmations for high-probability trade setups",
        content: `
# Confirmation Stacking: The Professional Edge

Even when you have a strong level or setup in mind (be it a liquidity sweep or an FVG), jumping in without confirmation can be risky. Confirmation stacking means waiting for multiple signals to line up in your favor before committing to a trade.

## The Philosophy of Confluence

**Core Principle:**
Rather than relying on a single indicator or one pattern, you look for an agreement among several independent clues – what traders often call confluence. The idea is to filter out low-quality setups and only act when many things point to the same conclusion.

**Think of it this way:**
Each confirmation is like a piece of a puzzle. One piece alone doesn't show the whole picture, but when several pieces fit together, you have a clearer image of where price might go.

## The Five Pillars of Confirmation

### 1. Market Structure & Price Action
This refers to analyzing how price swings (highs and lows) are behaving to confirm a trend change or continuation.

**Break of Structure (BOS):**
- Price takes out a significant previous high or low in the direction of a trend
- Confirms that trend's strength
- Shows institutional participation

**Change of Character (CHoCH):**
- Early sign of a possible trend reversal
- First break of a minor swing level against the trend
- Indicates potential shift in market sentiment

**Example:** If QQQ has been making higher highs and higher lows (uptrend) and then suddenly makes a lower low, that's a bearish CHoCH signaling the uptrend may be done.

### 2. Volume Profile & Range Context
Volume Profile shows how volume has been distributed at each price, giving insight into what prices the market deems "fair" vs "extreme."

**Key Elements:**
- **Point of Control (POC):** Price with highest traded volume
- **Value Area (VA):** Price range where ~70% of volume occurred
- **Value Area High/Low (VAH/VAL):** Boundaries of fair value

**Application:** If SPY rejects from yesterday's Value Area High after a liquidity sweep, that's confluence supporting a reversal trade.

### 3. Order Flow Tools (Advanced)
Real-time confirmation of what's happening under the hood through futures DOM, time and sales, or heatmap platforms.

**Signals to Watch:**
- Absorption of selling/buying at key levels
- Cumulative volume delta divergences
- Large limit orders on the book
- Aggressive vs passive order flow

### 4. Indicators & Overlays
Traditional technical indicators can be part of your confirmation stack, especially ones that measure trend or mean reversion.

**VWAP (Volume Weighted Average Price):**
- Intraday equilibrium level
- Reclaiming VWAP after a sweep adds confidence
- Acts as dynamic support/resistance

**Moving Averages:**
- 21 EMA, 50 EMA for trend confirmation
- Dynamic support on pullbacks
- Confluence with other levels

### 5. Liquidity & HTF Levels
Combining liquidity sweeps and HTF FVGs as part of confirmation checklist.

**High-Probability Setup:**
- Liquidity sweep at HTF FVG
- + Market structure confirmation
- + Volume profile level
- + VWAP reclaim
- = Maximum confluence

## The Professional Confirmation Checklist

### Minimum Requirements:
**For Entry:** At least 2-3 solid confirmations
**For High-Conviction Trades:** 4+ confirmations aligned

### Example Checklist:
1. ✓ Liquidity sweep occurred
2. ✓ Price action confirmation (CHoCH/BOS)
3. ✓ Volume profile level confluence
4. ✓ HTF FVG zone
5. ✓ VWAP reclaim/rejection

## Real-World Confirmation Stacking Example

### SPY Bearish Setup:
**Setup:** SPY approaches yesterday's high at $450

**Confirmations:**
1. **Liquidity Sweep:** Price hits $450.50, sweeps stops
2. **HTF Level:** Daily bearish FVG zone at $450-451
3. **Price Action:** 5-minute bearish engulfing + CHoCH
4. **Volume Profile:** Rejection from yesterday's VAH
5. **Volume:** Spike on sweep, sustained on reversal

**Entry:** Short at $449.50 after all confirmations align
**Stop:** $451 (above sweep high)
**Target:** $445 (previous support)
**Result:** 1% profit with 0.3% risk = 3.3:1 R/R

## Avoiding Analysis Paralysis

### Balance is Key:
- Too few confirmations = low probability
- Too many confirmations = missed opportunities
- Sweet spot: 2-3 strong confirmations

### Weighting Confirmations:
**Primary (Must Have):**
- Price action signal (structure break)
- Key level confluence (liquidity/FVG)

**Secondary (Nice to Have):**
- Volume confirmation
- Indicator alignment
- Higher timeframe context

## Common Confirmation Mistakes

1. **Forcing Confluence:** Seeing confirmations that aren't really there
2. **Over-Analysis:** Requiring too many signals
3. **Ignoring Context:** Not considering overall market environment
4. **Static Thinking:** Not adapting to changing market conditions
5. **Confirmation Bias:** Only seeing signals that support your bias
- Unfilled orders create demand/supply
- Technical traders target gap fills
- Self-fulfilling prophecy effect

## FVG vs. Regular Gaps

### Fair Value Gaps:
- Formed by 3-candle pattern
- Represent order flow imbalance
- High probability of fill (70-80%)
- Can be traded in both directions
- Show institutional activity

### Regular Price Gaps:
- Formed between sessions (overnight)
- Caused by news or events
- Lower probability of fill (40-60%)
- Often indicate trend continuation
- May not represent institutional flow

## Psychological Aspects of FVG Trading

### Institutional Perspective:
- "We moved price too fast"
- "Need to fill remaining orders"
- "Better prices available in the gap"
- "Risk management requires rebalancing"

### Retail Perspective:
- "Price gapped away from me"
- "I missed the move"
- "Will it come back?"
- "Should I chase or wait?"

## Real-World FVG Examples

### Example 1: SPY Bullish FVG
**Setup:** Fed announcement creates buying surge
**Formation:** 3-candle bullish FVG at $445-$447
**Fill:** Price returns to gap 5 days later
**Outcome:** Perfect bounce from gap support

### Example 2: QQQ Bearish FVG
**Setup:** Tech earnings disappointment
**Formation:** 3-candle bearish FVG at $380-$382
**Fill:** Price rallies to gap 2 weeks later
**Outcome:** Strong resistance at gap level

## Advanced FVG Concepts

### 1. Nested FVGs
- Multiple gaps within larger gaps
- Provide multiple trading opportunities
- Show sustained institutional activity
- Require careful order management

### 2. FVG Clusters
- Multiple gaps in same price area
- Extremely high probability zones
- Often mark major support/resistance
- Institutional accumulation/distribution areas

### 3. Partial vs. Full Fills
- **Partial Fill:** Price touches gap but doesn't close it
- **Full Fill:** Price completely closes the gap
- **Overfill:** Price extends beyond the gap
- Each has different trading implications

## Common FVG Mistakes

1. **Trading Every Gap:** Not all FVGs are equal quality
2. **Ignoring Context:** Market structure matters
3. **Poor Risk Management:** Gaps can extend before filling
4. **Wrong Timeframe:** Match timeframe to trading style
5. **Emotional Trading:** FOMO on gap formations
        `,
        type: "theory",
        duration: "25 min",
        keyPoints: [
          "Fair Value Gaps represent institutional order flow imbalances created by overwhelming buying/selling pressure",
          "FVGs require exactly 3 candles with no overlap between outer candles' high/low",
          "SPY FVGs typically range 0.2-0.8% while QQQ ranges 0.3-1.2% due to higher volatility",
          "70-80% of FVGs get filled within 5-20 sessions as markets seek price efficiency",
          "Inversion FVGs become powerful support/resistance after being filled"
        ],
        practicalExercises: [
          "Identify 5 bullish and 5 bearish FVGs on SPY 30-minute chart from last month",
          "Measure the size of each FVG as percentage of price and compare to typical ranges",
          "Track which FVGs got filled and calculate the fill rate for your sample",
          "Practice distinguishing between FVGs and regular overnight gaps"
        ],
        quiz: [
          {
            question: "How many candles are required to form a Fair Value Gap?",
            options: [
              "2 candles",
              "3 candles",
              "4 candles",
              "5 candles"
            ],
            correct: 1,
            explanation: "A Fair Value Gap requires exactly 3 consecutive candles, with the middle candle creating the imbalance and no overlap between the outer candles."
          },
          {
            question: "What percentage of Fair Value Gaps typically get filled?",
            options: [
              "30-40%",
              "50-60%",
              "70-80%",
              "90-100%"
            ],
            correct: 2,
            explanation: "Approximately 70-80% of Fair Value Gaps get filled as markets naturally seek price efficiency and institutional orders get completed."
          }
        ]
      },
      {
        id: 2,
        title: "FVG Classification System",
        description: "Learn to classify FVGs by strength and probability",
        imageUrl: "https://images.unsplash.com/photo-1559526324-4b87b5e36e44?w=800&h=400&fit=crop",
        content: `
# FVG Classification System: Separating High-Probability from Low-Probability Gaps

Not all Fair Value Gaps are created equal. The ability to classify FVGs by strength and probability is what separates professional traders from amateurs. This systematic approach will help you focus on only the highest-quality setups.

## The Professional FVG Classification Framework

### Tier 1: Institutional Grade FVGs (Highest Probability)

**Characteristics:**
- Formed on daily timeframe or higher
- Created with 200%+ above average volume
- Size represents 0.5-2% of current price
- Clean three-candle formation with no overlapping wicks
- Occurs at major institutional levels (previous highs/lows, round numbers)

**SPY Example:** Daily FVG formed during Fed announcement between $445-$448 with 300% volume spike

**Success Rate:** 85-90% eventually filled
**Priority:** Trade these immediately when price approaches

### Tier 2: High-Quality Professional FVGs

**Characteristics:**
- Formed on 4-hour or daily timeframes
- Created with 150%+ above average volume
- Size represents 0.3-1% of current price
- Good formation quality with minimal overlap
- Aligns with higher timeframe structure

**QQQ Example:** 4-hour FVG at $375-$377 formed during tech earnings week with strong volume

**Success Rate:** 75-80% eventually filled
**Priority:** Strong consideration for trading setups

### Tier 3: Standard Trade-Worthy FVGs

**Characteristics:**
- Formed on 1-hour to 4-hour timeframes
- Created with 120%+ above average volume
- Size represents 0.2-0.7% of current price
- Decent formation quality
- Some confluence with other levels

**Success Rate:** 65-75% eventually filled
**Priority:** Trade with confluence confirmation

### Tier 4: Lower-Probability FVGs (Avoid or Scale Down)

**Characteristics:**
- Formed on 15-minute to 1-hour timeframes
- Average or below-average volume
- Size less than 0.2% of current price
- Poor formation quality or overlapping wicks
- No significant confluence

**Success Rate:** 50-60% filled
**Priority:** Avoid or use for scalping only

## Size Classification System

### SPY FVG Size Guidelines:
- **Large**: $2.00+ gap (Institutional level - highest priority)
- **Medium**: $1.00-$2.00 gap (Professional level - high priority)
- **Small**: $0.50-$1.00 gap (Standard level - moderate priority)
- **Micro**: Under $0.50 gap (Low priority - scalping only)

### QQQ FVG Size Guidelines:
- **Large**: $3.00+ gap (Institutional level - highest priority)
- **Medium**: $1.50-$3.00 gap (Professional level - high priority)
- **Small**: $0.75-$1.50 gap (Standard level - moderate priority)
- **Micro**: Under $0.75 gap (Low priority - scalping only)

## Volume Classification Framework

### Volume Multiplier Analysis:
- **Extreme Volume** (300%+ average): Highest probability, institutional participation
- **High Volume** (200-300% average): Very high probability, strong professional interest
- **Elevated Volume** (150-200% average): High probability, moderate professional interest
- **Above Average** (120-150% average): Moderate probability, some institutional activity
- **Average or Below** (Under 120% average): Low probability, avoid trading

### Volume Context Considerations:
- Volume should increase during gap formation
- Volume should be higher than previous 10-period average
- Sustained volume after gap formation indicates institutional commitment
- Declining volume after formation suggests weak hands

## Context and Confluence Scoring

### Rate Each Factor (1-5 points):

**1. Market Structure Alignment:**
- 5 points: Perfect alignment with major swing levels
- 4 points: Good alignment with intermediate levels
- 3 points: Some structural relevance
- 2 points: Minor structural significance
- 1 point: No structural alignment

**2. News/Event Context:**
- 5 points: Major market-moving news (Fed, earnings, etc.)
- 4 points: Significant sector news
- 3 points: Minor news or data release
- 2 points: Some news context
- 1 point: No relevant news

**3. Time of Formation:**
- 5 points: Market open, close, or major session overlap
- 4 points: Active trading hours
- 3 points: Normal trading hours
- 2 points: Slower trading periods
- 1 point: Off-hours or low-activity periods

**4. Higher Timeframe Context:**
- 5 points: Aligns with weekly/monthly levels
- 4 points: Aligns with daily levels
- 3 points: Some higher timeframe relevance
- 2 points: Minor higher timeframe alignment
- 1 point: No higher timeframe alignment

### Total Classification Score:
- **18-20 points**: Tier 1 (Trade immediately)
- **15-17 points**: Tier 2 (High priority)
- **12-14 points**: Tier 3 (Standard consideration)
- **8-11 points**: Tier 4 (Lower priority)
- **Below 8 points**: Avoid trading

## Multiple Timeframe FVG Analysis

### The Professional Hierarchy:

**1. Weekly FVGs** (Highest Priority)
- Only trade in direction of weekly FVG
- Extremely high probability of being filled
- May take weeks to months to fill
- Overrides all lower timeframe signals

**2. Daily FVGs** (Very High Priority)
- Major institutional levels
- High probability fills within days to weeks
- Strong influence on intraday price action
- Should align with weekly direction

**3. 4-Hour FVGs** (High Priority)
- Professional trader reference points
- Good probability fills within hours to days
- Useful for swing trading setups
- Should not conflict with daily direction

**4. 1-Hour FVGs** (Moderate Priority)
- Intraday institutional levels
- Moderate probability fills within hours
- Good for day trading setups
- Subordinate to higher timeframes

**5. 15-Minute FVGs** (Lower Priority)
- Scalping and precision entry timing
- Lower probability fills
- Quick fills but less reliable
- Use only for entry refinement

## Advanced Classification Techniques

### 1. Age-Based Classification:
- **Fresh** (0-1 days old): Highest probability
- **Recent** (2-5 days old): High probability
- **Aged** (1-2 weeks old): Moderate probability
- **Stale** (2+ weeks old): Lower probability but can be very powerful

### 2. Test History Classification:
- **Untested**: Maximum probability on first touch
- **Tested Once**: Still high probability
- **Multiple Tests**: Decreasing probability with each test
- **Failed Tests**: Invalidated, avoid

### 3. Market Regime Classification:
- **Trending Markets**: FVGs in trend direction have higher probability
- **Range-bound Markets**: FVGs at range extremes most reliable
- **Volatile Markets**: Larger FVGs required for significance
- **Low Volatility**: Smaller FVGs can be significant

## Real-World Classification Examples

### Example 1: Tier 1 SPY FVG
- **Timeframe**: Daily
- **Size**: $446-$449 ($3 gap)
- **Volume**: 350% above average (Fed announcement)
- **Context**: Break of major weekly resistance
- **Structure**: Perfect alignment with monthly pivot
- **Score**: 19/20 points
- **Outcome**: Filled within 3 days for perfect reversal

### Example 2: Tier 3 QQQ FVG
- **Timeframe**: 1-hour
- **Size**: $378-$379 ($1 gap)
- **Volume**: 140% above average
- **Context**: Minor tech sector rotation
- **Structure**: Some alignment with 4H level
- **Score**: 13/20 points
- **Outcome**: Filled partially after 2 days

### Example 3: Tier 4 SPY FVG (Avoid)
- **Timeframe**: 15-minute
- **Size**: $450.20-$450.50 ($0.30 gap)
- **Volume**: 90% of average
- **Context**: No significant news
- **Structure**: No meaningful alignment
- **Score**: 6/20 points
- **Outcome**: Never filled, price continued away

## Common Classification Mistakes

### 1. Timeframe Bias
**Mistake**: Overweighting lower timeframe FVGs
**Solution**: Always prioritize higher timeframe gaps

### 2. Size Obsession
**Mistake**: Only trading the largest gaps
**Solution**: Consider context - small gaps can be significant in low volatility

### 3. Volume Ignorance
**Mistake**: Trading gaps formed on low volume
**Solution**: Always check volume context before trading

### 4. Context Blindness
**Mistake**: Trading gaps without market structure consideration
**Solution**: Always assess confluence and alignment

### 5. Recency Bias
**Mistake**: Only focusing on recent gaps
**Solution**: Also monitor aged, unfilled gaps for surprise moves

## Professional FVG Scanning Workflow

### Daily Routine:
1. **Scan Weekly/Daily charts** for new institutional-grade FVGs
2. **Classify all gaps** using the scoring system
3. **Prioritize Tier 1 and 2 gaps** for trading consideration
4. **Update gap database** with fill status and notes
5. **Plan alerts** for price approaching high-priority gaps

### Intraday Monitoring:
1. **Monitor price action** approaching classified gaps
2. **Assess real-time conditions** for trade entry
3. **Document actual vs predicted behavior**
4. **Adjust classification accuracy** based on outcomes

**Professional Insight**: The best FVG traders spend more time classifying and prioritizing gaps than actually trading them. Quality over quantity always wins in gap trading.
        `,
        type: "practical",
        duration: "22 min",
        sections: [
          {
            title: "Professional FVG Classification Framework",
            duration: "6 min",
            keyPoints: [
              "Four-tier classification system from institutional to low-probability",
              "Success rate ranges from 85-90% (Tier 1) to 50-60% (Tier 4)",
              "Volume and timeframe requirements for each tier"
            ]
          },
          {
            title: "Size and Volume Classification",
            duration: "5 min",
            keyPoints: [
              "SPY gap sizes: Large ($2+), Medium ($1-2), Small ($0.50-1), Micro (<$0.50)",
              "QQQ gap sizes adjusted for higher volatility",
              "Volume multiplier analysis for probability assessment"
            ]
          },
          {
            title: "Context and Confluence Scoring",
            duration: "6 min",
            keyPoints: [
              "20-point scoring system across four factors",
              "Market structure, news context, timing, and timeframe alignment",
              "Score thresholds for each classification tier"
            ]
          },
          {
            title: "Multiple Timeframe Analysis",
            duration: "5 min",
            keyPoints: [
              "Timeframe hierarchy: Weekly > Daily > 4H > 1H > 15M",
              "Age-based and test history classifications",
              "Market regime considerations for classification"
            ]
          }
        ],
        keyPoints: [
          "High probability vs low probability gaps",
          "Size and context importance",
          "Multiple timeframe FVG analysis"
        ],
        practicalExercises: [
          "Classify 10 recent SPY FVGs using the four-tier system",
          "Create a scoring sheet and rate 5 current QQQ gaps",
          "Practice the multiple timeframe hierarchy on live charts",
          "Build a database of classified gaps and track their fill rates"
        ],
        quiz: [
          {
            question: "What makes a Tier 1 institutional-grade FVG?",
            options: [
              "Any gap formed on the 15-minute timeframe",
              "Daily+ timeframe, 200%+ volume, 0.5-2% size, clean formation",
              "Only gaps larger than $5",
              "Gaps formed during lunch hours"
            ],
            correct: 1,
            explanation: "Tier 1 FVGs require daily+ timeframe formation, 200%+ above average volume, significant size (0.5-2% of price), and clean formation quality at institutional levels."
          },
          {
            question: "What is the typical success rate for Tier 1 FVGs?",
            options: [
              "50-60%",
              "65-75%",
              "75-80%",
              "85-90%"
            ],
            correct: 3,
            explanation: "Tier 1 institutional-grade FVGs have an 85-90% success rate of eventually being filled due to their strong institutional significance and high-quality formation."
          },
          {
            question: "For SPY, what size gap qualifies as 'Large' in the classification system?",
            options: [
              "Over $0.50",
              "Over $1.00",
              "Over $2.00",
              "Over $5.00"
            ],
            correct: 2,
            explanation: "For SPY, gaps over $2.00 qualify as 'Large' and represent institutional-level significance with the highest trading priority."
          },
          {
            question: "Which timeframe FVGs have the highest priority in the professional hierarchy?",
            options: [
              "15-minute FVGs",
              "1-hour FVGs",
              "Daily FVGs",
              "Weekly FVGs"
            ],
            correct: 3,
            explanation: "Weekly FVGs have the highest priority as they represent major institutional positioning and override all lower timeframe signals."
          }
        ]
      },
      {
        id: 3,
        title: "Trading FVG Fills",
        description: "Strategies for trading when price returns to fill gaps",
        content: "FVG fills often provide excellent trading opportunities...",
        type: "strategy",
        duration: "16 min",
        keyPoints: [
          "Partial vs full gap fills",
          "Entry and exit strategies",
          "Combining FVGs with other confluences"
        ]
      },
      {
        id: 4,
        title: "FVG Recognition Challenge",
        description: "Test your ability to spot and classify FVGs in real-time",
        content: "Interactive challenge to identify FVGs on live charts",
        type: "interactive",
        duration: "7 min",
        keyPoints: [
          "Speed recognition drills",
          "Classification accuracy",
          "Real-time decision making"
        ]
      }
    ]
  },
  {
    id: 4,
    title: "Volume Analysis & Confirmation",
    description: "Use volume analysis to confirm your price action signals",
    icon: "Activity",
    color: "from-orange-500 to-orange-600",
    estimatedTime: "40 minutes",
    difficulty: "Beginner",
    lessons: [
      {
        id: 1,
        title: "Volume Fundamentals",
        description: "Understanding volume and its relationship to price movement",
        content: "Volume is the fuel that drives price movement...",
        type: "theory",
        duration: "10 min",
        keyPoints: [
          "Volume precedes price",
          "Accumulation vs distribution patterns",
          "Volume profile concepts"
        ]
      },
      {
        id: 2,
        title: "Volume at Key Levels",
        description: "Analyzing volume behavior at support/resistance zones",
        content: "How volume behaves at key levels tells us about market sentiment...",
        type: "practical",
        duration: "15 min",
        keyPoints: [
          "Rising volume on approach to zones",
          "Fading volume and false breakouts",
          "Climactic volume patterns"
        ]
      },
      {
        id: 3,
        title: "Volume Confirmation Strategies",
        description: "Using volume to confirm your trading signals",
        content: "Volume confirmation can significantly improve trade success rates...",
        type: "strategy",
        duration: "12 min",
        keyPoints: [
          "Volume divergence signals",
          "Confirmation vs contradiction",
          "Multiple timeframe volume analysis"
        ]
      },
      {
        id: 4,
        title: "Volume Analysis Practice",
        description: "Practice reading volume patterns on SPY/QQQ charts",
        content: "Hands-on practice with volume analysis techniques",
        type: "interactive",
        duration: "3 min",
        keyPoints: [
          "Pattern recognition",
          "Signal confirmation practice",
          "Real-world application"
        ]
      }
    ]
  },
  {
    id: 5,
    title: "Confirmation Stacking",
    description: "Learn to stack multiple confirmations for high-probability trades",
    icon: "Layers",
    color: "from-red-500 to-red-600",
    estimatedTime: "55 minutes",
    difficulty: "Advanced",
    lessons: [
      {
        id: 1,
        title: "The Stacking Methodology",
        description: "Understanding the concept of confirmation stacking",
        content: "Confirmation stacking involves combining multiple technical signals...",
        type: "theory",
        duration: "12 min",
        keyPoints: [
          "Quality over quantity in confirmations",
          "Weighted confirmation systems",
          "Avoiding analysis paralysis"
        ]
      },
      {
        id: 2,
        title: "Building Your Stack",
        description: "How to systematically build confirmation stacks",
        content: "Learn the systematic approach to building robust confirmation stacks...",
        type: "practical",
        duration: "18 min",
        keyPoints: [
          "Primary vs secondary confirmations",
          "Timeframe hierarchy",
          "Confluence zone identification"
        ]
      },
      {
        id: 3,
        title: "Advanced Stacking Techniques",
        description: "Professional-level confirmation stacking strategies",
        content: "Advanced techniques used by professional traders...",
        type: "strategy",
        duration: "20 min",
        keyPoints: [
          "Multi-timeframe stacking",
          "Intermarket confirmations",
          "Sentiment-based confirmations"
        ]
      },
      {
        id: 4,
        title: "Stacking Mastery Challenge",
        description: "Put your stacking skills to the test with complex scenarios",
        content: "Advanced challenge scenarios to test your mastery",
        type: "interactive",
        duration: "5 min",
        keyPoints: [
          "Complex scenario analysis",
          "Decision-making under pressure",
          "Professional-level execution"
        ]
      }
    ]
  },
  {
    id: 6,
    title: "Risk Management & Psychology",
    description: "Master the mental game and risk management for consistent profits",
    icon: "Shield",
    color: "from-indigo-500 to-indigo-600",
    estimatedTime: "45 minutes",
    difficulty: "Intermediate",
    lessons: [
      {
        id: 1,
        title: "Position Sizing Fundamentals",
        description: "Calculate optimal position sizes for your account",
        content: "Proper position sizing is the foundation of risk management...",
        type: "theory",
        duration: "12 min",
        keyPoints: [
          "Risk percentage rules",
          "Account size considerations",
          "Volatility-adjusted sizing"
        ]
      },
      {
        id: 2,
        title: "Stop Loss Strategies",
        description: "Advanced stop loss placement and management techniques",
        content: "Stop losses are your insurance policy in trading...",
        type: "practical",
        duration: "15 min",
        keyPoints: [
          "Technical vs percentage stops",
          "Trailing stop strategies",
          "Stop loss psychology"
        ]
      },
      {
        id: 3,
        title: "Trading Psychology Mastery",
        description: "Develop the mental discipline required for consistent trading",
        content: "Trading psychology often determines success more than technical skills...",
        type: "strategy",
        duration: "15 min",
        keyPoints: [
          "Emotional regulation techniques",
          "Dealing with losses",
          "Maintaining discipline"
        ]
      },
      {
        id: 4,
        title: "Psychology Assessment",
        description: "Evaluate your trading psychology and identify areas for improvement",
        content: "Self-assessment tools for trading psychology",
        type: "interactive",
        duration: "3 min",
        keyPoints: [
          "Psychological profiling",
          "Weakness identification",
          "Improvement planning"
        ]
      }
    ]
  },
  {
    id: 7,
    title: "Advanced Pattern Recognition",
    description: "Identify complex patterns and market structures for professional-level trading",
    icon: "Eye",
    color: "from-teal-500 to-teal-600",
    estimatedTime: "65 minutes",
    difficulty: "Advanced",
    lessons: [
      {
        id: 1,
        title: "Complex Pattern Structures",
        description: "Understanding advanced chart patterns and their implications",
        content: "Advanced patterns often provide the highest probability setups...",
        type: "theory",
        duration: "18 min",
        keyPoints: [
          "Multi-timeframe pattern analysis",
          "Pattern failure and continuation",
          "Context-dependent patterns"
        ]
      },
      {
        id: 2,
        title: "Market Structure Shifts",
        description: "Identifying when market structure changes and how to adapt",
        content: "Market structure shifts signal major changes in sentiment...",
        type: "practical",
        duration: "20 min",
        keyPoints: [
          "Break of structure signals",
          "Change of character patterns",
          "Trend transition identification"
        ]
      },
      {
        id: 3,
        title: "Professional Pattern Trading",
        description: "How professionals trade complex patterns for maximum profit",
        content: "Professional trading strategies for advanced patterns...",
        type: "strategy",
        duration: "22 min",
        keyPoints: [
          "Entry and exit optimization",
          "Risk-reward maximization",
          "Pattern-specific strategies"
        ]
      },
      {
        id: 4,
        title: "Pattern Mastery Exam",
        description: "Final examination of your pattern recognition abilities",
        content: "Comprehensive test of all pattern recognition skills",
        type: "interactive",
        duration: "5 min",
        keyPoints: [
          "Comprehensive pattern test",
          "Speed and accuracy assessment",
          "Professional certification"
        ]
      }
    ]
  }
]

export const COURSE_ACHIEVEMENTS = [
  {
    id: "first_lesson",
    title: "Getting Started",
    description: "Complete your first lesson",
    icon: "Play",
    points: 10
  },
  {
    id: "first_module",
    title: "Module Master",
    description: "Complete your first module",
    icon: "Award",
    points: 50
  },
  {
    id: "quiz_master",
    title: "Quiz Master",
    description: "Score 90% or higher on 5 quizzes",
    icon: "Brain",
    points: 100
  },
  {
    id: "speed_learner",
    title: "Speed Learner",
    description: "Complete 3 lessons in one day",
    icon: "Zap",
    points: 75
  },
  {
    id: "course_complete",
    title: "Course Graduate",
    description: "Complete the entire course",
    icon: "GraduationCap",
    points: 500
  }
]
