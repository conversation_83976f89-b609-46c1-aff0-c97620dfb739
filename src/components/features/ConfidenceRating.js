/**
 * Confidence Rating Component
 * Interactive confidence rating slider with visual feedback
 */

'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { Star, TrendingUp, Target, Zap } from 'lucide-react'
import Card, { CardHeader, CardTitle, CardContent } from '../ui/Card'
import { useStore } from '@/store/useStore'

export default function ConfidenceRating({ className = '' }) {
  const { checklist, setConfidenceRating } = useStore()
  const [hoveredRating, setHoveredRating] = useState(0)

  const rating = checklist.confidenceRating || 5
  const displayRating = hoveredRating || rating

  const ratingLabels = {
    1: { label: 'Very Low', color: 'text-red-600', bg: 'bg-red-100', description: 'High uncertainty, consider waiting' },
    2: { label: 'Low', color: 'text-orange-600', bg: 'bg-orange-100', description: 'Some doubts, proceed with caution' },
    3: { label: 'Moderate', color: 'text-yellow-600', bg: 'bg-yellow-100', description: 'Neutral confidence, standard risk' },
    4: { label: 'High', color: 'text-blue-600', bg: 'bg-blue-100', description: 'Good confidence, favorable conditions' },
    5: { label: 'Very High', color: 'text-green-600', bg: 'bg-green-100', description: 'Maximum confidence, optimal setup' }
  }

  const currentRating = ratingLabels[displayRating]

  const handleRatingChange = (newRating) => {
    setConfidenceRating(newRating)
  }

  return (
    <Card variant="glass" className={className}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-3">
          <div className={`w-10 h-10 rounded-xl flex items-center justify-center ${currentRating.bg}`}>
            <Target className={`w-5 h-5 ${currentRating.color}`} />
          </div>
          <div>
            <span>Pre-Trade Confidence</span>
            <p className="text-sm text-gray-600 font-normal mt-1">
              Rate your confidence level for this trade
            </p>
          </div>
        </CardTitle>
      </CardHeader>

      <CardContent>
        {/* Current Rating Display */}
        <div className="text-center mb-6">
          <motion.div
            key={displayRating}
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            className={`inline-flex items-center space-x-2 px-4 py-2 rounded-full ${currentRating.bg} mb-3`}
          >
            <Star className={`w-5 h-5 ${currentRating.color} fill-current`} />
            <span className={`font-bold ${currentRating.color}`}>
              {displayRating}/5 - {currentRating.label}
            </span>
          </motion.div>
          
          <p className="text-sm text-gray-600 
            {currentRating.description}
          </p>
        </div>

        {/* Interactive Star Rating */}
        <div className="flex justify-center space-x-2 mb-6">
          {[1, 2, 3, 4, 5].map((star) => (
            <motion.button
              key={star}
              onClick={() => handleRatingChange(star)}
              onMouseEnter={() => setHoveredRating(star)}
              onMouseLeave={() => setHoveredRating(0)}
              className="focus:outline-none"
              whileHover={{ scale: 1.2 }}
              whileTap={{ scale: 0.9 }}
            >
              <Star
                className={`w-8 h-8 transition-all duration-200 ${
                  star <= displayRating
                    ? 'text-yellow-400 fill-current drop-shadow-lg'
                    : 'text-gray-300 
                }`}
              />
            </motion.button>
          ))}
        </div>

        {/* Slider Alternative */}
        <div className="space-y-3">
          <div className="flex justify-between text-xs text-gray-500 
            <span>Low</span>
            <span>Moderate</span>
            <span>High</span>
          </div>
          
          <div className="relative">
            <input
              type="range"
              min="1"
              max="5"
              value={rating}
              onChange={(e) => handleRatingChange(parseInt(e.target.value))}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
            />
            
            {/* Progress indicator */}
            <div 
              className="absolute top-0 h-2 bg-gradient-to-r from-red-400 via-yellow-400 to-green-400 rounded-lg pointer-events-none"
              style={{ width: `${(rating / 5) * 100}%` }}
            />
          </div>
        </div>

        {/* Confidence Insights */}
        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <h4 className="font-medium text-gray-900 mb-2 flex items-center space-x-2">
            <Zap className="w-4 h-4 text-primary-600" />
            <span>Confidence Insights</span>
          </h4>
          
          <div className="space-y-2 text-sm text-gray-600 
            {rating >= 4 && (
              <div className="flex items-start space-x-2">
                <TrendingUp className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                <span>High confidence suggests strong setup alignment with your strategy</span>
              </div>
            )}
            
            {rating === 3 && (
              <div className="flex items-start space-x-2">
                <Target className="w-4 h-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                <span>Moderate confidence - ensure risk management is in place</span>
              </div>
            )}
            
            {rating <= 2 && (
              <div className="flex items-start space-x-2">
                <Star className="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" />
                <span>Low confidence - consider waiting for better setup or reducing position size</span>
              </div>
            )}
            
            <div className="pt-2 border-t border-gray-200 
              <span className="text-xs">
                Tip: Higher confidence ratings correlate with better trading outcomes
              </span>
            </div>
          </div>
        </div>

        {/* Historical Average */}
        <div className="mt-4 text-center">
          <div className="text-xs text-gray-500 
            Your average confidence: <span className="font-semibold">4.1/5</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
