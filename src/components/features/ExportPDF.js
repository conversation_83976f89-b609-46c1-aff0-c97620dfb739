/**
 * Export PDF Component
 * Export journal entries and checklists as PDF documents
 */

'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { Download, FileText, Calendar, CheckSquare, Loader2 } from 'lucide-react'
import Button from '../ui/Button'
import Card, { CardHeader, CardTitle, CardContent } from '../ui/Card'
import { useStore } from '@/store/useStore'
import { getAllJournalDates, getAllChecklistDates, loadJournalEntry, loadChecklistData, formatDate } from '@/utils/storage'
import toast from 'react-hot-toast'

export default function ExportPDF() {
  const [isExporting, setIsExporting] = useState(false)
  const [exportType, setExportType] = useState('journal')
  const [dateRange, setDateRange] = useState('week')
  
  const { journal, checklist } = useStore()

  const generatePDFContent = async (type, range) => {
    const today = new Date()
    let startDate = new Date()
    
    // Calculate date range
    switch (range) {
      case 'week':
        startDate.setDate(today.getDate() - 7)
        break
      case 'month':
        startDate.setMonth(today.getMonth() - 1)
        break
      case 'quarter':
        startDate.setMonth(today.getMonth() - 3)
        break
      case 'all':
        startDate = new Date(2020, 0, 1) // Far back date
        break
    }

    let content = `
      <html>
        <head>
          <title>Limitless Options - ${type === 'journal' ? 'Trading Journal' : 'Checklist Report'}</title>
          <style>
            body { font-family: 'Inter', Arial, sans-serif; margin: 40px; color: #1f2937; }
            .header { text-align: center; margin-bottom: 40px; border-bottom: 2px solid #3b82f6; padding-bottom: 20px; }
            .logo { font-size: 24px; font-weight: bold; color: #3b82f6; margin-bottom: 10px; }
            .subtitle { color: #6b7280; font-size: 14px; }
            .entry { margin-bottom: 30px; padding: 20px; border: 1px solid #e5e7eb; border-radius: 8px; }
            .entry-date { font-size: 18px; font-weight: bold; color: #1f2937; margin-bottom: 10px; }
            .entry-content { line-height: 1.6; }
            .checklist-item { margin: 8px 0; padding: 8px; background: #f9fafb; border-radius: 4px; }
            .completed { background: #d1fae5; }
            .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }
            .stat-card { padding: 15px; background: #f3f4f6; border-radius: 8px; text-align: center; }
            .stat-value { font-size: 24px; font-weight: bold; color: #3b82f6; }
            .stat-label { font-size: 12px; color: #6b7280; text-transform: uppercase; }
            .footer { margin-top: 40px; text-align: center; font-size: 12px; color: #9ca3af; }
          </style>
        </head>
        <body>
          <div class="header">
            <div class="logo">Limitless Options</div>
            <div class="subtitle">${type === 'journal' ? 'Trading Journal Report' : 'Checklist Performance Report'}</div>
            <div class="subtitle">Generated on ${today.toLocaleDateString()}</div>
          </div>
    `

    if (type === 'journal') {
      const journalDates = getAllJournalDates().filter(date => {
        const entryDate = new Date(date)
        return entryDate >= startDate && entryDate <= today
      })

      // Add stats
      content += `
        <div class="stats">
          <div class="stat-card">
            <div class="stat-value">${journalDates.length}</div>
            <div class="stat-label">Total Entries</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">${Math.round(journalDates.length / Math.max(1, Math.ceil((today - startDate) / (1000 * 60 * 60 * 24))) * 100)}%</div>
            <div class="stat-label">Consistency Rate</div>
          </div>
        </div>
      `

      // Add journal entries
      journalDates.forEach(date => {
        const entry = loadJournalEntry(date)
        if (entry && entry.content) {
          content += `
            <div class="entry">
              <div class="entry-date">${formatDate(date)}</div>
              <div class="entry-content">${entry.content.replace(/<[^>]*>/g, '')}</div>
            </div>
          `
        }
      })
    } else {
      // Checklist report
      const checklistDates = getAllChecklistDates().filter(date => {
        const entryDate = new Date(date)
        return entryDate >= startDate && entryDate <= today
      })

      let totalItems = 0
      let completedItems = 0

      content += `
        <div class="stats">
          <div class="stat-card">
            <div class="stat-value">${checklistDates.length}</div>
            <div class="stat-label">Trading Days</div>
          </div>
        </div>
      `

      checklistDates.forEach(date => {
        const data = loadChecklistData(date)
        if (data && data.checkedItems) {
          const items = Object.entries(data.checkedItems)
          const completed = items.filter(([_, checked]) => checked).length
          totalItems += items.length
          completedItems += completed

          content += `
            <div class="entry">
              <div class="entry-date">${formatDate(date)} - ${completed}/${items.length} completed</div>
              <div class="entry-content">
                ${items.map(([itemId, checked]) => `
                  <div class="checklist-item ${checked ? 'completed' : ''}">
                    ${checked ? '✓' : '○'} ${itemId.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </div>
                `).join('')}
              </div>
            </div>
          `
        }
      })

      // Update stats
      const completionRate = totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0
      content = content.replace('<div class="stat-card">\n            <div class="stat-value">', `
        <div class="stat-card">
          <div class="stat-value">${completionRate}%</div>
          <div class="stat-label">Completion Rate</div>
        </div>
        <div class="stat-card">
          <div class="stat-value">
      `)
    }

    content += `
          <div class="footer">
            <p>Generated by Limitless Options Trading Hub</p>
            <p>Professional trading tools for disciplined traders</p>
          </div>
        </body>
      </html>
    `

    return content
  }

  const handleExport = async () => {
    setIsExporting(true)
    
    try {
      // Dynamic import to avoid SSR issues
      const jsPDF = (await import('jspdf')).default
      const html2canvas = (await import('html2canvas')).default

      const content = await generatePDFContent(exportType, dateRange)
      
      // Create a temporary div to render HTML
      const tempDiv = document.createElement('div')
      tempDiv.innerHTML = content
      tempDiv.style.position = 'absolute'
      tempDiv.style.left = '-9999px'
      tempDiv.style.width = '800px'
      document.body.appendChild(tempDiv)

      // Convert to canvas
      const canvas = await html2canvas(tempDiv, {
        scale: 2,
        useCORS: true,
        allowTaint: true
      })

      // Remove temp div
      document.body.removeChild(tempDiv)

      // Create PDF
      const pdf = new jsPDF('p', 'mm', 'a4')
      const imgData = canvas.toDataURL('image/png')
      
      const pdfWidth = pdf.internal.pageSize.getWidth()
      const pdfHeight = (canvas.height * pdfWidth) / canvas.width
      
      pdf.addImage(imgData, 'PNG', 0, 0, pdfWidth, pdfHeight)
      
      // Save PDF
      const filename = `limitless-${exportType}-${dateRange}-${new Date().toISOString().split('T')[0]}.pdf`
      pdf.save(filename)
      
      toast.success(`${exportType === 'journal' ? 'Journal' : 'Checklist'} exported successfully!`)
    } catch (error) {
      console.error('Export error:', error)
      toast.error('Failed to export PDF. Please try again.')
    } finally {
      setIsExporting(false)
    }
  }

  return (
    <Card variant="glass">
      <CardHeader>
        <CardTitle className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-r from-accent-500 to-accent-600 rounded-xl flex items-center justify-center">
            <Download className="w-5 h-5 text-white" />
          </div>
          <div>
            <span>Export to PDF</span>
            <p className="text-sm text-gray-600 font-normal mt-1">
              Generate professional PDF reports
            </p>
          </div>
        </CardTitle>
      </CardHeader>

      <CardContent>
        <div className="space-y-4">
          {/* Export Type Selection */}
          <div>
            <label className="text-sm font-medium text-gray-700 mb-2 block">
              Export Type
            </label>
            <div className="grid grid-cols-2 gap-2">
              <button
                onClick={() => setExportType('journal')}
                className={`
                  flex items-center space-x-2 p-3 rounded-lg border-2 transition-colors
                  ${exportType === 'journal'
                    ? 'border-primary-500 bg-primary-50 text-primary-700 
                    : 'border-gray-200 hover:border-gray-300 
                  }
                `}
              >
                <FileText className="w-5 h-5" />
                <span className="font-medium">Journal</span>
              </button>
              
              <button
                onClick={() => setExportType('checklist')}
                className={`
                  flex items-center space-x-2 p-3 rounded-lg border-2 transition-colors
                  ${exportType === 'checklist'
                    ? 'border-primary-500 bg-primary-50 text-primary-700 
                    : 'border-gray-200 hover:border-gray-300 
                  }
                `}
              >
                <CheckSquare className="w-5 h-5" />
                <span className="font-medium">Checklist</span>
              </button>
            </div>
          </div>

          {/* Date Range Selection */}
          <div>
            <label className="text-sm font-medium text-gray-700 mb-2 block">
              Date Range
            </label>
            <select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
              className="w-full p-3 border border-gray-200 rounded-lg bg-white text-gray-900 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value="week">Last 7 days</option>
              <option value="month">Last 30 days</option>
              <option value="quarter">Last 3 months</option>
              <option value="all">All time</option>
            </select>
          </div>

          {/* Export Button */}
          <Button
            variant="primary"
            icon={isExporting ? Loader2 : Download}
            onClick={handleExport}
            disabled={isExporting}
            fullWidth
            className="mt-6"
          >
            {isExporting ? 'Generating PDF...' : `Export ${exportType === 'journal' ? 'Journal' : 'Checklist'} PDF`}
          </Button>

          {/* Info */}
          <div className="mt-4 p-3 bg-blue-50 rounded-lg">
            <p className="text-sm text-blue-700 
              <strong>Note:</strong> The PDF will include all {exportType === 'journal' ? 'journal entries' : 'checklist data'} from the selected date range, 
              formatted for professional presentation.
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
