'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Calendar,
  TrendingUp,
  BookOpen,
  Bell,
  Settings,
  Menu,
  X,
  User,
  Bar<PERSON>hart3,
  Timer,
  Target
} from 'lucide-react'
import { formatDate, getTodayKey } from '@/utils/storage'
import { useStore, useChecklist, useNotifications, useUI } from '@/store/useStore'

import Button from './ui/Button'
import { IconButton } from './ui/Button'
import NotificationCenter from './features/NotificationCenter'

export default function Header() {
  const [currentTime, setCurrentTime] = useState(new Date())
  const [showNotifications, setShowNotifications] = useState(false)
  const today = getTodayKey()
  const formattedDate = formatDate(today)

  const checklist = useChecklist()
  const notifications = useNotifications()
  const { showMobileMenu, toggleMobileMenu, toggleModal } = useStore()

  const unreadNotifications = notifications.filter(n => !n.read).length
  const checkedCount = Object.values(checklist.items).filter(Boolean).length
  const isReadyToTrade = checkedCount >= 3
  const isSessionActive = checklist.isSessionActive

  // Update time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 60000)
    return () => clearInterval(timer)
  }, [])

  // Calculate session duration
  const getSessionDuration = () => {
    if (!checklist.sessionStartTime) return null
    const start = new Date(checklist.sessionStartTime)
    const now = new Date()
    const diff = Math.floor((now - start) / 1000 / 60) // minutes
    const hours = Math.floor(diff / 60)
    const minutes = diff % 60
    return hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`
  }

  return (
    <>
      <motion.header
        className="bg-[#0A0F0F]/95 backdrop-blur-xl border-b border-[#F4C46A]/20 sticky top-0 z-50 shadow-lg shadow-black/30"
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16 lg:h-20">
            {/* Logo and Brand */}
            <motion.div
              className="flex items-center space-x-3"
              whileHover={{ scale: 1.02 }}
            >
              <div className="relative w-10 h-10 lg:w-12 lg:h-12 rounded-xl overflow-hidden shadow-lg ring-2 ring-[#1FC77D]/40"
                <Image
                  src="/LimitlessLogo.jpg"
                  alt="Limitless Options Logo"
                  fill
                  className="object-cover"
                  priority
                />
              </div>
              <div>
                <h1 className="text-xl lg:text-2xl font-heading font-bold bg-gradient-to-r from-[#F4C46A] to-[#1FC77D] bg-clip-text text-transparent">
                  Limitless Options
                </h1>
                <p className="text-xs lg:text-sm text-[#9CA3AF] font-medium">
                  Trading Hub
                </p>
              </div>
            </motion.div>

            {/* Desktop Navigation */}
            <div className="hidden lg:flex items-center space-x-6">
              {/* Current Time & Date */}
              <div className="flex items-center space-x-4">
                <div className="text-center">
                  <div className="text-sm font-semibold text-[#F5F5F1]">
                    {currentTime.toLocaleTimeString('en-US', {
                      hour: '2-digit',
                      minute: '2-digit',
                      hour12: true
                    })}
                  </div>
                  <div className="text-xs text-[#9CA3AF]">
                    {formattedDate.split(',')[0]}
                  </div>
                </div>
              </div>

              {/* Trading Status */}
              <div className="flex items-center space-x-3">
                <motion.div
                  className={`
                    flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium
                    ${isReadyToTrade
                      ? 'bg-[#1FC77D]/20 text-[#1FC77D] border border-[#1FC77D]/30'
                      : 'bg-[#F4C46A]/20 text-[#F4C46A] border border-[#F4C46A]/30'
                    }
                  `}
                  animate={isReadyToTrade ? { scale: [1, 1.05, 1] } : {}}
                  transition={{ duration: 0.5, repeat: isReadyToTrade ? Infinity : 0, repeatDelay: 3 }}
                >
                  <Target className="w-4 h-4" />
                  <span>{isReadyToTrade ? 'Ready to Trade' : `${3 - checkedCount} more needed`}</span>
                </motion.div>

                {/* Session Timer */}
                {isSessionActive && (
                  <motion.div
                    className="flex items-center space-x-2 px-3 py-2 bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-400 rounded-lg text-sm font-medium"
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                  >
                    <Timer className="w-4 h-4" />
                    <span>{getSessionDuration()}</span>
                  </motion.div>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex items-center space-x-2">
                {/* Notifications */}
                <div className="relative">
                  <div className="relative">
                    <IconButton
                      icon={Bell}
                      variant="ghost"
                      onClick={() => setShowNotifications(!showNotifications)}
                      className="relative"
                    />
                    {unreadNotifications > 0 && (
                      <motion.div
                        className="absolute -top-1 -right-1 w-5 h-5 bg-[#F25D5D] text-[#F5F5F1] text-xs rounded-full flex items-center justify-center shadow-lg"
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ type: "spring", stiffness: 500, damping: 30 }}
                      >
                        {unreadNotifications}
                      </motion.div>
                    )}
                  </div>
                </div>

                {/* Settings */}
                <IconButton
                  icon={Settings}
                  variant="ghost"
                  onClick={() => toggleModal('settings')}
                />
              </div>
            </div>

            {/* Mobile Menu Button */}
            <div className="lg:hidden flex items-center space-x-2">
              <IconButton
                icon={showMobileMenu ? X : Menu}
                variant="ghost"
                onClick={toggleMobileMenu}
              />
            </div>
          </div>
        </div>
      </motion.header>

      {/* Mobile Menu */}
      <AnimatePresence>
        {showMobileMenu && (
          <motion.div
            className="lg:hidden fixed inset-0 z-40 bg-black/50 backdrop-blur-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={toggleMobileMenu}
          >
            <motion.div
              className="absolute top-16 right-4 left-4 bg-white dark:bg-dark-800 rounded-2xl shadow-xl border border-gray-200 dark:border-dark-700 p-6"
              initial={{ opacity: 0, scale: 0.95, y: -20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: -20 }}
              onClick={(e) => e.stopPropagation()}
            >
              {/* Mobile Status */}
              <div className="mb-6">
                <div className="text-center mb-4">
                  <div className="text-lg font-semibold text-gray-900 dark:text-white">
                    {currentTime.toLocaleTimeString('en-US', {
                      hour: '2-digit',
                      minute: '2-digit',
                      hour12: true
                    })}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    {formattedDate}
                  </div>
                </div>

                <div className="space-y-3">
                  <div className={`
                    flex items-center justify-center space-x-2 px-4 py-3 rounded-lg text-sm font-medium
                    ${isReadyToTrade
                      ? 'bg-success-50 dark:bg-success-900/20 text-success-700 dark:text-success-400'
                      : 'bg-warning-50 dark:bg-warning-900/20 text-warning-700 dark:text-warning-400'
                    }
                  `}>
                    <Target className="w-4 h-4" />
                    <span>{isReadyToTrade ? 'Ready to Trade' : `${3 - checkedCount} more needed`}</span>
                  </div>

                  {isSessionActive && (
                    <div className="flex items-center justify-center space-x-2 px-4 py-3 bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-400 rounded-lg text-sm font-medium">
                      <Timer className="w-4 h-4" />
                      <span>Session: {getSessionDuration()}</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Mobile Actions */}
              <div className="space-y-3">
                <Button
                  variant="ghost"
                  fullWidth
                  icon={Bell}
                  onClick={() => {
                    toggleModal('notifications')
                    toggleMobileMenu()
                  }}
                  className="justify-start"
                >
                  Notifications
                  {unreadNotifications > 0 && (
                    <span className="ml-auto bg-danger-500 text-white text-xs px-2 py-1 rounded-full">
                      {unreadNotifications}
                    </span>
                  )}
                </Button>

                <Button
                  variant="ghost"
                  fullWidth
                  icon={BarChart3}
                  onClick={() => {
                    toggleModal('stats')
                    toggleMobileMenu()
                  }}
                  className="justify-start"
                >
                  Statistics
                </Button>

                <Button
                  variant="ghost"
                  fullWidth
                  icon={Settings}
                  onClick={() => {
                    toggleModal('settings')
                    toggleMobileMenu()
                  }}
                  className="justify-start"
                >
                  Settings
                </Button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Notification Center */}
      <NotificationCenter
        isOpen={showNotifications}
        onClose={() => setShowNotifications(false)}
      />
    </>
  )
}
