'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Database,
  Upload,
  CheckCircle2,
  AlertCircle,
  Loader2,
  RefreshCw,
  Cloud,
  HardDrive,
  ArrowRight,
  Info
} from 'lucide-react'
import <PERSON><PERSON> from '@/components/ui/Button'
import Card, { CardHeader, CardTitle, CardContent } from '@/components/ui/Card'
import toast from 'react-hot-toast'

export default function MigrationPanel() {
  const [migrationStatus, setMigrationStatus] = useState(null)
  const [isLoading, setIsLoading] = useState(false)
  const [isMigrating, setIsMigrating] = useState(false)
  const [migrationResult, setMigrationResult] = useState(null)

  // Check migration status on component mount
  useEffect(() => {
    checkStatus()
  }, [])

  const checkStatus = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/migrate')
      const result = await response.json()
      
      if (result.success) {
        setMigrationStatus(result.data)
      } else {
        console.error('Failed to check migration status:', result.error)
        toast.error('Failed to check migration status')
      }
    } catch (error) {
      console.error('Error checking migration status:', error)
      toast.error('Error checking migration status')
    } finally {
      setIsLoading(false)
    }
  }

  const startMigration = async () => {
    setIsMigrating(true)
    setMigrationResult(null)
    
    try {
      toast.loading('Starting migration...', { id: 'migration' })
      
      const response = await fetch('/api/migrate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      })
      
      const result = await response.json()
      
      if (result.success) {
        setMigrationResult(result.data)
        toast.success('Migration completed successfully!', { id: 'migration' })
        
        // Refresh status after migration
        setTimeout(() => {
          checkStatus()
        }, 1000)
      } else {
        toast.error(`Migration failed: ${result.error}`, { id: 'migration' })
      }
    } catch (error) {
      console.error('Migration error:', error)
      toast.error('Migration failed due to network error', { id: 'migration' })
    } finally {
      setIsMigrating(false)
    }
  }

  const getLocalStorageStats = () => {
    let checklistCount = 0
    let journalCount = 0
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key?.startsWith('limitless_checklist_')) checklistCount++
      if (key?.startsWith('limitless_journal_')) journalCount++
    }
    
    return { checklistCount, journalCount }
  }

  const localStats = getLocalStorageStats()
  const hasLocalData = localStats.checklistCount > 0 || localStats.journalCount > 0
  const hasCloudData = migrationStatus && (migrationStatus.checklistEntries > 0 || migrationStatus.journalEntries > 0)

  return (
    <div className="space-y-6">
      {/* Migration Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center"
      >
        <div className="flex items-center justify-center space-x-3 mb-4">
          <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
            <Database className="w-6 h-6 text-white" />
          </div>
          <h2 className="text-3xl font-bold text-gray-900 
            Cloud Migration Center
          </h2>
        </div>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Migrate your local data to the cloud database for cross-device access and backup protection
        </p>
      </motion.div>

      {/* Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Local Storage Status */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card variant="glass">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <HardDrive className="w-5 h-5" />
                <span>Local Storage</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-gray-50 rounded-lg">
                  <div className="text-2xl font-bold text-gray-900 
                    {localStats.checklistCount}
                  </div>
                  <div className="text-xs text-gray-600 
                    Checklists
                  </div>
                </div>
                <div className="text-center p-3 bg-gray-50 rounded-lg">
                  <div className="text-2xl font-bold text-gray-900 
                    {localStats.journalCount}
                  </div>
                  <div className="text-xs text-gray-600 
                    Journal Entries
                  </div>
                </div>
              </div>
              
              <div className={`
                flex items-center space-x-2 p-3 rounded-lg
                ${hasLocalData 
                  ? 'bg-green-50 text-green-700 
                  : 'bg-gray-50 text-gray-600 
                }
              `}>
                {hasLocalData ? (
                  <CheckCircle2 className="w-4 h-4" />
                ) : (
                  <Info className="w-4 h-4" />
                )}
                <span className="text-sm">
                  {hasLocalData ? 'Data found in local storage' : 'No local data found'}
                </span>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Cloud Storage Status */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card variant="glass">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Cloud className="w-5 h-5" />
                <span>Cloud Database</span>
                <Button
                  variant="ghost"
                  size="sm"
                  icon={RefreshCw}
                  onClick={checkStatus}
                  disabled={isLoading}
                  className="ml-auto"
                >
                  {isLoading ? 'Checking...' : 'Refresh'}
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {migrationStatus ? (
                <>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-3 bg-gray-50 rounded-lg">
                      <div className="text-2xl font-bold text-gray-900 
                        {migrationStatus.checklistEntries}
                      </div>
                      <div className="text-xs text-gray-600 
                        Checklists
                      </div>
                    </div>
                    <div className="text-center p-3 bg-gray-50 rounded-lg">
                      <div className="text-2xl font-bold text-gray-900 
                        {migrationStatus.journalEntries}
                      </div>
                      <div className="text-xs text-gray-600 
                        Journal Entries
                      </div>
                    </div>
                  </div>
                  
                  <div className={`
                    flex items-center space-x-2 p-3 rounded-lg
                    ${migrationStatus.tablesExist 
                      ? 'bg-green-50 text-green-700 
                      : 'bg-red-50 text-red-700 
                    }
                  `}>
                    {migrationStatus.tablesExist ? (
                      <CheckCircle2 className="w-4 h-4" />
                    ) : (
                      <AlertCircle className="w-4 h-4" />
                    )}
                    <span className="text-sm">
                      {migrationStatus.tablesExist ? 'Database connected' : 'Database not initialized'}
                    </span>
                  </div>
                </>
              ) : (
                <div className="text-center py-8">
                  <Loader2 className="w-8 h-8 animate-spin mx-auto mb-2 text-gray-400" />
                  <p className="text-sm text-gray-600 
                    Checking database status...
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Migration Action */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
      >
        <Card variant="glass">
          <CardContent className="p-8 text-center">
            <div className="max-w-md mx-auto space-y-6">
              <div className="flex items-center justify-center space-x-4">
                <div className="flex items-center space-x-2 text-gray-600 
                  <HardDrive className="w-5 h-5" />
                  <span>Local Data</span>
                </div>
                <ArrowRight className="w-6 h-6 text-primary-500" />
                <div className="flex items-center space-x-2 text-gray-600 
                  <Cloud className="w-5 h-5" />
                  <span>Cloud Database</span>
                </div>
              </div>

              <div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  Ready to Migrate?
                </h3>
                <p className="text-gray-600 text-sm">
                  This will copy all your local data to the cloud database. 
                  Your local data will remain as backup.
                </p>
              </div>

              <Button
                variant="primary"
                size="lg"
                icon={isMigrating ? Loader2 : Upload}
                onClick={startMigration}
                disabled={isMigrating || !hasLocalData}
                className="w-full"
              >
                {isMigrating ? 'Migrating...' : 'Start Migration'}
              </Button>

              {!hasLocalData && (
                <p className="text-xs text-gray-500 
                  No local data found to migrate
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Migration Result */}
      <AnimatePresence>
        {migrationResult && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
          >
            <Card variant="success">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <CheckCircle2 className="w-5 h-5 text-success-600" />
                  <span>Migration Completed Successfully!</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center p-3 bg-white rounded-lg">
                    <div className="text-lg font-bold text-success-700 
                      {migrationResult.checklistEntries}
                    </div>
                    <div className="text-xs text-success-600 
                      Checklists Migrated
                    </div>
                  </div>
                  <div className="text-center p-3 bg-white rounded-lg">
                    <div className="text-lg font-bold text-success-700 
                      {migrationResult.journalEntries}
                    </div>
                    <div className="text-xs text-success-600 
                      Journal Entries
                    </div>
                  </div>
                  <div className="text-center p-3 bg-white rounded-lg">
                    <div className="text-lg font-bold text-success-700 
                      {migrationResult.courseProgress ? '✓' : '—'}
                    </div>
                    <div className="text-xs text-success-600 
                      Course Progress
                    </div>
                  </div>
                  <div className="text-center p-3 bg-white rounded-lg">
                    <div className="text-lg font-bold text-success-700 
                      {migrationResult.preferences ? '✓' : '—'}
                    </div>
                    <div className="text-xs text-success-600 
                      Preferences
                    </div>
                  </div>
                </div>
                
                <div className="text-center text-sm text-success-600 
                  Migration completed at {new Date(migrationResult.timestamp).toLocaleString()}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
