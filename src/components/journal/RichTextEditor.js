/**
 * Rich Text Editor Component
 * WYSIWYG editor for journal entries with modern features
 */

'use client'

import { useState, useEffect, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Bold, 
  Italic, 
  Underline, 
  List, 
  ListOrdered,
  Quote,
  Link,
  Image,
  Save,
  Eye,
  Edit3,
  Type,
  Palette,
  AlignLeft,
  AlignCenter,
  AlignRight
} from 'lucide-react'
import <PERSON>ton from '../ui/Button'
import { IconButton } from '../ui/Button'

export default function RichTextEditor({ 
  value = '', 
  onChange, 
  placeholder = 'Start writing your trading journal...',
  autoSave = true,
  className = ''
}) {
  const [content, setContent] = useState(value)
  const [isPreview, setIsPreview] = useState(false)
  const [isFocused, setIsFocused] = useState(false)
  const [wordCount, setWordCount] = useState(0)
  const [charCount, setCharCount] = useState(0)
  const editorRef = useRef(null)

  useEffect(() => {
    setContent(value)
    updateCounts(value)
  }, [value])

  useEffect(() => {
    if (autoSave && content !== value) {
      const timer = setTimeout(() => {
        onChange?.(content)
      }, 1000) // Auto-save after 1 second of inactivity
      
      return () => clearTimeout(timer)
    }
  }, [content, value, onChange, autoSave])

  const updateCounts = (text) => {
    const words = text.trim().split(/\s+/).filter(word => word.length > 0).length
    const chars = text.length
    setWordCount(words)
    setCharCount(chars)
  }

  const handleContentChange = (newContent) => {
    setContent(newContent)
    updateCounts(newContent)
  }

  const formatText = (command, value = null) => {
    document.execCommand(command, false, value)
    editorRef.current?.focus()
  }

  const insertText = (text) => {
    const selection = window.getSelection()
    if (selection.rangeCount > 0) {
      const range = selection.getRangeAt(0)
      range.deleteContents()
      range.insertNode(document.createTextNode(text))
      range.collapse(false)
      selection.removeAllRanges()
      selection.addRange(range)
    }
    editorRef.current?.focus()
  }

  const toolbarButtons = [
    { icon: Bold, command: 'bold', title: 'Bold (Ctrl+B)' },
    { icon: Italic, command: 'italic', title: 'Italic (Ctrl+I)' },
    { icon: Underline, command: 'underline', title: 'Underline (Ctrl+U)' },
    { type: 'separator' },
    { icon: List, command: 'insertUnorderedList', title: 'Bullet List' },
    { icon: ListOrdered, command: 'insertOrderedList', title: 'Numbered List' },
    { icon: Quote, command: 'formatBlock', value: 'blockquote', title: 'Quote' },
    { type: 'separator' },
    { icon: AlignLeft, command: 'justifyLeft', title: 'Align Left' },
    { icon: AlignCenter, command: 'justifyCenter', title: 'Align Center' },
    { icon: AlignRight, command: 'justifyRight', title: 'Align Right' },
  ]

  return (
    <motion.div
      className={`border-2 rounded-2xl overflow-hidden transition-all duration-300 ${
        isFocused 
          ? 'border-primary-400 shadow-glow' 
          : 'border-gray-200 
      } ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
    >
      {/* Toolbar */}
      <div className="bg-gray-50 border-b border-gray-200 p-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-1">
            {toolbarButtons.map((button, index) => {
              if (button.type === 'separator') {
                return (
                  <div 
                    key={index} 
                    className="w-px h-6 bg-gray-300 mx-1" 
                  />
                )
              }
              
              return (
                <IconButton
                  key={index}
                  icon={button.icon}
                  size="sm"
                  variant="ghost"
                  onClick={() => formatText(button.command, button.value)}
                  title={button.title}
                  className="hover:bg-gray-200 
                />
              )
            })}
          </div>

          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              icon={isPreview ? Edit3 : Eye}
              onClick={() => setIsPreview(!isPreview)}
            >
              {isPreview ? 'Edit' : 'Preview'}
            </Button>
          </div>
        </div>
      </div>

      {/* Editor Content */}
      <div className="relative">
        {!isPreview ? (
          <div
            ref={editorRef}
            contentEditable
            className="min-h-64 p-6 text-gray-900 focus:outline-none prose prose-gray max-w-none"
            style={{ 
              lineHeight: '1.6',
              fontSize: '16px'
            }}
            onInput={(e) => handleContentChange(e.target.innerHTML)}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            dangerouslySetInnerHTML={{ __html: content }}
            data-placeholder={placeholder}
          />
        ) : (
          <div className="min-h-64 p-6 prose prose-gray max-w-none">
            <div dangerouslySetInnerHTML={{ __html: content || '<p class="text-gray-500 italic">No content to preview</p>' }} />
          </div>
        )}

        {/* Placeholder */}
        {!content && !isPreview && (
          <div className="absolute top-6 left-6 text-gray-400 pointer-events-none">
            {placeholder}
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="bg-gray-50 border-t border-gray-200 px-6 py-3">
        <div className="flex items-center justify-between text-sm text-gray-500 
          <div className="flex items-center space-x-4">
            <span>{wordCount} words</span>
            <span>{charCount} characters</span>
          </div>
          
          {autoSave && (
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span>Auto-saving...</span>
            </div>
          )}
        </div>
      </div>
    </motion.div>
  )
}

// Quick formatting shortcuts
export const EditorShortcuts = () => {
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case 'b':
            e.preventDefault()
            document.execCommand('bold')
            break
          case 'i':
            e.preventDefault()
            document.execCommand('italic')
            break
          case 'u':
            e.preventDefault()
            document.execCommand('underline')
            break
          default:
            break
        }
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [])

  return null
}
