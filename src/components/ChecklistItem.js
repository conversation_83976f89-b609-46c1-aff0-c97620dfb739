'use client'

import { useState, useEffect } from 'react'
import { Check, AlertCircle, TrendingUp, StickyNote, ChevronDown, ChevronUp, Sparkles } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { useStore } from '@/store/useStore'
import Button from './ui/Button'

export default function ChecklistItem({
  id,
  text,
  checked,
  onChange,
  priority = 'normal',
  description = null
}) {
  const [isAnimating, setIsAnimating] = useState(false)
  const [showNotes, setShowNotes] = useState(false)
  const [noteText, setNoteText] = useState('')
  const { updateChecklistNote, checklist } = useStore()

  // Load existing note
  useEffect(() => {
    const existingNote = checklist.notes?.[id] || ''
    setNoteText(existingNote)
  }, [id, checklist.notes])

  const handleChange = () => {
    setIsAnimating(true)
    onChange(id, !checked)

    // Reset animation after completion
    setTimeout(() => setIsAnimating(false), 600)
  }

  const handleNoteChange = (value) => {
    setNoteText(value)
    updateChecklistNote(id, value)
  }

  const getPriorityIcon = () => {
    switch (priority) {
      case 'high':
        return <AlertCircle className="w-4 h-4 text-red-500" />
      case 'medium':
        return <TrendingUp className="w-4 h-4 text-amber-500" />
      default:
        return null
    }
  }

  const getPriorityStyles = () => {
    if (checked) {
      return 'bg-gradient-to-r from-success-50 to-emerald-50  border-success-200 shadow-success-100 
    }

    switch (priority) {
      case 'high':
        return 'bg-gradient-to-r from-danger-50 to-pink-50  border-danger-200 hover:border-danger-300 hover:shadow-danger-100 
      case 'medium':
        return 'bg-gradient-to-r from-warning-50 to-orange-50  border-warning-200 hover:border-warning-300 hover:shadow-warning-100 
      default:
        return 'bg-white border-gray-200 hover:border-primary-300 hover:shadow-primary-100 
    }
  }

  const hasNote = noteText.trim().length > 0

  return (
    <motion.div
      layout
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className={`
        relative rounded-2xl border-2 transition-all duration-300 overflow-hidden
        ${getPriorityStyles()}
        ${checked ? 'shadow-soft-lg' : 'shadow-soft hover:shadow-soft-lg'}
        group
      `}
      whileHover={{ scale: 1.01 }}
      whileTap={{ scale: 0.99 }}
    >
      {/* Main Content */}
      <div
        className="p-6 cursor-pointer"
        onClick={handleChange}
      >
        <div className="flex items-start space-x-4">
          {/* Custom Checkbox */}
          <div className="relative flex-shrink-0 mt-1">
            <motion.div
              className={`
                w-7 h-7 rounded-xl border-2 flex items-center justify-center transition-all duration-300 shadow-sm
                ${checked
                  ? 'bg-gradient-to-r from-success-500 to-emerald-500 border-success-500 shadow-success-200'
                  : 'bg-white border-gray-300 group-hover:border-primary-400 group-hover:shadow-primary-200 
                }
              `}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <AnimatePresence>
                {checked && (
                  <motion.div
                    initial={{ scale: 0, opacity: 0, rotate: -90 }}
                    animate={{ scale: 1, opacity: 1, rotate: 0 }}
                    exit={{ scale: 0, opacity: 0, rotate: 90 }}
                    transition={{ duration: 0.3, type: "spring", stiffness: 500, damping: 30 }}
                  >
                    <Check className="w-4 h-4 text-white" strokeWidth={3} />
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>

            {/* Ripple effect */}
            <AnimatePresence>
              {isAnimating && (
                <motion.div
                  className="absolute inset-0 rounded-xl bg-success-400"
                  initial={{ scale: 1, opacity: 0.4 }}
                  animate={{ scale: 3, opacity: 0 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.8, ease: "easeOut" }}
                />
              )}
            </AnimatePresence>

            {/* Success sparkles */}
            <AnimatePresence>
              {checked && isAnimating && (
                <div className="absolute inset-0 pointer-events-none">
                  {[...Array(8)].map((_, i) => (
                    <motion.div
                      key={i}
                      className="absolute w-1 h-1 bg-success-400 rounded-full"
                      style={{
                        left: '50%',
                        top: '50%',
                      }}
                      initial={{ scale: 0, x: 0, y: 0 }}
                      animate={{
                        scale: [0, 1, 0],
                        x: Math.cos(i * 45 * Math.PI / 180) * 30,
                        y: Math.sin(i * 45 * Math.PI / 180) * 30,
                      }}
                      transition={{
                        duration: 0.8,
                        delay: 0.1,
                        ease: "easeOut"
                      }}
                    />
                  ))}
                </div>
              )}
            </AnimatePresence>
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2 mb-1">
              {getPriorityIcon()}
              <motion.p
                className={`
                  font-semibold transition-all duration-300 text-lg
                  ${checked
                    ? 'text-success-700 line-through opacity-75'
                    : 'text-gray-900 group-hover:text-primary-700 
                  }
                `}
                animate={checked ? { scale: [1, 1.02, 1] } : {}}
                transition={{ duration: 0.3 }}
              >
                {text}
              </motion.p>
            </div>

            {description && (
              <p className={`
                text-sm transition-all duration-300 mb-3
                ${checked
                  ? 'text-success-600 opacity-75'
                  : 'text-gray-600 
                }
              `}>
                {description}
              </p>
            )}

            {/* Note Preview */}
            {hasNote && !showNotes && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="mt-2 p-2 bg-gray-50 rounded-lg border-l-4 border-primary-500"
              >
                <p className="text-xs text-gray-600 line-clamp-2">
                  {noteText}
                </p>
              </motion.div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col items-center space-y-2">
            {/* Priority Badge */}
            {priority !== 'normal' && (
              <motion.div
                className={`
                  px-2 py-1 rounded-full text-xs font-medium
                  ${priority === 'high'
                    ? 'bg-danger-100 text-danger-700 
                    : 'bg-warning-100 text-warning-700 
                  }
                `}
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.2 }}
              >
                {priority}
              </motion.div>
            )}

            {/* Notes Button */}
            <motion.button
              onClick={(e) => {
                e.stopPropagation()
                setShowNotes(!showNotes)
              }}
              className={`
                p-2 rounded-lg transition-all duration-200
                ${hasNote
                  ? 'bg-primary-100 text-primary-600 
                  : 'bg-gray-100 text-gray-500 hover:bg-primary-50 hover:text-primary-600 
                }
              `}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              title={hasNote ? 'View note' : 'Add note'}
            >
              <StickyNote className="w-4 h-4" />
            </motion.button>

            {/* Completion Status */}
            <motion.div
              className={`
                w-3 h-3 rounded-full transition-all duration-300
                ${checked
                  ? 'bg-success-500 shadow-lg shadow-success-200 
                  : 'bg-gray-300 group-hover:bg-primary-400 
                }
              `}
              animate={checked ? { scale: [1, 1.3, 1] } : { scale: 1 }}
              transition={{ duration: 0.4, delay: 0.1 }}
            />
          </div>
        </div>
      </div>

      {/* Notes Section */}
      <AnimatePresence>
        {showNotes && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="border-t border-gray-200 bg-gray-50 
          >
            <div className="p-4">
              <div className="flex items-center justify-between mb-3">
                <label className="text-sm font-medium text-gray-700 
                  Personal Notes
                </label>
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    setShowNotes(false)
                  }}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <ChevronUp className="w-4 h-4" />
                </button>
              </div>

              <textarea
                value={noteText}
                onChange={(e) => handleNoteChange(e.target.value)}
                onClick={(e) => e.stopPropagation()}
                placeholder="Add your personal notes, observations, or reminders for this checklist item..."
                className="w-full h-24 p-3 text-sm border border-gray-200 rounded-lg bg-white text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none transition-all duration-200"
                rows={3}
              />

              {noteText.trim() && (
                <div className="mt-2 text-xs text-gray-500 
                  {noteText.trim().length} characters
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Completion Celebration */}
      <AnimatePresence>
        {checked && isAnimating && (
          <motion.div
            className="absolute inset-0 pointer-events-none overflow-hidden rounded-2xl"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            {/* Confetti Effect */}
            {[...Array(12)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-2 h-2 rounded-full"
                style={{
                  left: '50%',
                  top: '50%',
                  backgroundColor: ['#10b981', '#3b82f6', '#f59e0b', '#ef4444'][i % 4],
                }}
                initial={{ scale: 0, x: 0, y: 0, rotate: 0 }}
                animate={{
                  scale: [0, 1, 0],
                  x: Math.cos(i * 30 * Math.PI / 180) * (50 + Math.random() * 30),
                  y: Math.sin(i * 30 * Math.PI / 180) * (50 + Math.random() * 30),
                  rotate: 360,
                }}
                transition={{
                  duration: 1,
                  delay: 0.1 + Math.random() * 0.2,
                  ease: "easeOut"
                }}
              />
            ))}

            {/* Success Glow */}
            <motion.div
              className="absolute inset-0 bg-success-400/20 rounded-2xl"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: [0, 0.5, 0], scale: [0.8, 1.1, 1] }}
              transition={{ duration: 0.8 }}
            />
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  )
}
