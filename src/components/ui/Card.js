/**
 * Modern Card Component
 * Supports glassmorphism, neumorphism, and various interactive states
 */

'use client'

import { forwardRef } from 'react'
import { motion } from 'framer-motion'

const Card = forwardRef(({
  children,
  variant = 'default',
  size = 'md',
  interactive = false,
  className = '',
  onClick,
  ...props
}, ref) => {
  const baseClasses = `
    rounded-2xl transition-all duration-300 ease-in-out
    ${interactive ? 'cursor-pointer' : ''}
  `

  const variants = {
    default: `
      bg-[#F5F5F1]/5 backdrop-blur-xl
      border border-[#F4C46A]/20
      shadow-lg shadow-black/30
    `,
    glass: `
      bg-[#F5F5F1]/8 backdrop-blur-xl
      border border-[#F4C46A]/30
      shadow-lg shadow-black/30 hover:shadow-2xl hover:shadow-[#F4C46A]/20
    `,
    'glass-gradient': `
      bg-gradient-to-br from-[#F5F5F1]/10 to-[#F5F5F1]/5 backdrop-blur-xl
      border border-[#1FC77D]/20
      shadow-2xl shadow-black/40
    `,
    success: `
      bg-gradient-to-br from-[#1FC77D]/20 to-[#1FC77D]/10
      border border-[#1FC77D]/30
      shadow-lg hover:shadow-xl hover:shadow-[#1FC77D]/20
    `,
    warning: `
      bg-gradient-to-br from-[#F4C46A]/20 to-[#F4C46A]/10
      border border-[#F4C46A]/30
      shadow-lg hover:shadow-xl hover:shadow-[#F4C46A]/20
    `,
    danger: `
      bg-gradient-to-br from-[#F25D5D]/20 to-[#F25D5D]/10
      border border-[#F25D5D]/30
      shadow-lg hover:shadow-xl hover:shadow-[#F25D5D]/20
    `,
    neumorphic: `
      bg-gray-100 
      shadow-[8px_8px_16px_#d1d5db,-8px_-8px_16px_#ffffff]
      
      hover:shadow-[4px_4px_8px_#d1d5db,-4px_-4px_8px_#ffffff]
      
    `,
    gradient: `
      bg-gradient-to-br from-white to-gray-50 
      
      border border-gray-200 
      shadow-soft hover:shadow-soft-lg
    `,
    elevated: `
      bg-white 
      shadow-lg hover:shadow-xl
      border-0
    `,
    outlined: `
      bg-transparent border-2 border-gray-200 
      hover:border-primary-300 
      hover:bg-gray-50 
    `,
  }

  const sizes = {
    xs: 'p-3',
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8',
    xl: 'p-10',
  }

  const interactiveClasses = interactive ? `
    hover:scale-[1.02] active:scale-[0.98]
    hover:shadow-glow
  ` : ''

  return (
    <motion.div
      ref={ref}
      className={`
        ${baseClasses}
        ${variants[variant]}
        ${sizes[size]}
        ${interactiveClasses}
        ${className}
      `}
      onClick={onClick}
      whileHover={interactive ? { y: -2 } : {}}
      whileTap={interactive ? { scale: 0.98 } : {}}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      {...props}
    >
      {children}
    </motion.div>
  )
})

Card.displayName = 'Card'

// Card Header Component
export const CardHeader = ({ children, className = '', ...props }) => (
  <div className={`mb-4 ${className}`} {...props}>
    {children}
  </div>
)

// Card Title Component
export const CardTitle = ({ children, className = '', ...props }) => (
  <h3 className={`text-xl font-bold text-gray-900 ${className}`} {...props}>
    {children}
  </h3>
)

// Card Description Component
export const CardDescription = ({ children, className = '', ...props }) => (
  <p className={`text-gray-600 ${className}`} {...props}>
    {children}
  </p>
)

// Card Content Component
export const CardContent = ({ children, className = '', ...props }) => (
  <div className={`${className}`} {...props}>
    {children}
  </div>
)

// Card Footer Component
export const CardFooter = ({ children, className = '', ...props }) => (
  <div className={`mt-4 pt-4 border-t border-gray-200 ${className}`} {...props}>
    {children}
  </div>
)

// Premium Stats Card Component
export const StatsCard = ({
  title,
  value,
  change,
  changeType = 'positive',
  icon: Icon,
  className = '',
  ...props
}) => (
  <motion.div
    className={`
      glass-effect rounded-2xl p-6 text-center group cursor-pointer
      border border-white/10 hover:border-brand-emerald/30
      transition-all duration-300 hover:shadow-2xl hover:shadow-brand-emerald/10
      ${className}
    `}
    whileHover={{
      y: -5,
      scale: 1.02,
      transition: { type: "spring", stiffness: 300, damping: 20 }
    }}
    {...props}
  >
    <div className="flex items-center justify-center mb-4">
      <motion.div
        className={`
          w-14 h-14 rounded-2xl flex items-center justify-center shadow-lg
          ${changeType === 'positive'
            ? 'bg-gradient-to-br from-brand-emerald to-green-600'
            : changeType === 'negative'
            ? 'bg-gradient-to-br from-brand-red to-red-600'
            : 'bg-gradient-to-br from-brand-gold to-yellow-600'
          }
        `}
        whileHover={{
          scale: 1.1,
          rotate: 5,
          transition: { type: "spring", stiffness: 400 }
        }}
      >
        <Icon className="w-7 h-7 text-white" />
      </motion.div>
    </div>

    <h3 className="text-3xl font-heading font-bold text-brand-ivory mb-2 group-hover:text-brand-emerald transition-colors">
      {value}
    </h3>

    <p className="text-brand-gray text-sm mb-3 group-hover:text-brand-ivory/80 transition-colors">
      {title}
    </p>

    {change && (
      <div className={`
        text-xs font-medium px-3 py-1 rounded-full inline-block
        ${changeType === 'positive'
          ? 'text-brand-emerald bg-brand-emerald/10'
          : changeType === 'negative'
          ? 'text-brand-red bg-brand-red/10'
          : 'text-brand-gold bg-brand-gold/10'
        }
      `}>
        {change}
      </div>
    )}
  </motion.div>
)

// Feature Card Component
export const FeatureCard = ({ 
  title, 
  description, 
  icon: Icon,
  action,
  className = '',
  ...props 
}) => (
  <Card variant="glass" interactive className={className} {...props}>
    <div className="flex items-start space-x-4">
      <div className="w-10 h-10 bg-gradient-to-r from-primary-500 to-primary-600 rounded-lg flex items-center justify-center flex-shrink-0">
        <Icon className="w-5 h-5 text-white" />
      </div>
      <div className="flex-1">
        <h3 className="font-semibold text-gray-900 mb-1">{title}</h3>
        <p className="text-gray-600 text-sm mb-3">{description}</p>
        {action}
      </div>
    </div>
  </Card>
)

export default Card
