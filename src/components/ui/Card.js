/**
 * Modern Card Component
 * Supports glassmorphism, neumorphism, and various interactive states
 */

'use client'

import { forwardRef } from 'react'
import { motion } from 'framer-motion'

const Card = forwardRef(({
  children,
  variant = 'default',
  size = 'md',
  interactive = false,
  className = '',
  onClick,
  ...props
}, ref) => {
  const baseClasses = `
    rounded-2xl transition-all duration-300 ease-in-out
    ${interactive ? 'cursor-pointer' : ''}
  `

  const variants = {
    default: `
      bg-white/5 backdrop-blur-md
      border border-white/10
      shadow-lg shadow-black/20
    `,
    glass: `
      bg-white/5 backdrop-blur-md
      border border-white/10
      shadow-lg shadow-black/20 hover:shadow-2xl hover:shadow-brand-emerald/10
    `,
    'glass-gradient': `
      bg-gradient-to-br from-white/8 to-white/3 backdrop-blur-xl
      border border-white/20
      shadow-2xl shadow-black/30
    `,
    success: `
      bg-gradient-to-br from-success-50 to-emerald-50
      dark:from-success-900/20 dark:to-emerald-900/20
      border border-success-200 dark:border-success-700
      shadow-lg hover:shadow-xl
    `,
    warning: `
      bg-gradient-to-br from-warning-50 to-orange-50
      dark:from-warning-900/20 dark:to-orange-900/20
      border border-warning-200 dark:border-warning-700
      shadow-lg hover:shadow-xl
    `,
    danger: `
      bg-gradient-to-br from-red-50 to-rose-50
      dark:from-red-900/20 dark:to-rose-900/20
      border border-red-200 dark:border-red-700
      shadow-lg hover:shadow-xl
    `,
    neumorphic: `
      bg-gray-100 dark:bg-dark-900
      shadow-[8px_8px_16px_#d1d5db,-8px_-8px_16px_#ffffff]
      dark:shadow-[8px_8px_16px_#0f172a,-8px_-8px_16px_#1e293b]
      hover:shadow-[4px_4px_8px_#d1d5db,-4px_-4px_8px_#ffffff]
      dark:hover:shadow-[4px_4px_8px_#0f172a,-4px_-4px_8px_#1e293b]
    `,
    gradient: `
      bg-gradient-to-br from-white to-gray-50 
      dark:from-dark-800 dark:to-dark-900
      border border-gray-200 dark:border-dark-700
      shadow-soft hover:shadow-soft-lg
    `,
    elevated: `
      bg-white dark:bg-dark-800
      shadow-lg hover:shadow-xl
      border-0
    `,
    outlined: `
      bg-transparent border-2 border-gray-200 dark:border-dark-600
      hover:border-primary-300 dark:hover:border-primary-600
      hover:bg-gray-50 dark:hover:bg-dark-800/50
    `,
  }

  const sizes = {
    xs: 'p-3',
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8',
    xl: 'p-10',
  }

  const interactiveClasses = interactive ? `
    hover:scale-[1.02] active:scale-[0.98]
    hover:shadow-glow
  ` : ''

  return (
    <motion.div
      ref={ref}
      className={`
        ${baseClasses}
        ${variants[variant]}
        ${sizes[size]}
        ${interactiveClasses}
        ${className}
      `}
      onClick={onClick}
      whileHover={interactive ? { y: -2 } : {}}
      whileTap={interactive ? { scale: 0.98 } : {}}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      {...props}
    >
      {children}
    </motion.div>
  )
})

Card.displayName = 'Card'

// Card Header Component
export const CardHeader = ({ children, className = '', ...props }) => (
  <div className={`mb-4 ${className}`} {...props}>
    {children}
  </div>
)

// Card Title Component
export const CardTitle = ({ children, className = '', ...props }) => (
  <h3 className={`text-xl font-bold text-gray-900 dark:text-white ${className}`} {...props}>
    {children}
  </h3>
)

// Card Description Component
export const CardDescription = ({ children, className = '', ...props }) => (
  <p className={`text-gray-600 dark:text-gray-400 ${className}`} {...props}>
    {children}
  </p>
)

// Card Content Component
export const CardContent = ({ children, className = '', ...props }) => (
  <div className={`${className}`} {...props}>
    {children}
  </div>
)

// Card Footer Component
export const CardFooter = ({ children, className = '', ...props }) => (
  <div className={`mt-4 pt-4 border-t border-gray-200 dark:border-dark-700 ${className}`} {...props}>
    {children}
  </div>
)

// Premium Stats Card Component
export const StatsCard = ({
  title,
  value,
  change,
  changeType = 'positive',
  icon: Icon,
  className = '',
  ...props
}) => (
  <motion.div
    className={`
      glass-effect rounded-2xl p-6 text-center group cursor-pointer
      border border-white/10 hover:border-brand-emerald/30
      transition-all duration-300 hover:shadow-2xl hover:shadow-brand-emerald/10
      ${className}
    `}
    whileHover={{
      y: -5,
      scale: 1.02,
      transition: { type: "spring", stiffness: 300, damping: 20 }
    }}
    {...props}
  >
    <div className="flex items-center justify-center mb-4">
      <motion.div
        className={`
          w-14 h-14 rounded-2xl flex items-center justify-center shadow-lg
          ${changeType === 'positive'
            ? 'bg-gradient-to-br from-brand-emerald to-green-600'
            : changeType === 'negative'
            ? 'bg-gradient-to-br from-brand-red to-red-600'
            : 'bg-gradient-to-br from-brand-gold to-yellow-600'
          }
        `}
        whileHover={{
          scale: 1.1,
          rotate: 5,
          transition: { type: "spring", stiffness: 400 }
        }}
      >
        <Icon className="w-7 h-7 text-white" />
      </motion.div>
    </div>

    <h3 className="text-3xl font-heading font-bold text-brand-ivory mb-2 group-hover:text-brand-emerald transition-colors">
      {value}
    </h3>

    <p className="text-brand-gray text-sm mb-3 group-hover:text-brand-ivory/80 transition-colors">
      {title}
    </p>

    {change && (
      <div className={`
        text-xs font-medium px-3 py-1 rounded-full inline-block
        ${changeType === 'positive'
          ? 'text-brand-emerald bg-brand-emerald/10'
          : changeType === 'negative'
          ? 'text-brand-red bg-brand-red/10'
          : 'text-brand-gold bg-brand-gold/10'
        }
      `}>
        {change}
      </div>
    )}
  </motion.div>
)

// Feature Card Component
export const FeatureCard = ({ 
  title, 
  description, 
  icon: Icon,
  action,
  className = '',
  ...props 
}) => (
  <Card variant="glass" interactive className={className} {...props}>
    <div className="flex items-start space-x-4">
      <div className="w-10 h-10 bg-gradient-to-r from-primary-500 to-primary-600 rounded-lg flex items-center justify-center flex-shrink-0">
        <Icon className="w-5 h-5 text-white" />
      </div>
      <div className="flex-1">
        <h3 className="font-semibold text-gray-900 dark:text-white mb-1">{title}</h3>
        <p className="text-gray-600 dark:text-gray-400 text-sm mb-3">{description}</p>
        {action}
      </div>
    </div>
  </Card>
)

export default Card
