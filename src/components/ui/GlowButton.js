'use client'

import { motion } from 'framer-motion'
import { forwardRef } from 'react'

const GlowButton = forwardRef(({
  children,
  variant = 'emerald',
  size = 'md',
  className = '',
  onClick,
  disabled = false,
  ...props
}, ref) => {
  const variants = {
    emerald: `
      bg-gradient-to-r from-brand-emerald to-green-600 
      text-black font-semibold
      shadow-lg shadow-brand-emerald/30
      hover:shadow-brand-emerald/50 hover:shadow-xl
      hover:from-green-400 hover:to-green-500
    `,
    gold: `
      bg-gradient-to-r from-brand-gold to-yellow-600 
      text-black font-semibold
      shadow-lg shadow-brand-gold/30
      hover:shadow-brand-gold/50 hover:shadow-xl
      hover:from-yellow-400 hover:to-yellow-500
    `,
    red: `
      bg-gradient-to-r from-brand-red to-red-600 
      text-white font-semibold
      shadow-lg shadow-brand-red/30
      hover:shadow-brand-red/50 hover:shadow-xl
      hover:from-red-400 hover:to-red-500
    `,
    glass: `
      bg-white/10 backdrop-blur-md border border-white/20
      text-brand-ivory font-semibold
      shadow-lg shadow-black/20
      hover:bg-white/20 hover:shadow-xl
    `
  }

  const sizes = {
    sm: 'px-4 py-2 text-sm rounded-lg',
    md: 'px-6 py-3 text-base rounded-xl',
    lg: 'px-8 py-4 text-lg rounded-2xl',
    xl: 'px-10 py-5 text-xl rounded-2xl'
  }

  return (
    <motion.button
      ref={ref}
      className={`
        relative inline-flex items-center justify-center
        transition-all duration-300 ease-out
        focus:outline-none focus:ring-2 focus:ring-brand-emerald/50 focus:ring-offset-2 focus:ring-offset-transparent
        disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none
        ${variants[variant]}
        ${sizes[size]}
        ${className}
      `}
      disabled={disabled}
      onClick={onClick}
      whileHover={!disabled ? { 
        scale: 1.05,
        transition: { type: "spring", stiffness: 400, damping: 10 }
      } : {}}
      whileTap={!disabled ? { 
        scale: 0.95,
        transition: { type: "spring", stiffness: 400, damping: 10 }
      } : {}}
      initial={{ scale: 1 }}
      {...props}
    >
      {/* Breathing pulse effect */}
      <motion.div
        className="absolute inset-0 rounded-inherit"
        animate={{
          boxShadow: [
            `0 0 20px rgba(31, 199, 125, 0.3)`,
            `0 0 30px rgba(31, 199, 125, 0.5)`,
            `0 0 20px rgba(31, 199, 125, 0.3)`
          ]
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
      
      {children}
    </motion.button>
  )
})

GlowButton.displayName = 'GlowButton'

export default GlowButton
