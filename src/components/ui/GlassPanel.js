'use client'

import { motion } from 'framer-motion'
import { forwardRef } from 'react'

const GlassPanel = forwardRef(({
  children,
  variant = 'default',
  className = '',
  hover = true,
  ...props
}, ref) => {
  const variants = {
    default: `
      bg-white/5 backdrop-blur-md 
      border border-white/10 
      shadow-lg shadow-black/20
    `,
    emerald: `
      bg-white/5 backdrop-blur-md 
      border border-brand-emerald/20 
      shadow-lg shadow-brand-emerald/10
    `,
    gold: `
      bg-white/5 backdrop-blur-md 
      border border-brand-gold/20 
      shadow-lg shadow-brand-gold/10
    `,
    premium: `
      bg-gradient-to-br from-white/8 to-white/3 backdrop-blur-xl
      border border-white/20 
      shadow-2xl shadow-black/30
    `
  }

  const hoverEffects = hover ? {
    whileHover: {
      y: -5,
      boxShadow: "0 20px 40px rgba(0, 0, 0, 0.3)",
      transition: { type: "spring", stiffness: 300, damping: 20 }
    }
  } : {}

  return (
    <motion.div
      ref={ref}
      className={`
        rounded-2xl p-6 transition-all duration-300
        ${variants[variant]}
        ${className}
      `}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, ease: "easeOut" }}
      {...hoverEffects}
      {...props}
    >
      {children}
    </motion.div>
  )
})

GlassPanel.displayName = 'GlassPanel'

export default GlassPanel
