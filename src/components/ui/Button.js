/**
 * Modern Button Component
 * Supports multiple variants, sizes, and states with smooth animations
 */

'use client'

import { forwardRef } from 'react'
import { motion } from 'framer-motion'
import { Loader2 } from 'lucide-react'

const Button = forwardRef(({
  children,
  variant = 'primary',
  size = 'md',
  loading = false,
  disabled = false,
  className = '',
  icon: Icon,
  iconPosition = 'left',
  fullWidth = false,
  onClick,
  ...props
}, ref) => {
  const baseClasses = `
    relative inline-flex items-center justify-center font-semibold rounded-xl
    transition-all duration-300 ease-in-out transform backdrop-blur-xl
    focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent
    disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none
    overflow-hidden group
    ${fullWidth ? 'w-full' : ''}
  `

  const variants = {
    primary: `
      bg-gradient-to-r from-[#F4C46A] to-[#1FC77D]
      hover:from-[#1FC77D] hover:to-[#F4C46A]
      text-[#0A0F0F] font-bold shadow-lg hover:shadow-xl hover:shadow-[#F4C46A]/40
      focus:ring-[#F4C46A]/50 border border-[#F4C46A]/30
      active:scale-95
    `,
    secondary: `
      bg-gradient-to-r from-[#9CA3AF]/20 to-[#9CA3AF]/30
      hover:from-[#9CA3AF]/30 hover:to-[#9CA3AF]/40
      text-[#F5F5F1] shadow-lg hover:shadow-xl border border-[#9CA3AF]/20
      focus:ring-[#9CA3AF]/50
      active:scale-95
    `,
    accent: `
      bg-gradient-to-r from-[#F4C46A] to-[#F4C46A]/80
      hover:from-[#F4C46A]/90 hover:to-[#F4C46A]
      text-[#0A0F0F] font-bold shadow-lg hover:shadow-xl hover:shadow-[#F4C46A]/40
      focus:ring-[#F4C46A]/50 border border-[#F4C46A]/30
      active:scale-95
    `,
    success: `
      bg-gradient-to-r from-[#1FC77D] to-[#1FC77D]/80
      hover:from-[#1FC77D]/90 hover:to-[#1FC77D]
      text-[#F5F5F1] font-bold shadow-lg hover:shadow-xl hover:shadow-[#1FC77D]/40
      focus:ring-[#1FC77D]/50 border border-[#1FC77D]/30
      active:scale-95
    `,
    danger: `
      bg-gradient-to-r from-[#F25D5D] to-[#F25D5D]/80
      hover:from-[#F25D5D]/90 hover:to-[#F25D5D]
      text-[#F5F5F1] font-bold shadow-lg hover:shadow-xl hover:shadow-[#F25D5D]/40
      focus:ring-[#F25D5D]/50 border border-[#F25D5D]/30
      active:scale-95
    `,
    ghost: `
      bg-[#F5F5F1]/10 hover:bg-[#F5F5F1]/20 border border-[#F4C46A]/20 hover:border-[#F4C46A]/40
      text-[#F5F5F1] hover:text-[#F4C46A]
      shadow-sm hover:shadow-lg hover:shadow-[#F4C46A]/20
      focus:ring-[#F4C46A]/50
      active:scale-95
    `,
    outline: `
      bg-transparent border-2 border-[#F4C46A]/60
      hover:bg-[#F4C46A] hover:text-[#0A0F0F]
      text-[#F4C46A] font-bold
      focus:ring-[#F4C46A]/50
      active:scale-95 shadow-lg hover:shadow-xl hover:shadow-[#F4C46A]/30
    `,
  }

  const sizes = {
    xs: 'px-3 py-1.5 text-xs',
    sm: 'px-4 py-2 text-sm',
    md: 'px-6 py-3 text-base',
    lg: 'px-8 py-4 text-lg',
    xl: 'px-10 py-5 text-xl',
  }

  const iconSizes = {
    xs: 'w-3 h-3',
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6',
    xl: 'w-7 h-7',
  }

  const isDisabled = disabled || loading

  return (
    <motion.button
      ref={ref}
      className={`
        ${baseClasses}
        ${variants[variant]}
        ${sizes[size]}
        ${className}
      `}
      disabled={isDisabled}
      onClick={onClick}
      whileHover={!isDisabled ? { scale: 1.02 } : {}}
      whileTap={!isDisabled ? { scale: 0.98 } : {}}
      {...props}
    >
      {loading && (
        <Loader2 className={`${iconSizes[size]} animate-spin ${Icon || children ? 'mr-2' : ''}`} />
      )}
      
      {Icon && !loading && iconPosition === 'left' && (
        <Icon className={`${iconSizes[size]} ${children ? 'mr-2' : ''}`} />
      )}
      
      {children}
      
      {Icon && !loading && iconPosition === 'right' && (
        <Icon className={`${iconSizes[size]} ${children ? 'ml-2' : ''}`} />
      )}
    </motion.button>
  )
})

Button.displayName = 'Button'

export default Button

// Specialized button variants
export const IconButton = forwardRef(({
  icon: Icon,
  size = 'md',
  variant = 'ghost',
  className = '',
  ...props
}, ref) => {
  const iconSizes = {
    xs: 'w-8 h-8',
    sm: 'w-9 h-9',
    md: 'w-10 h-10',
    lg: 'w-12 h-12',
    xl: 'w-14 h-14',
  }

  return (
    <Button
      ref={ref}
      variant={variant}
      className={`${iconSizes[size]} p-0 ${className}`}
      {...props}
    >
      <Icon className="w-5 h-5" />
    </Button>
  )
})

IconButton.displayName = 'IconButton'

export const FloatingActionButton = forwardRef(({
  icon: Icon,
  className = '',
  ...props
}, ref) => {
  return (
    <motion.button
      ref={ref}
      className={`
        fixed bottom-6 right-6 w-14 h-14 bg-gradient-to-r from-primary-600 to-primary-700
        hover:from-primary-700 hover:to-primary-800 text-white rounded-full
        shadow-lg hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-primary-500
        flex items-center justify-center z-50 ${className}
      `}
      whileHover={{ scale: 1.1 }}
      whileTap={{ scale: 0.9 }}
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      transition={{ type: "spring", stiffness: 260, damping: 20 }}
      {...props}
    >
      <Icon className="w-6 h-6" />
    </motion.button>
  )
})

FloatingActionButton.displayName = 'FloatingActionButton'
