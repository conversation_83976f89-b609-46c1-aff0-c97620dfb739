'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  ArrowLeft,
  CheckCircle2,
  Clock,
  Brain,
  Target,
  Lightbulb,
  Eye,
  Play,
  Pause,
  RotateCcw,
  BookOpen,
  Star,
  Award,
  ArrowRight,
  Bookmark,
  BookmarkCheck,
  MessageSquare
} from 'lucide-react'
import { useStore, useCourse } from '@/store/useStore'
import Button from '@/components/ui/Button'
import Card, { CardHeader, CardTitle, CardContent } from '@/components/ui/Card'
import InteractiveQuiz from './InteractiveQuiz'
import toast from 'react-hot-toast'

const lessonTypeIcons = {
  theory: Brain,
  practical: Target,
  strategy: Lightbulb,
  interactive: Eye
}

const lessonTypeColors = {
  theory: 'from-blue-500 to-blue-600',
  practical: 'from-green-500 to-green-600',
  strategy: 'from-purple-500 to-purple-600',
  interactive: 'from-orange-500 to-orange-600'
}

export default function CourseLesson({ module, lesson, onBack, onComplete, isCompleted }) {
  const [currentStep, setCurrentStep] = useState(0)
  const [showQuiz, setShowQuiz] = useState(false)
  const [lessonStartTime, setLessonStartTime] = useState(null)
  const [timeSpent, setTimeSpent] = useState(0)
  const [userNotes, setUserNotes] = useState('')
  const course = useCourse()
  const { saveCourseNote, addBookmark, removeBookmark } = useStore()

  const TypeIcon = lessonTypeIcons[lesson.type] || BookOpen
  const totalSteps = lesson.keyPoints.length + 1 // Content + key points
  const progress = Math.round(((currentStep + 1) / totalSteps) * 100)

  useEffect(() => {
    setLessonStartTime(Date.now())
    const interval = setInterval(() => {
      if (lessonStartTime) {
        setTimeSpent(Math.floor((Date.now() - lessonStartTime) / 1000))
      }
    }, 1000)

    return () => clearInterval(interval)
  }, [lessonStartTime])

  useEffect(() => {
    // Load existing notes
    const existingNote = course.notes[`${module.id}-${lesson.id}`]
    if (existingNote) {
      setUserNotes(existingNote)
    }
  }, [course.notes, module.id, lesson.id])

  const handleNext = () => {
    if (currentStep < totalSteps - 1) {
      setCurrentStep(currentStep + 1)
    } else if (lesson.type === 'interactive') {
      setShowQuiz(true)
    } else {
      handleComplete()
    }
  }

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleComplete = () => {
    if (!isCompleted) {
      onComplete()
    }
    onBack()
  }

  const handleSaveNotes = () => {
    saveCourseNote(module.id, lesson.id, userNotes)
    toast.success('Notes saved!')
  }

  const isBookmarked = course.bookmarks.some(b => 
    b.moduleId === module.id && b.lessonId === lesson.id
  )

  const toggleBookmark = () => {
    const existingBookmark = course.bookmarks.find(b => 
      b.moduleId === module.id && b.lessonId === lesson.id
    )
    
    if (existingBookmark) {
      removeBookmark(existingBookmark.id)
      toast.success('Bookmark removed')
    } else {
      addBookmark(module.id, lesson.id, lesson.title)
      toast.success('Lesson bookmarked!')
    }
  }

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  if (showQuiz) {
    return (
      <InteractiveQuiz
        module={module}
        lesson={lesson}
        onComplete={handleComplete}
        onBack={() => setShowQuiz(false)}
      />
    )
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Lesson Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            icon={ArrowLeft}
            onClick={onBack}
          >
            Back to Module
          </Button>
          
          <div className="flex items-center space-x-3">
            <div className={`w-10 h-10 bg-gradient-to-r ${lessonTypeColors[lesson.type]} rounded-xl flex items-center justify-center`}>
              <TypeIcon className="w-5 h-5 text-white" />
            </div>
            <div>
              <h1 className="text-xl md:text-2xl font-bold text-gray-900 
                {lesson.title}
              </h1>
              <div className="flex items-center space-x-4 text-sm text-gray-600 
                <span>Module {module.id}, Lesson {lesson.id}</span>
                <div className="flex items-center space-x-1">
                  <Clock className="w-3 h-3" />
                  <span>{lesson.duration}</span>
                </div>
                <span className="capitalize">{lesson.type}</span>
              </div>
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <div className="text-right text-sm">
            <div className="text-gray-900 font-medium">
              Time: {formatTime(timeSpent)}
            </div>
            <div className="text-gray-600 
              Progress: {progress}%
            </div>
          </div>
          
          <Button
            variant="ghost"
            size="sm"
            icon={isBookmarked ? BookmarkCheck : Bookmark}
            onClick={toggleBookmark}
            className={isBookmarked ? 'text-yellow-500' : ''}
          />
        </div>
      </motion.div>

      {/* Progress Bar */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <Card variant="glass">
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700 
                Lesson Progress
              </span>
              <span className="text-sm text-gray-600 
                {currentStep + 1} of {totalSteps}
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <motion.div
                className={`h-2 rounded-full bg-gradient-to-r ${lessonTypeColors[lesson.type]}`}
                initial={{ width: 0 }}
                animate={{ width: `${progress}%` }}
                transition={{ duration: 0.5 }}
              />
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Lesson Content */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Card variant="default">
          <CardContent className="p-8">
            <AnimatePresence mode="wait">
              {currentStep === 0 ? (
                <motion.div
                  key="content"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  className="space-y-6"
                >
                  <div className="flex items-center space-x-3 mb-6">
                    <div className={`w-8 h-8 bg-gradient-to-r ${lessonTypeColors[lesson.type]} rounded-lg flex items-center justify-center`}>
                      <BookOpen className="w-4 h-4 text-white" />
                    </div>
                    <h2 className="text-2xl font-bold text-gray-900 
                      Lesson Overview
                    </h2>
                  </div>
                  
                  <div className="prose prose-lg max-w-none">
                    <div
                      className="text-gray-700 leading-relaxed"
                      dangerouslySetInnerHTML={{
                        __html: lesson.content
                          .replace(/\n/g, '<br>')
                          .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                          .replace(/\*(.*?)\*/g, '<em>$1</em>')
                          .replace(/^# (.*$)/gm, '<h1 class="text-2xl font-bold mb-4 text-gray-900 
                          .replace(/^## (.*$)/gm, '<h2 class="text-xl font-semibold mb-3 text-gray-800 
                          .replace(/^### (.*$)/gm, '<h3 class="text-lg font-medium mb-2 text-gray-700 
                      }}
                    />
                  </div>

                  <div className="bg-gradient-to-r from-blue-50 to-indigo-50  rounded-xl p-6 border border-blue-200 
                    <h3 className="text-lg font-semibold text-blue-900 mb-3">
                      What You'll Learn
                    </h3>
                    <ul className="space-y-2">
                      {lesson.keyPoints.map((point, index) => (
                        <li key={index} className="flex items-start space-x-2 text-blue-800 
                          <Star className="w-4 h-4 mt-0.5 flex-shrink-0" />
                          <span>{point}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </motion.div>
              ) : (
                <motion.div
                  key={`keypoint-${currentStep}`}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  className="space-y-6"
                >
                  <div className="flex items-center space-x-3 mb-6">
                    <div className={`w-8 h-8 bg-gradient-to-r ${lessonTypeColors[lesson.type]} rounded-lg flex items-center justify-center`}>
                      <Target className="w-4 h-4 text-white" />
                    </div>
                    <h2 className="text-2xl font-bold text-gray-900 
                      Key Point {currentStep}
                    </h2>
                  </div>
                  
                  <div className="bg-gradient-to-r from-gray-50 to-gray-100  rounded-xl p-8 border border-gray-200 
                    <h3 className="text-xl font-semibold text-gray-900 mb-4">
                      {lesson.keyPoints[currentStep - 1]}
                    </h3>
                    <div className="space-y-4 text-gray-700 leading-relaxed">
                      {lesson.sections && lesson.sections[currentStep - 1] ? (
                        <div>
                          <h4 className="font-semibold text-lg mb-2">{lesson.sections[currentStep - 1].title}</h4>
                          <p className="mb-3">{lesson.sections[currentStep - 1].description}</p>
                          <div className="space-y-2">
                            {lesson.sections[currentStep - 1].keyPoints.map((point, index) => (
                              <div key={index} className="flex items-start space-x-2">
                                <div className="w-2 h-2 bg-primary-500 rounded-full mt-2 flex-shrink-0"></div>
                                <span className="text-sm">{point}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      ) : (
                        <div>
                          <p className="mb-4">
                            This key learning point focuses on {lesson.keyPoints[currentStep - 1].toLowerCase()}.
                          </p>
                          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <h4 className="font-semibold text-blue-900 mb-2">💡 Key Insight</h4>
                            <p className="text-blue-800 text-sm">
                              Understanding this concept is essential for mastering {lesson.title.toLowerCase()}. 
                              Apply this knowledge through practice and real-world observation of SPY/QQQ price action.
                            </p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Interactive Elements for Different Lesson Types */}
                  {lesson.type === 'practical' && (
                    <div className="bg-green-50 rounded-xl p-6 border border-green-200 
                      <h4 className="text-lg font-semibold text-green-900 mb-3">
                        💡 Practice Exercise
                      </h4>
                      <p className="text-green-800 
                        Try applying this concept on a practice chart. Look for examples of this pattern 
                        in recent SPY/QQQ price action.
                      </p>
                    </div>
                  )}

                  {lesson.type === 'strategy' && (
                    <div className="bg-purple-50 rounded-xl p-6 border border-purple-200 
                      <h4 className="text-lg font-semibold text-purple-900 mb-3">
                        🎯 Strategy Application
                      </h4>
                      <p className="text-purple-800 
                        Consider how you would implement this strategy in your own trading. 
                        What risk management rules would you apply?
                      </p>
                    </div>
                  )}
                </motion.div>
              )}
            </AnimatePresence>
          </CardContent>
        </Card>
      </motion.div>

      {/* Notes Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
      >
        <Card variant="glass">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <MessageSquare className="w-5 h-5" />
              <span>Your Notes</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <textarea
              value={userNotes}
              onChange={(e) => setUserNotes(e.target.value)}
              placeholder="Take notes about this lesson..."
              className="w-full h-24 p-3 border border-gray-200 rounded-lg bg-white text-gray-900 resize-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
            <Button
              variant="ghost"
              size="sm"
              onClick={handleSaveNotes}
              disabled={!userNotes.trim()}
            >
              Save Notes
            </Button>
          </CardContent>
        </Card>
      </motion.div>

      {/* Navigation */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="flex items-center justify-between"
      >
        <Button
          variant="ghost"
          onClick={handlePrevious}
          disabled={currentStep === 0}
          icon={ArrowLeft}
        >
          Previous
        </Button>

        <div className="flex items-center space-x-2">
          {Array.from({ length: totalSteps }, (_, index) => (
            <div
              key={index}
              className={`w-2 h-2 rounded-full transition-colors ${
                index <= currentStep
                  ? 'bg-primary-500'
                  : 'bg-gray-300 
              }`}
            />
          ))}
        </div>

        <Button
          variant="primary"
          onClick={handleNext}
          icon={currentStep === totalSteps - 1 ? (lesson.type === 'interactive' ? Eye : CheckCircle2) : ArrowRight}
        >
          {currentStep === totalSteps - 1 
            ? (lesson.type === 'interactive' ? 'Take Quiz' : 'Complete Lesson')
            : 'Next'
          }
        </Button>
      </motion.div>
    </div>
  )
}
