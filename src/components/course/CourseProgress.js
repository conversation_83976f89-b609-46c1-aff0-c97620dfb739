'use client'

import { motion } from 'framer-motion'
import {
  Trophy,
  Star,
  Target,
  Clock,
  Award,
  TrendingUp,
  CheckCircle2,
  Calendar
} from 'lucide-react'
import { useCourse } from '@/store/useStore'
import { COURSE_MODULES, COURSE_ACHIEVEMENTS } from '@/data/courseData'
import Card, { CardHeader, CardTitle, CardContent } from '@/components/ui/Card'

export default function CourseProgress() {
  const course = useCourse()

  const totalLessons = COURSE_MODULES.reduce((total, module) => total + module.lessons.length, 0)
  const completedLessons = course.completedLessons.length
  const progressPercentage = Math.round((completedLessons / totalLessons) * 100)

  const getTimeSpent = () => {
    // Calculate estimated time spent based on completed lessons
    let totalMinutes = 0
    COURSE_MODULES.forEach(module => {
      module.lessons.forEach(lesson => {
        const lessonKey = `${module.id}-${lesson.id}`
        if (course.completedLessons.includes(lessonKey)) {
          totalMinutes += parseInt(lesson.duration) || 10
        }
      })
    })
    
    const hours = Math.floor(totalMinutes / 60)
    const minutes = totalMinutes % 60
    return hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`
  }

  const getStreakDays = () => {
    // Calculate actual learning streak based on course activity
    if (!course.lastAccessed) return 0

    const today = new Date()
    const lastAccessed = new Date(course.lastAccessed)
    const daysDiff = Math.floor((today - lastAccessed) / (1000 * 60 * 60 * 24))

    // If accessed today or yesterday, calculate streak
    if (daysDiff <= 1) {
      // For now, return a simple calculation based on completed lessons
      // This could be enhanced to track daily access patterns
      return Math.min(course.completedLessons.length, 7)
    }

    return 0
  }

  const getRecentAchievements = () => {
    return course.achievements.slice(-3).reverse()
  }

  return (
    <Card variant="glass">
      <CardHeader>
        <CardTitle className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-gradient-to-r from-primary-500 to-accent-500 rounded-lg flex items-center justify-center">
            <TrendingUp className="w-4 h-4 text-white" />
          </div>
          <span>Your Learning Progress</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Main Progress Circle */}
        <div className="flex items-center justify-center">
          <div className="relative w-32 h-32">
            {/* Background Circle */}
            <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 120 120">
              <circle
                cx="60"
                cy="60"
                r="50"
                stroke="currentColor"
                strokeWidth="8"
                fill="none"
                className="text-gray-200 
              />
              {/* Progress Circle */}
              <motion.circle
                cx="60"
                cy="60"
                r="50"
                stroke="url(#progressGradient)"
                strokeWidth="8"
                fill="none"
                strokeLinecap="round"
                strokeDasharray={`${2 * Math.PI * 50}`}
                initial={{ strokeDashoffset: 2 * Math.PI * 50 }}
                animate={{ 
                  strokeDashoffset: 2 * Math.PI * 50 * (1 - progressPercentage / 100)
                }}
                transition={{ duration: 2, ease: "easeInOut" }}
              />
              <defs>
                <linearGradient id="progressGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                  <stop offset="0%" stopColor="#3b82f6" />
                  <stop offset="100%" stopColor="#f97316" />
                </linearGradient>
              </defs>
            </svg>
            
            {/* Center Content */}
            <div className="absolute inset-0 flex flex-col items-center justify-center">
              <motion.span 
                className="text-2xl font-bold text-gray-900 
                initial={{ opacity: 0, scale: 0.5 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 1, duration: 0.5 }}
              >
                {progressPercentage}%
              </motion.span>
              <span className="text-xs text-gray-600 
            </div>
          </div>
        </div>

        {/* Progress Stats Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="text-center p-4 bg-gray-50 rounded-xl"
          >
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mx-auto mb-2">
              <CheckCircle2 className="w-4 h-4 text-white" />
            </div>
            <div className="text-lg font-bold text-gray-900 
              {completedLessons}
            </div>
            <div className="text-xs text-gray-600 
              Lessons Done
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="text-center p-4 bg-gray-50 rounded-xl"
          >
            <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center mx-auto mb-2">
              <Trophy className="w-4 h-4 text-white" />
            </div>
            <div className="text-lg font-bold text-gray-900 
              {course.completedModules.length}
            </div>
            <div className="text-xs text-gray-600 
              Modules Done
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="text-center p-4 bg-gray-50 rounded-xl"
          >
            <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mx-auto mb-2">
              <Clock className="w-4 h-4 text-white" />
            </div>
            <div className="text-lg font-bold text-gray-900 
              {getTimeSpent()}
            </div>
            <div className="text-xs text-gray-600 
              Time Spent
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="text-center p-4 bg-gray-50 rounded-xl"
          >
            <div className="w-8 h-8 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg flex items-center justify-center mx-auto mb-2">
              <Calendar className="w-4 h-4 text-white" />
            </div>
            <div className="text-lg font-bold text-gray-900 
              {getStreakDays()}
            </div>
            <div className="text-xs text-gray-600 
              Day Streak
            </div>
          </motion.div>
        </div>

        {/* Recent Achievements */}
        {course.achievements.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="space-y-3"
          >
            <h4 className="text-sm font-semibold text-gray-900 flex items-center space-x-2">
              <Award className="w-4 h-4" />
              <span>Recent Achievements</span>
            </h4>
            <div className="space-y-2">
              {getRecentAchievements().map((achievement, index) => (
                <motion.div
                  key={achievement.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.7 + index * 0.1 }}
                  className="flex items-center space-x-3 p-3 bg-gradient-to-r from-yellow-50 to-orange-50  rounded-lg border border-yellow-200 
                >
                  <div className="w-8 h-8 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg flex items-center justify-center">
                    <Trophy className="w-4 h-4 text-white" />
                  </div>
                  <div className="flex-1">
                    <div className="text-sm font-medium text-gray-900 
                      {achievement.title}
                    </div>
                    <div className="text-xs text-gray-600 
                      {achievement.description}
                    </div>
                  </div>
                  <div className="text-xs font-medium text-yellow-600 
                    +{achievement.points}
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}

        {/* Next Milestone */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
          className="p-4 bg-gradient-to-r from-primary-50 to-accent-50  rounded-xl border border-primary-200 
        >
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-r from-primary-500 to-accent-500 rounded-lg flex items-center justify-center">
              <Target className="w-4 h-4 text-white" />
            </div>
            <div className="flex-1">
              <div className="text-sm font-medium text-gray-900 
                Next Milestone
              </div>
              <div className="text-xs text-gray-600 
                {course.completedModules.length < COURSE_MODULES.length
                  ? `Complete Module ${course.completedModules.length + 1}`
                  : 'Course Complete! 🎉'
                }
              </div>
            </div>
            <div className="text-right">
              <div className="text-sm font-bold text-primary-600 
                {course.completedModules.length < COURSE_MODULES.length
                  ? `${COURSE_MODULES.length - course.completedModules.length} left`
                  : '100%'
                }
              </div>
            </div>
          </div>
        </motion.div>
      </CardContent>
    </Card>
  )
}
