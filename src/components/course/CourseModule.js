'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  ArrowLeft,
  Play,
  CheckCircle2,
  Clock,
  BookOpen,
  Target,
  Brain,
  Lightbulb,
  ArrowRight,
  Star,
  Award,
  Eye,
  Bookmark,
  BookmarkCheck
} from 'lucide-react'
import { useStore, useCourse } from '@/store/useStore'
import { COURSE_MODULES } from '@/data/courseData'
import Button from '@/components/ui/Button'
import Card, { CardHeader, CardTitle, CardContent } from '@/components/ui/Card'
import CourseLesson from './CourseLesson'
import toast from 'react-hot-toast'

const lessonTypeIcons = {
  theory: Brain,
  practical: Target,
  strategy: Lightbulb,
  interactive: Eye
}

const lessonTypeColors = {
  theory: 'from-blue-500 to-blue-600',
  practical: 'from-green-500 to-green-600',
  strategy: 'from-purple-500 to-purple-600',
  interactive: 'from-orange-500 to-orange-600'
}

export default function CourseModule({ moduleId, onBack }) {
  const [selectedLesson, setSelectedLesson] = useState(null)
  const [showLessonList, setShowLessonList] = useState(true)
  const course = useCourse()
  const { setCourseLesson, completeLesson, completeModule, addBookmark, removeBookmark } = useStore()

  const module = COURSE_MODULES.find(m => m.id === moduleId)
  
  if (!module) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-600 not found</p>
        <Button onClick={onBack} className="mt-4">
          Back to Course
        </Button>
      </div>
    )
  }

  const completedLessons = course.completedLessons.filter(lesson => 
    lesson.startsWith(`${moduleId}-`)
  )
  const moduleProgress = Math.round((completedLessons.length / module.lessons.length) * 100)
  const isModuleComplete = completedLessons.length === module.lessons.length

  useEffect(() => {
    if (isModuleComplete && !course.completedModules.includes(moduleId)) {
      completeModule(moduleId)
      toast.success(`🎉 Module ${moduleId} completed!`)
    }
  }, [isModuleComplete, moduleId, course.completedModules, completeModule])

  const handleLessonSelect = (lessonId) => {
    setCourseLesson(lessonId)
    setSelectedLesson(lessonId)
    setShowLessonList(false)
  }

  const handleLessonComplete = (lessonId) => {
    completeLesson(moduleId, lessonId)
    toast.success('Lesson completed! 🎯')
  }

  const isLessonCompleted = (lessonId) => {
    return course.completedLessons.includes(`${moduleId}-${lessonId}`)
  }

  const isLessonBookmarked = (lessonId) => {
    return course.bookmarks.some(b => b.moduleId === moduleId && b.lessonId === lessonId)
  }

  const toggleBookmark = (lessonId, lessonTitle) => {
    const existingBookmark = course.bookmarks.find(b => 
      b.moduleId === moduleId && b.lessonId === lessonId
    )
    
    if (existingBookmark) {
      removeBookmark(existingBookmark.id)
      toast.success('Bookmark removed')
    } else {
      addBookmark(moduleId, lessonId, lessonTitle)
      toast.success('Lesson bookmarked!')
    }
  }

  if (selectedLesson && !showLessonList) {
    const lesson = module.lessons.find(l => l.id === selectedLesson)
    return (
      <CourseLesson
        module={module}
        lesson={lesson}
        onBack={() => setShowLessonList(true)}
        onComplete={() => handleLessonComplete(selectedLesson)}
        isCompleted={isLessonCompleted(selectedLesson)}
      />
    )
  }

  return (
    <div className="space-y-6">
      {/* Module Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center space-x-4"
      >
        <Button
          variant="ghost"
          icon={ArrowLeft}
          onClick={onBack}
        >
          Back to Course
        </Button>
        <div className="flex-1">
          <div className="flex items-center space-x-3 mb-2">
            <div className={`w-10 h-10 bg-gradient-to-r ${module.color} rounded-xl flex items-center justify-center`}>
              <BookOpen className="w-5 h-5 text-white" />
            </div>
            <div>
              <h1 className="text-2xl md:text-3xl font-bold text-gray-900 
                {module.title}
              </h1>
              <p className="text-gray-600 
                Module {moduleId} • {module.estimatedTime} • {module.difficulty}
              </p>
            </div>
          </div>
          <p className="text-gray-600 max-w-3xl">
            {module.description}
          </p>
        </div>
      </motion.div>

      {/* Progress Card */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <Card variant="glass">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 
                  Module Progress
                </h3>
                <p className="text-sm text-gray-600 
                  {completedLessons.length} of {module.lessons.length} lessons completed
                </p>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-gray-900 
                  {moduleProgress}%
                </div>
                {isModuleComplete && (
                  <div className="flex items-center space-x-1 text-success-600 
                    <Award className="w-4 h-4" />
                    <span className="text-sm font-medium">Complete!</span>
                  </div>
                )}
              </div>
            </div>
            
            {/* Progress Bar */}
            <div className="w-full bg-gray-200 rounded-full h-3">
              <motion.div
                className={`h-3 rounded-full bg-gradient-to-r ${module.color}`}
                initial={{ width: 0 }}
                animate={{ width: `${moduleProgress}%` }}
                transition={{ duration: 1, delay: 0.5 }}
              />
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Lessons List */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="space-y-4"
      >
        <h2 className="text-xl font-bold text-gray-900 
          Course Lessons
        </h2>
        
        <div className="grid gap-4">
          {module.lessons.map((lesson, index) => {
            const TypeIcon = lessonTypeIcons[lesson.type] || BookOpen
            const isCompleted = isLessonCompleted(lesson.id)
            const isBookmarked = isLessonBookmarked(lesson.id)
            
            return (
              <motion.div
                key={lesson.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.1 * index }}
              >
                <Card 
                  variant={isCompleted ? "success" : "default"}
                  className="hover:shadow-lg transition-all duration-300 cursor-pointer"
                  onClick={() => handleLessonSelect(lesson.id)}
                >
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      {/* Lesson Icon */}
                      <div className={`
                        w-12 h-12 rounded-xl flex items-center justify-center flex-shrink-0
                        ${isCompleted 
                          ? 'bg-success-500' 
                          : `bg-gradient-to-r ${lessonTypeColors[lesson.type]}`
                        }
                      `}>
                        {isCompleted ? (
                          <CheckCircle2 className="w-6 h-6 text-white" />
                        ) : (
                          <TypeIcon className="w-6 h-6 text-white" />
                        )}
                      </div>

                      {/* Lesson Content */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex items-center space-x-2">
                            <h3 className="text-lg font-semibold text-gray-900 
                              {lesson.title}
                            </h3>
                            <span className="text-xs px-2 py-1 bg-gray-100 text-gray-600 rounded-full">
                              Lesson {lesson.id}
                            </span>
                          </div>
                          
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={(e) => {
                                e.stopPropagation()
                                toggleBookmark(lesson.id, lesson.title)
                              }}
                              className="p-1 hover:bg-gray-100 rounded"
                            >
                              {isBookmarked ? (
                                <BookmarkCheck className="w-4 h-4 text-yellow-500" />
                              ) : (
                                <Bookmark className="w-4 h-4 text-gray-400" />
                              )}
                            </button>
                            
                            <div className="flex items-center space-x-1 text-xs text-gray-500 
                              <Clock className="w-3 h-3" />
                              <span>{lesson.duration}</span>
                            </div>
                          </div>
                        </div>

                        <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                          {lesson.description}
                        </p>

                        {/* Key Points */}
                        <div className="space-y-2">
                          <h4 className="text-xs font-medium text-gray-700 uppercase tracking-wide">
                            Key Learning Points
                          </h4>
                          <div className="flex flex-wrap gap-2">
                            {lesson.keyPoints.slice(0, 3).map((point, pointIndex) => (
                              <span
                                key={pointIndex}
                                className="text-xs px-2 py-1 bg-gray-50 text-gray-600 rounded-md"
                              >
                                {point}
                              </span>
                            ))}
                            {lesson.keyPoints.length > 3 && (
                              <span className="text-xs px-2 py-1 bg-gray-50 text-gray-600 rounded-md">
                                +{lesson.keyPoints.length - 3} more
                              </span>
                            )}
                          </div>
                        </div>

                        {/* Action Button */}
                        <div className="mt-4 flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <span className={`
                              text-xs px-2 py-1 rounded-full font-medium
                              ${lesson.type === 'theory' ? 'bg-blue-100 text-blue-700 :
                                lesson.type === 'practical' ? 'bg-green-100 text-green-700 :
                                lesson.type === 'strategy' ? 'bg-purple-100 text-purple-700 :
                                'bg-orange-100 text-orange-700 
                              }
                            `}>
                              {lesson.type.charAt(0).toUpperCase() + lesson.type.slice(1)}
                            </span>
                          </div>
                          
                          <Button
                            variant={isCompleted ? "success" : "primary"}
                            size="sm"
                            icon={isCompleted ? CheckCircle2 : Play}
                            onClick={(e) => {
                              e.stopPropagation()
                              handleLessonSelect(lesson.id)
                            }}
                          >
                            {isCompleted ? 'Review' : 'Start Lesson'}
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )
          })}
        </div>
      </motion.div>
    </div>
  )
}
