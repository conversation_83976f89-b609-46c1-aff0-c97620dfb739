'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  BookOpen,
  Play,
  CheckCircle2,
  Clock,
  Award,
  Target,
  TrendingUp,
  BarChart3,
  Activity,
  Layers,
  Shield,
  Eye,
  Star,
  ArrowRight,
  Lock,
  Unlock
} from 'lucide-react'
import { useStore, useCourse } from '@/store/useStore'
import { COURSE_MODULES } from '@/data/courseData'
import Button from '@/components/ui/Button'
import Card, { CardHeader, CardTitle, CardContent } from '@/components/ui/Card'
import CourseModule from './CourseModule'
import CourseProgress from './CourseProgress'
import toast from 'react-hot-toast'

const iconMap = {
  Target,
  TrendingUp,
  BarChart3,
  Activity,
  Layers,
  Shield,
  Eye
}

export default function Course() {
  const [selectedModule, setSelectedModule] = useState(null)
  const [showProgress, setShowProgress] = useState(false)
  const course = useCourse()
  const { setCourseModule, completeModule, addAchievement } = useStore()

  // Check for achievements
  useEffect(() => {
    if (course.completedLessons.length === 1 && !course.achievements.find(a => a.id === 'first_lesson')) {
      addAchievement({
        id: 'first_lesson',
        title: 'Getting Started',
        description: 'Complete your first lesson',
        icon: 'Play',
        points: 10
      })
      toast.success('🎉 Achievement unlocked: Getting Started!')
    }

    if (course.completedModules.length === 1 && !course.achievements.find(a => a.id === 'first_module')) {
      addAchievement({
        id: 'first_module',
        title: 'Module Master',
        description: 'Complete your first module',
        icon: 'Award',
        points: 50
      })
      toast.success('🏆 Achievement unlocked: Module Master!')
    }
  }, [course.completedLessons, course.completedModules, course.achievements, addAchievement])

  const handleModuleSelect = (moduleId) => {
    setCourseModule(moduleId)
    setSelectedModule(moduleId)
  }

  const isModuleUnlocked = (moduleId) => {
    if (moduleId === 1) return true
    return course.completedModules.includes(moduleId - 1)
  }

  const getModuleProgress = (moduleId) => {
    const module = COURSE_MODULES.find(m => m.id === moduleId)
    if (!module) return 0
    
    const completedLessons = course.completedLessons.filter(lesson => 
      lesson.startsWith(`${moduleId}-`)
    ).length
    
    return Math.round((completedLessons / module.lessons.length) * 100)
  }

  if (selectedModule) {
    return (
      <CourseModule 
        moduleId={selectedModule} 
        onBack={() => setSelectedModule(null)}
      />
    )
  }

  return (
    <div className="space-y-8">
      {/* Course Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center"
      >
        <div className="flex items-center justify-center space-x-3 mb-4">
          <div className="w-12 h-12 bg-gradient-to-r from-primary-500 to-accent-500 rounded-xl flex items-center justify-center">
            <BookOpen className="w-6 h-6 text-white" />
          </div>
          <h1 className="text-4xl md:text-5xl font-bold gradient-text">
            Advanced Price Action Course
          </h1>
        </div>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-6">
          Master SPY/QQQ trading with liquidity sweeps, Fair Value Gaps, and confirmation stacking techniques
        </p>
        
        {/* Progress Overview */}
        <div className="flex items-center justify-center space-x-6 text-sm">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-success-500 rounded-full"></div>
            <span className="text-gray-600">
              {course.completedModules.length} of {COURSE_MODULES.length} modules completed
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <Clock className="w-4 h-4 text-gray-500" />
            <span className="text-gray-600">
              ~5 hours total content
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <Award className="w-4 h-4 text-gray-500" />
            <span className="text-gray-600">
              {course.achievements.length} achievements earned
            </span>
          </div>
        </div>
      </motion.div>

      {/* Course Progress Card */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <CourseProgress />
      </motion.div>

      {/* Course Modules Grid */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
      >
        {COURSE_MODULES.map((module, index) => {
          const Icon = iconMap[module.icon] || BookOpen
          const isUnlocked = isModuleUnlocked(module.id)
          const progress = getModuleProgress(module.id)
          const isCompleted = course.completedModules.includes(module.id)

          return (
            <motion.div
              key={module.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 * index }}
              whileHover={isUnlocked ? { y: -5 } : {}}
            >
              <Card 
                variant={isCompleted ? "success" : isUnlocked ? "default" : "outlined"}
                className={`h-full transition-all duration-300 ${
                  isUnlocked 
                    ? 'hover:shadow-xl cursor-pointer' 
                    : 'opacity-60 cursor-not-allowed'
                }`}
                onClick={() => isUnlocked && handleModuleSelect(module.id)}
              >
                <CardContent className="p-6">
                  {/* Module Header */}
                  <div className="flex items-start justify-between mb-4">
                    <div className={`
                      w-12 h-12 rounded-xl flex items-center justify-center
                      ${isUnlocked 
                        ? `bg-gradient-to-r ${module.color}` 
                        : 'bg-gray-200 bg-gray-200'
                      }
                    `}>
                      {isUnlocked ? (
                        <Icon className="w-6 h-6 text-white" />
                      ) : (
                        <Lock className="w-6 h-6 text-gray-400" />
                      )}
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      {isCompleted && (
                        <div className="w-6 h-6 bg-success-500 rounded-full flex items-center justify-center">
                          <CheckCircle2 className="w-4 h-4 text-white" />
                        </div>
                      )}
                      <span className="text-sm font-medium text-gray-500 
                        Module {module.id}
                      </span>
                    </div>
                  </div>

                  {/* Module Content */}
                  <div className="space-y-3">
                    <h3 className="text-lg font-bold text-gray-900 text-gray-900">
                      {module.title}
                    </h3>
                    <p className="text-sm text-gray-600 line-clamp-2">
                      {module.description}
                    </p>

                    {/* Module Stats */}
                    <div className="flex items-center justify-between text-xs text-gray-500 
                      <div className="flex items-center space-x-1">
                        <Clock className="w-3 h-3" />
                        <span>{module.estimatedTime}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Target className="w-3 h-3" />
                        <span>{module.difficulty}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <BookOpen className="w-3 h-3" />
                        <span>{module.lessons.length} lessons</span>
                      </div>
                    </div>

                    {/* Progress Bar */}
                    {isUnlocked && (
                      <div className="space-y-2">
                        <div className="flex items-center justify-between text-xs">
                          <span className="text-gray-600">Progress</span>
                          <span className="font-medium text-gray-900 text-gray-900">
                            {progress}%
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 bg-gray-200 rounded-full h-2">
                          <motion.div
                            className={`h-2 rounded-full bg-gradient-to-r ${module.color}`}
                            initial={{ width: 0 }}
                            animate={{ width: `${progress}%` }}
                            transition={{ duration: 1, delay: 0.5 + index * 0.1 }}
                          />
                        </div>
                      </div>
                    )}

                    {/* Action Button */}
                    <div className="pt-2">
                      {isUnlocked ? (
                        <Button
                          variant={isCompleted ? "success" : "primary"}
                          size="sm"
                          fullWidth
                          icon={isCompleted ? CheckCircle2 : Play}
                          onClick={(e) => {
                            e.stopPropagation()
                            handleModuleSelect(module.id)
                          }}
                        >
                          {isCompleted ? 'Review Module' : progress > 0 ? 'Continue' : 'Start Module'}
                        </Button>
                      ) : (
                        <Button
                          variant="ghost"
                          size="sm"
                          fullWidth
                          disabled
                          icon={Lock}
                        >
                          Complete Module {module.id - 1} to unlock
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )
        })}
      </motion.div>

      {/* Quick Stats */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="grid grid-cols-1 md:grid-cols-3 gap-6"
      >
        <Card variant="glass">
          <CardContent className="p-6 text-center">
            <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mx-auto mb-3">
              <BookOpen className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 text-gray-900 mb-1">
              {course.completedLessons.length}
            </h3>
            <p className="text-sm text-gray-600 
              Lessons Completed
            </p>
          </CardContent>
        </Card>

        <Card variant="glass">
          <CardContent className="p-6 text-center">
            <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-xl flex items-center justify-center mx-auto mb-3">
              <Award className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 text-gray-900 mb-1">
              {course.achievements.length}
            </h3>
            <p className="text-sm text-gray-600 
              Achievements Earned
            </p>
          </CardContent>
        </Card>

        <Card variant="glass">
          <CardContent className="p-6 text-center">
            <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-3">
              <Star className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 text-gray-900 mb-1">
              {course.progress}%
            </h3>
            <p className="text-sm text-gray-600 
              Overall Progress
            </p>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
