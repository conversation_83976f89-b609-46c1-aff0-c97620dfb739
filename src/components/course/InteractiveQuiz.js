'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  ArrowLeft,
  CheckCircle2,
  XCircle,
  Brain,
  Award,
  RotateCcw,
  ArrowRight,
  Star,
  Target,
  Clock
} from 'lucide-react'
import { useStore } from '@/store/useStore'
import Button from '@/components/ui/Button'
import Card, { CardHeader, CardTitle, CardContent } from '@/components/ui/Card'
import toast from 'react-hot-toast'

// Professional quiz questions based on the comprehensive course content
const generateQuizQuestions = (module, lesson) => {
  // Get quiz questions directly from the lesson data if available
  if (lesson.quiz && lesson.quiz.length > 0) {
    return lesson.quiz
  }

  // Fallback questions for lessons without specific quiz data
  const fallbackQuestions = {
    1: { // Market Structure & Zone Identification
      1: [
        {
          question: "What defines an uptrend in market structure analysis?",
          options: [
            "Price moving above a moving average",
            "Series of higher highs and higher lows",
            "Increasing volume",
            "Bullish candlestick patterns"
          ],
          correct: 1,
          explanation: "An uptrend is defined by a series of higher highs and higher lows, indicating that buyers are willing to pay progressively higher prices."
        },
        {
          question: "Why are SPY and QQQ ideal for market structure analysis?",
          options: [
            "They have low volatility",
            "They only move during market hours",
            "High liquidity and institutional participation create clean price action",
            "They always trend upward"
          ],
          correct: 2,
          explanation: "SPY and QQQ's high liquidity and heavy institutional participation result in clean, reliable price action that clearly shows market structure."
        }
      ],
      2: [
        {
          question: "What is the ideal zone thickness for SPY/QQQ trading?",
          options: [
            "Exactly 1 point",
            "0.1% to 0.3% of the instrument's price",
            "5% of the current price",
            "Whatever looks good on the chart"
          ],
          correct: 1,
          explanation: "Professional zone thickness should be 0.1-0.3% of the instrument's price to account for natural volatility while maintaining precision."
        },
        {
          question: "Which timeframe combination is considered professional standard for zone identification?",
          options: [
            "1-minute and 5-minute",
            "15-minute and 30-minute",
            "1-hour and 4-hour",
            "Daily and weekly"
          ],
          correct: 1,
          explanation: "15-30 minute timeframes provide the optimal balance between precision for entries and broader market context."
        }
      ]
    },
    2: { // Liquidity Sweeps & Stop Hunting
      1: [
        {
          question: "Where is buy-side liquidity typically located?",
          options: [
            "Below recent swing lows",
            "Above recent swing highs and resistance levels",
            "At moving averages",
            "At random price levels"
          ],
          correct: 1,
          explanation: "Buy-side liquidity is located above recent highs where retail traders place stop losses when shorting or buying breakouts."
        },
        {
          question: "Why do institutions hunt retail stop losses?",
          options: [
            "To manipulate markets illegally",
            "To create volatility",
            "To obtain liquidity for large orders at better prices",
            "To confuse retail traders"
          ],
          correct: 2,
          explanation: "Institutions hunt stops to obtain the liquidity needed to fill their large orders at more favorable prices with reduced market impact."
        }
      ],
      2: [
        {
          question: "What is the typical penetration distance for SPY liquidity sweeps?",
          options: [
            "1-2% beyond the level",
            "0.1-0.3% beyond the level",
            "5-10% beyond the level",
            "Exactly to the level"
          ],
          correct: 1,
          explanation: "SPY liquidity sweeps typically penetrate 0.1-0.3% beyond key levels - enough to trigger stops but not so much as to indicate a genuine breakout."
        },
        {
          question: "Which phase of a liquidity sweep shows the highest volume?",
          options: [
            "The approach phase",
            "The penetration phase",
            "The reversal phase",
            "The follow-through phase"
          ],
          correct: 2,
          explanation: "The reversal phase typically shows the highest volume as institutional orders enter the market after stops are triggered."
        }
      ]
    },
    3: { // Fair Value Gaps (FVGs) Mastery
      1: [
        {
          question: "How many candles are required to form a Fair Value Gap?",
          options: [
            "2 candles",
            "3 candles",
            "4 candles",
            "5 candles"
          ],
          correct: 1,
          explanation: "A Fair Value Gap requires exactly 3 consecutive candles, with the middle candle creating the imbalance and no overlap between the outer candles."
        },
        {
          question: "What percentage of Fair Value Gaps typically get filled?",
          options: [
            "30-40%",
            "50-60%",
            "70-80%",
            "90-100%"
          ],
          correct: 2,
          explanation: "Approximately 70-80% of Fair Value Gaps get filled as markets naturally seek price efficiency and institutional orders get completed."
        }
      ]
    }
  }

  return baseQuestions[module.id]?.[lesson.id] || [
    {
      question: `What is the main focus of ${lesson.title}?`,
      options: ["Technical analysis", "Price action patterns", "Risk management", "All of the above"],
      correct: 3,
      explanation: "This lesson covers multiple aspects of professional trading methodology."
    }
  ]
}

export default function InteractiveQuiz({ module, lesson, onComplete, onBack }) {
  const [currentQuestion, setCurrentQuestion] = useState(0)
  const [selectedAnswer, setSelectedAnswer] = useState(null)
  const [showResult, setShowResult] = useState(false)
  const [answers, setAnswers] = useState([])
  const [quizComplete, setQuizComplete] = useState(false)
  const [startTime, setStartTime] = useState(Date.now())
  const { saveQuizScore, addAchievement } = useStore()

  const questions = generateQuizQuestions(module, lesson)
  const totalQuestions = questions.length
  const progress = Math.round(((currentQuestion + 1) / totalQuestions) * 100)

  useEffect(() => {
    setStartTime(Date.now())
  }, [])

  const handleAnswerSelect = (answerIndex) => {
    setSelectedAnswer(answerIndex)
  }

  const handleSubmitAnswer = () => {
    const isCorrect = selectedAnswer === questions[currentQuestion].correct
    const newAnswers = [...answers, {
      questionIndex: currentQuestion,
      selectedAnswer,
      correct: isCorrect,
      question: questions[currentQuestion]
    }]
    setAnswers(newAnswers)
    setShowResult(true)

    // Auto-advance after showing result
    setTimeout(() => {
      if (currentQuestion < totalQuestions - 1) {
        setCurrentQuestion(currentQuestion + 1)
        setSelectedAnswer(null)
        setShowResult(false)
      } else {
        completeQuiz(newAnswers)
      }
    }, 2000)
  }

  const completeQuiz = (finalAnswers) => {
    const correctAnswers = finalAnswers.filter(a => a.correct).length
    const score = Math.round((correctAnswers / totalQuestions) * 100)
    const timeSpent = Math.round((Date.now() - startTime) / 1000)

    // Save quiz score
    saveQuizScore(module.id, lesson.id, {
      score,
      correctAnswers,
      totalQuestions,
      timeSpent,
      completedAt: new Date().toISOString()
    })

    // Check for achievements
    if (score >= 90) {
      addAchievement({
        id: `quiz_master_${module.id}_${lesson.id}`,
        title: 'Quiz Master',
        description: `Scored ${score}% on ${lesson.title}`,
        icon: 'Brain',
        points: 25
      })
      toast.success('🧠 Achievement: Quiz Master!')
    }

    setQuizComplete(true)
  }

  const handleRetakeQuiz = () => {
    setCurrentQuestion(0)
    setSelectedAnswer(null)
    setShowResult(false)
    setAnswers([])
    setQuizComplete(false)
    setStartTime(Date.now())
  }

  const getScoreColor = (score) => {
    if (score >= 90) return 'text-green-600 dark:text-green-400'
    if (score >= 70) return 'text-yellow-600 dark:text-yellow-400'
    return 'text-red-600 dark:text-red-400'
  }

  const getScoreMessage = (score) => {
    if (score >= 90) return 'Excellent! You have mastered this material.'
    if (score >= 70) return 'Good job! Consider reviewing the material for better understanding.'
    return 'You may want to review the lesson content and try again.'
  }

  if (quizComplete) {
    const correctAnswers = answers.filter(a => a.correct).length
    const score = Math.round((correctAnswers / totalQuestions) * 100)
    const timeSpent = Math.round((Date.now() - startTime) / 1000)

    return (
      <div className="max-w-2xl mx-auto space-y-6">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center"
        >
          <Card variant="glass">
            <CardContent className="p-8">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.2, type: "spring" }}
                className="w-20 h-20 mx-auto mb-6 bg-gradient-to-r from-primary-500 to-accent-500 rounded-full flex items-center justify-center"
              >
                <Award className="w-10 h-10 text-white" />
              </motion.div>

              <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                Quiz Complete!
              </h2>

              <div className="space-y-4 mb-6">
                <div className={`text-4xl font-bold ${getScoreColor(score)}`}>
                  {score}%
                </div>
                <p className="text-gray-600 dark:text-gray-400">
                  {correctAnswers} out of {totalQuestions} correct
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Completed in {Math.floor(timeSpent / 60)}:{(timeSpent % 60).toString().padStart(2, '0')}
                </p>
                <p className="text-gray-700 dark:text-gray-300">
                  {getScoreMessage(score)}
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Button
                  variant="primary"
                  onClick={onComplete}
                  icon={CheckCircle2}
                >
                  Complete Lesson
                </Button>
                <Button
                  variant="ghost"
                  onClick={handleRetakeQuiz}
                  icon={RotateCcw}
                >
                  Retake Quiz
                </Button>
                <Button
                  variant="ghost"
                  onClick={onBack}
                  icon={ArrowLeft}
                >
                  Back to Lesson
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Answer Review */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card variant="default">
            <CardHeader>
              <CardTitle>Answer Review</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {answers.map((answer, index) => (
                <div
                  key={index}
                  className={`p-4 rounded-lg border ${
                    answer.correct
                      ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-700'
                      : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-700'
                  }`}
                >
                  <div className="flex items-start space-x-3">
                    {answer.correct ? (
                      <CheckCircle2 className="w-5 h-5 text-green-600 dark:text-green-400 mt-0.5" />
                    ) : (
                      <XCircle className="w-5 h-5 text-red-600 dark:text-red-400 mt-0.5" />
                    )}
                    <div className="flex-1">
                      <p className="font-medium text-gray-900 dark:text-white mb-2">
                        {answer.question.question}
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                        Your answer: {answer.question.options[answer.selectedAnswer]}
                      </p>
                      {!answer.correct && (
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                          Correct answer: {answer.question.options[answer.question.correct]}
                        </p>
                      )}
                      <p className="text-sm text-gray-700 dark:text-gray-300">
                        {answer.question.explanation}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </motion.div>
      </div>
    )
  }

  const currentQ = questions[currentQuestion]

  return (
    <div className="max-w-2xl mx-auto space-y-6">
      {/* Quiz Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div className="flex items-center space-x-3">
          <Button
            variant="ghost"
            icon={ArrowLeft}
            onClick={onBack}
          >
            Back to Lesson
          </Button>
          <div>
            <h1 className="text-xl font-bold text-gray-900 dark:text-white">
              Knowledge Check
            </h1>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {lesson.title} • Question {currentQuestion + 1} of {totalQuestions}
            </p>
          </div>
        </div>
        <div className="text-right">
          <div className="text-lg font-bold text-gray-900 dark:text-white">
            {progress}%
          </div>
          <div className="text-xs text-gray-600 dark:text-gray-400">
            Progress
          </div>
        </div>
      </motion.div>

      {/* Progress Bar */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <div className="w-full bg-gray-200 dark:bg-dark-700 rounded-full h-2">
          <motion.div
            className="h-2 rounded-full bg-gradient-to-r from-primary-500 to-accent-500"
            initial={{ width: 0 }}
            animate={{ width: `${progress}%` }}
            transition={{ duration: 0.5 }}
          />
        </div>
      </motion.div>

      {/* Question Card */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Card variant="default">
          <CardContent className="p-8">
            <AnimatePresence mode="wait">
              {!showResult ? (
                <motion.div
                  key={`question-${currentQuestion}`}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  className="space-y-6"
                >
                  <div className="flex items-center space-x-3 mb-6">
                    <div className="w-8 h-8 bg-gradient-to-r from-primary-500 to-accent-500 rounded-lg flex items-center justify-center">
                      <Brain className="w-4 h-4 text-white" />
                    </div>
                    <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                      Question {currentQuestion + 1}
                    </h2>
                  </div>

                  <p className="text-lg text-gray-900 dark:text-white leading-relaxed">
                    {currentQ.question}
                  </p>

                  <div className="space-y-3">
                    {currentQ.options.map((option, index) => (
                      <motion.button
                        key={index}
                        onClick={() => handleAnswerSelect(index)}
                        className={`
                          w-full p-4 text-left rounded-lg border-2 transition-all duration-200
                          ${selectedAnswer === index
                            ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                            : 'border-gray-200 dark:border-dark-600 hover:border-gray-300 dark:hover:border-dark-500'
                          }
                        `}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <div className="flex items-center space-x-3">
                          <div className={`
                            w-6 h-6 rounded-full border-2 flex items-center justify-center
                            ${selectedAnswer === index
                              ? 'border-primary-500 bg-primary-500'
                              : 'border-gray-300 dark:border-dark-500'
                            }
                          `}>
                            {selectedAnswer === index && (
                              <div className="w-2 h-2 bg-white rounded-full" />
                            )}
                          </div>
                          <span className="text-gray-900 dark:text-white">
                            {option}
                          </span>
                        </div>
                      </motion.button>
                    ))}
                  </div>

                  <div className="pt-4">
                    <Button
                      variant="primary"
                      onClick={handleSubmitAnswer}
                      disabled={selectedAnswer === null}
                      icon={ArrowRight}
                      fullWidth
                    >
                      Submit Answer
                    </Button>
                  </div>
                </motion.div>
              ) : (
                <motion.div
                  key={`result-${currentQuestion}`}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="text-center space-y-6"
                >
                  <div className={`
                    w-16 h-16 mx-auto rounded-full flex items-center justify-center
                    ${selectedAnswer === currentQ.correct
                      ? 'bg-green-500'
                      : 'bg-red-500'
                    }
                  `}>
                    {selectedAnswer === currentQ.correct ? (
                      <CheckCircle2 className="w-8 h-8 text-white" />
                    ) : (
                      <XCircle className="w-8 h-8 text-white" />
                    )}
                  </div>

                  <div>
                    <h3 className={`
                      text-2xl font-bold mb-2
                      ${selectedAnswer === currentQ.correct
                        ? 'text-green-600 dark:text-green-400'
                        : 'text-red-600 dark:text-red-400'
                      }
                    `}>
                      {selectedAnswer === currentQ.correct ? 'Correct!' : 'Incorrect'}
                    </h3>
                    
                    {selectedAnswer !== currentQ.correct && (
                      <p className="text-gray-600 dark:text-gray-400 mb-4">
                        The correct answer is: <strong>{currentQ.options[currentQ.correct]}</strong>
                      </p>
                    )}
                    
                    <p className="text-gray-700 dark:text-gray-300">
                      {currentQ.explanation}
                    </p>
                  </div>

                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    {currentQuestion < totalQuestions - 1 
                      ? 'Moving to next question...'
                      : 'Calculating final score...'
                    }
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
