'use client'

import { motion } from 'framer-motion'
import { Users, Calendar, Star, ArrowRight } from 'lucide-react'
import GlassPanel from '@/components/ui/GlassPanel'
import GlowButton from '@/components/ui/GlowButton'

export default function MentorshipCard() {
  return (
    <section className="py-20 px-6 bg-gradient-to-b from-brand-dark-primary to-brand-dark-secondary">
      <div className="max-w-6xl mx-auto">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          whileInView={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, type: "spring", stiffness: 100 }}
          viewport={{ once: true }}
        >
          <GlassPanel variant="emerald" className="relative overflow-hidden">
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-5">
              <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-brand-emerald to-transparent" />
              <div className="absolute top-4 right-4 w-32 h-32 border border-brand-emerald/20 rounded-full" />
              <div className="absolute bottom-4 left-4 w-24 h-24 border border-brand-gold/20 rounded-full" />
            </div>

            <div className="relative z-10 grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              {/* Content */}
              <div>
                <motion.div
                  className="flex items-center space-x-3 mb-6"
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.2, duration: 0.6 }}
                  viewport={{ once: true }}
                >
                  <div className="w-12 h-12 bg-gradient-to-br from-brand-emerald to-green-600 rounded-xl flex items-center justify-center">
                    <Users className="w-6 h-6 text-white" />
                  </div>
                  <span className="text-brand-emerald font-semibold text-lg">1-on-1 Mentorship</span>
                </motion.div>

                <motion.h3
                  className="text-3xl md:text-4xl font-heading font-bold text-brand-ivory mb-4"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3, duration: 0.6 }}
                  viewport={{ once: true }}
                >
                  Learn Directly from 
                  <span className="gradient-text"> Trading Professionals</span>
                </motion.h3>

                <motion.p
                  className="text-brand-gray text-lg leading-relaxed mb-8"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4, duration: 0.6 }}
                  viewport={{ once: true }}
                >
                  Work directly with Eddyy and Adam for personalized coaching sessions. 
                  Get real-time feedback on your trades, refine your strategy, and 
                  accelerate your growth with expert guidance.
                </motion.p>

                {/* Features List */}
                <motion.div
                  className="space-y-4 mb-8"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5, duration: 0.6 }}
                  viewport={{ once: true }}
                >
                  {[
                    "Live chart analysis and trade reviews",
                    "Personalized strategy development",
                    "Real-time market guidance",
                    "Psychology and discipline coaching"
                  ].map((feature, index) => (
                    <motion.div
                      key={feature}
                      className="flex items-center space-x-3"
                      initial={{ opacity: 0, x: -10 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.6 + index * 0.1, duration: 0.4 }}
                      viewport={{ once: true }}
                    >
                      <div className="w-2 h-2 bg-brand-emerald rounded-full" />
                      <span className="text-brand-ivory">{feature}</span>
                    </motion.div>
                  ))}
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.7, duration: 0.6 }}
                  viewport={{ once: true }}
                >
                  <GlowButton variant="emerald" size="lg" className="group">
                    <Calendar className="w-5 h-5 mr-2" />
                    Book Your Session
                    <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                  </GlowButton>
                </motion.div>
              </div>

              {/* Visual Element */}
              <motion.div
                className="relative"
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.4, duration: 0.8 }}
                viewport={{ once: true }}
              >
                <div className="relative">
                  {/* Main Card */}
                  <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-md rounded-2xl p-8 border border-white/20">
                    <div className="flex items-center space-x-4 mb-6">
                      <div className="w-16 h-16 bg-gradient-to-br from-brand-gold to-yellow-600 rounded-full flex items-center justify-center">
                        <Star className="w-8 h-8 text-white" />
                      </div>
                      <div>
                        <h4 className="text-brand-ivory font-semibold text-lg">Expert Mentors</h4>
                        <p className="text-brand-gray">Eddyy & Adam</p>
                      </div>
                    </div>
                    
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-brand-gray">Success Rate</span>
                        <span className="text-brand-emerald font-semibold">94%</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-brand-gray">Students Mentored</span>
                        <span className="text-brand-gold font-semibold">500+</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-brand-gray">Years Experience</span>
                        <span className="text-brand-ivory font-semibold">15+</span>
                      </div>
                    </div>
                  </div>

                  {/* Floating Elements */}
                  <motion.div
                    className="absolute -top-4 -right-4 w-8 h-8 bg-brand-emerald rounded-full opacity-60"
                    animate={{
                      y: [0, -10, 0],
                      scale: [1, 1.1, 1]
                    }}
                    transition={{
                      duration: 3,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  />
                  <motion.div
                    className="absolute -bottom-2 -left-2 w-6 h-6 bg-brand-gold rounded-full opacity-40"
                    animate={{
                      y: [0, 8, 0],
                      scale: [1, 0.9, 1]
                    }}
                    transition={{
                      duration: 4,
                      repeat: Infinity,
                      ease: "easeInOut",
                      delay: 1
                    }}
                  />
                </div>
              </motion.div>
            </div>
          </GlassPanel>
        </motion.div>
      </div>
    </section>
  )
}
