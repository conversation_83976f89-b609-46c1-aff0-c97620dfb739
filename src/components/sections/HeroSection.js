'use client'

import { motion } from 'framer-motion'
import { TrendingUp, Zap, Target } from 'lucide-react'
import GlowButton from '@/components/ui/GlowButton'
import Button from '@/components/ui/Button'

export default function HeroSection() {
  return (
    <motion.section
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8, ease: "easeOut" }}
      className="relative min-h-screen bg-gradient-to-br from-brand-dark-primary via-brand-dark-accent to-brand-dark-secondary text-brand-ivory flex flex-col items-center justify-center px-6 overflow-hidden"
    >
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Floating candlestick patterns */}
        <motion.div
          className="absolute top-20 left-10 w-2 h-16 bg-gradient-to-t from-brand-emerald to-transparent opacity-20"
          animate={{
            y: [0, -20, 0],
            opacity: [0.2, 0.4, 0.2]
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute top-40 right-20 w-2 h-12 bg-gradient-to-t from-brand-red to-transparent opacity-20"
          animate={{
            y: [0, 15, 0],
            opacity: [0.2, 0.3, 0.2]
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 1
          }}
        />
        <motion.div
          className="absolute bottom-32 left-1/4 w-2 h-20 bg-gradient-to-t from-brand-gold to-transparent opacity-20"
          animate={{
            y: [0, -25, 0],
            opacity: [0.2, 0.5, 0.2]
          }}
          transition={{
            duration: 5,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2
          }}
        />
      </div>

      {/* Logo */}
      <motion.div
        className="mb-8"
        initial={{ scale: 0, rotate: -180 }}
        animate={{ scale: 1, rotate: 0 }}
        transition={{ 
          type: "spring", 
          stiffness: 260, 
          damping: 20,
          delay: 0.2 
        }}
      >
        <div className="w-24 h-24 bg-gradient-to-br from-brand-gold to-brand-emerald rounded-2xl flex items-center justify-center shadow-2xl shadow-brand-emerald/30">
          <TrendingUp className="w-12 h-12 text-brand-dark-primary" />
        </div>
      </motion.div>

      {/* Main Heading */}
      <motion.h1
        className="text-5xl md:text-6xl lg:text-7xl font-heading font-bold text-center mb-6 leading-tight"
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4, duration: 0.8 }}
      >
        <span className="gradient-text">Master the Markets</span>
        <br />
        <span className="text-brand-ivory">Like a Pro</span>
      </motion.h1>

      {/* Subtitle */}
      <motion.p
        className="text-xl md:text-2xl text-brand-gray max-w-3xl text-center mb-12 leading-relaxed"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6, duration: 0.8 }}
      >
        Advanced price action strategies, institutional insights, and professional trading tools 
        for the <span className="text-brand-emerald font-semibold">Limitless Options</span> community.
      </motion.p>

      {/* Feature Pills */}
      <motion.div
        className="flex flex-wrap justify-center gap-4 mb-12"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8, duration: 0.8 }}
      >
        {[
          { icon: Zap, text: "Smart Money Signals", color: "brand-gold" },
          { icon: Target, text: "Precision Entries", color: "brand-emerald" },
          { icon: TrendingUp, text: "Live Mentorship", color: "brand-red" }
        ].map((feature, index) => (
          <motion.div
            key={feature.text}
            className="flex items-center space-x-2 bg-white/10 backdrop-blur-md px-4 py-2 rounded-full border border-white/20"
            whileHover={{ scale: 1.05, backgroundColor: "rgba(255, 255, 255, 0.15)" }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            <feature.icon className={`w-4 h-4 text-${feature.color}`} />
            <span className="text-sm font-medium text-brand-ivory">{feature.text}</span>
          </motion.div>
        ))}
      </motion.div>

      {/* CTA Buttons */}
      <motion.div
        className="flex flex-col sm:flex-row gap-6"
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1.0, duration: 0.8 }}
      >
        <GlowButton variant="emerald" size="lg">
          Start Trading Course
        </GlowButton>
        
        <GlowButton variant="gold" size="lg">
          Join Discord Community
        </GlowButton>
      </motion.div>

      {/* Scroll Indicator */}
      <motion.div
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 1.5, duration: 1 }}
      >
        <motion.div
          className="w-6 h-10 border-2 border-brand-emerald rounded-full flex justify-center"
          animate={{ y: [0, 10, 0] }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          <motion.div
            className="w-1 h-3 bg-brand-emerald rounded-full mt-2"
            animate={{ opacity: [1, 0, 1] }}
            transition={{ duration: 2, repeat: Infinity }}
          />
        </motion.div>
      </motion.div>
    </motion.section>
  )
}
