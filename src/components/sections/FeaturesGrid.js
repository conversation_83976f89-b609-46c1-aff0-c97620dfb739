'use client'

import { motion } from 'framer-motion'
import { Zap, Activity, BookOpen, Target, BarChart3, Users } from 'lucide-react'
import GlassPanel from '@/components/ui/GlassPanel'

const features = [
  {
    title: "Smart Money Indicator",
    description: "Track institutional order flow and identify high-probability setups with our proprietary algorithms.",
    icon: Zap,
    color: "#1FC77D",
    gradient: "from-brand-emerald to-green-600"
  },
  {
    title: "Live Trade Alerts",
    description: "Real-time notifications for liquidity sweeps, FVG formations, and confirmation stacking opportunities.",
    icon: Activity,
    color: "#F4C46A",
    gradient: "from-brand-gold to-yellow-600"
  },
  {
    title: "Advanced Price Action Course",
    description: "Master liquidity sweeps, Fair Value Gaps, and professional confirmation stacking techniques.",
    icon: BookOpen,
    color: "#F25D5D",
    gradient: "from-brand-red to-red-600"
  },
  {
    title: "Precision Trading Tools",
    description: "Professional-grade checklist system and journal for maintaining trading discipline.",
    icon: Target,
    color: "#1FC77D",
    gradient: "from-brand-emerald to-emerald-600"
  },
  {
    title: "Performance Analytics",
    description: "Deep insights into your trading performance with advanced metrics and trend analysis.",
    icon: BarChart3,
    color: "#F4C46A",
    gradient: "from-brand-gold to-amber-600"
  },
  {
    title: "Elite Community",
    description: "Connect with serious traders, share insights, and learn from experienced professionals.",
    icon: Users,
    color: "#F25D5D",
    gradient: "from-brand-red to-pink-600"
  }
]

export default function FeaturesGrid() {
  return (
    <section className="py-20 px-6 bg-gradient-to-b from-brand-dark-secondary to-brand-dark-primary">
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl md:text-5xl font-heading font-bold text-brand-ivory mb-6">
            Everything You Need to
            <span className="gradient-text"> Trade Like a Pro</span>
          </h2>
          <p className="text-xl text-brand-gray max-w-3xl mx-auto">
            From beginner-friendly tools to advanced institutional strategies, 
            we provide the complete trading ecosystem for serious traders.
          </p>
        </motion.div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ 
                delay: index * 0.1, 
                duration: 0.6,
                ease: "easeOut"
              }}
              viewport={{ once: true }}
            >
              <GlassPanel variant="premium" className="h-full group">
                {/* Icon with animated background */}
                <div className="relative mb-6">
                  <motion.div
                    className={`w-16 h-16 bg-gradient-to-br ${feature.gradient} rounded-2xl flex items-center justify-center shadow-lg`}
                    whileHover={{ 
                      scale: 1.1,
                      rotate: 5,
                      transition: { type: "spring", stiffness: 300 }
                    }}
                  >
                    <feature.icon className="w-8 h-8 text-white" />
                  </motion.div>
                  
                  {/* Animated glow effect */}
                  <motion.div
                    className="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                    style={{
                      background: `radial-gradient(circle, ${feature.color}20 0%, transparent 70%)`
                    }}
                  />
                </div>

                {/* Content */}
                <h3 className="text-xl font-heading font-semibold text-brand-ivory mb-3 group-hover:text-brand-gold transition-colors duration-300">
                  {feature.title}
                </h3>
                
                <p className="text-brand-gray leading-relaxed group-hover:text-brand-ivory/80 transition-colors duration-300">
                  {feature.description}
                </p>

                {/* Hover indicator */}
                <motion.div
                  className="mt-4 flex items-center text-sm font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                  style={{ color: feature.color }}
                >
                  Learn more →
                </motion.div>
              </GlassPanel>
            </motion.div>
          ))}
        </div>

        {/* Bottom CTA */}
        <motion.div
          className="text-center mt-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5, duration: 0.8 }}
          viewport={{ once: true }}
        >
          <p className="text-brand-gray mb-6">
            Ready to elevate your trading game?
          </p>
          <motion.button
            className="glow-button px-8 py-4 rounded-2xl font-semibold text-lg"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            Explore All Features
          </motion.button>
        </motion.div>
      </div>
    </section>
  )
}
