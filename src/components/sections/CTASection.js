'use client'

import { motion } from 'framer-motion'
import { <PERSON>R<PERSON>, Zap, TrendingUp } from 'lucide-react'
import GlowButton from '@/components/ui/GlowButton'

export default function CTASection() {
  return (
    <section className="py-20 px-6 bg-gradient-to-t from-brand-dark-secondary via-brand-dark-accent to-brand-dark-primary relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        {/* Animated grid pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="grid grid-cols-12 gap-4 h-full">
            {Array.from({ length: 48 }).map((_, i) => (
              <motion.div
                key={i}
                className="border-l border-brand-emerald/20"
                initial={{ opacity: 0 }}
                animate={{ opacity: [0, 0.3, 0] }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  delay: i * 0.1,
                  ease: "easeInOut"
                }}
              />
            ))}
          </div>
        </div>

        {/* Floating elements */}
        <motion.div
          className="absolute top-20 left-10 w-4 h-4 bg-brand-gold rounded-full opacity-40"
          animate={{
            y: [0, -30, 0],
            x: [0, 20, 0],
            scale: [1, 1.2, 1]
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute bottom-32 right-16 w-6 h-6 bg-brand-emerald rounded-full opacity-30"
          animate={{
            y: [0, 25, 0],
            x: [0, -15, 0],
            scale: [1, 0.8, 1]
          }}
          transition={{
            duration: 5,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2
          }}
        />
      </div>

      <div className="max-w-4xl mx-auto text-center relative z-10">
        {/* Icon */}
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, scale: 0 }}
          whileInView={{ opacity: 1, scale: 1 }}
          transition={{ 
            type: "spring", 
            stiffness: 200, 
            damping: 15,
            delay: 0.2 
          }}
          viewport={{ once: true }}
        >
          <div className="w-20 h-20 bg-gradient-to-br from-brand-emerald to-green-600 rounded-2xl flex items-center justify-center mx-auto shadow-2xl shadow-brand-emerald/30">
            <Zap className="w-10 h-10 text-white" />
          </div>
        </motion.div>

        {/* Heading */}
        <motion.h2
          className="text-4xl md:text-5xl lg:text-6xl font-heading font-bold text-brand-ivory mb-6"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3, duration: 0.8 }}
          viewport={{ once: true }}
        >
          Ready to Trade Like a 
          <span className="gradient-text"> Professional?</span>
        </motion.h2>

        {/* Subtitle */}
        <motion.p
          className="text-xl md:text-2xl text-brand-gray mb-12 leading-relaxed max-w-3xl mx-auto"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4, duration: 0.8 }}
          viewport={{ once: true }}
        >
          Join the <span className="text-brand-emerald font-semibold">Limitless Options</span> community 
          and transform your trading with professional strategies, live mentorship, and cutting-edge tools.
        </motion.p>

        {/* Stats */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5, duration: 0.8 }}
          viewport={{ once: true }}
        >
          {[
            { number: "500+", label: "Active Traders", color: "brand-emerald" },
            { number: "94%", label: "Success Rate", color: "brand-gold" },
            { number: "24/7", label: "Community Support", color: "brand-red" }
          ].map((stat, index) => (
            <motion.div
              key={stat.label}
              className="text-center"
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.6 + index * 0.1, duration: 0.6 }}
              viewport={{ once: true }}
            >
              <div className={`text-3xl md:text-4xl font-bold text-${stat.color} mb-2`}>
                {stat.number}
              </div>
              <div className="text-brand-gray font-medium">{stat.label}</div>
            </motion.div>
          ))}
        </motion.div>

        {/* CTA Buttons */}
        <motion.div
          className="flex flex-col sm:flex-row gap-6 justify-center items-center"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7, duration: 0.8 }}
          viewport={{ once: true }}
        >
          <GlowButton variant="emerald" size="xl" className="group">
            <TrendingUp className="w-6 h-6 mr-3" />
            Start Your Journey
            <ArrowRight className="w-5 h-5 ml-3 group-hover:translate-x-1 transition-transform" />
          </GlowButton>

          <motion.button
            className="px-8 py-4 text-lg font-semibold text-brand-ivory border-2 border-brand-gold/50 rounded-2xl hover:border-brand-gold hover:bg-brand-gold/10 transition-all duration-300"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            View Pricing
          </motion.button>
        </motion.div>

        {/* Trust Indicators */}
        <motion.div
          className="mt-12 pt-8 border-t border-white/10"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ delay: 0.8, duration: 0.8 }}
          viewport={{ once: true }}
        >
          <p className="text-brand-gray text-sm mb-4">Trusted by professional traders worldwide</p>
          <div className="flex justify-center items-center space-x-8 opacity-60">
            {/* Trust badges or logos would go here */}
            <div className="text-brand-gray text-xs">✓ Secure Platform</div>
            <div className="text-brand-gray text-xs">✓ 24/7 Support</div>
            <div className="text-brand-gray text-xs">✓ Money-Back Guarantee</div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
