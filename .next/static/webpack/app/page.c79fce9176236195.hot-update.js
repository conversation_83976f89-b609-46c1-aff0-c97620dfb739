"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/Header.js":
/*!**********************************!*\
  !*** ./src/components/Header.js ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Header; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,BookOpen,Calendar,Menu,Settings,Target,Timer,TrendingUp,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,BookOpen,Calendar,Menu,Settings,Target,Timer,TrendingUp,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/timer.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,BookOpen,Calendar,Menu,Settings,Target,Timer,TrendingUp,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,BookOpen,Calendar,Menu,Settings,Target,Timer,TrendingUp,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,BookOpen,Calendar,Menu,Settings,Target,Timer,TrendingUp,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,BookOpen,Calendar,Menu,Settings,Target,Timer,TrendingUp,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,BookOpen,Calendar,Menu,Settings,Target,Timer,TrendingUp,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _utils_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/storage */ \"(app-pages-browser)/./src/utils/storage.js\");\n/* harmony import */ var _store_useStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/useStore */ \"(app-pages-browser)/./src/store/useStore.js\");\n/* harmony import */ var _ui_Button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.js\");\n/* harmony import */ var _features_NotificationCenter__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./features/NotificationCenter */ \"(app-pages-browser)/./src/components/features/NotificationCenter.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction Header() {\n    _s();\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [showNotifications, setShowNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const today = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.getTodayKey)();\n    const formattedDate = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.formatDate)(today);\n    const checklist = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_4__.useChecklist)();\n    const notifications = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_4__.useNotifications)();\n    const { showMobileMenu, toggleMobileMenu, toggleModal } = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_4__.useStore)();\n    const unreadNotifications = notifications.filter((n)=>!n.read).length;\n    const checkedCount = Object.values(checklist.items).filter(Boolean).length;\n    const isReadyToTrade = checkedCount >= 3;\n    const isSessionActive = checklist.isSessionActive;\n    // Update time every minute\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setInterval(()=>{\n            setCurrentTime(new Date());\n        }, 60000);\n        return ()=>clearInterval(timer);\n    }, []);\n    // Calculate session duration\n    const getSessionDuration = ()=>{\n        if (!checklist.sessionStartTime) return null;\n        const start = new Date(checklist.sessionStartTime);\n        const now = new Date();\n        const diff = Math.floor((now - start) / 1000 / 60) // minutes\n        ;\n        const hours = Math.floor(diff / 60);\n        const minutes = diff % 60;\n        return hours > 0 ? \"\".concat(hours, \"h \").concat(minutes, \"m\") : \"\".concat(minutes, \"m\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.header, {\n                className: \"bg-[#0A0F0F]/95 backdrop-blur-xl border-b border-[#F4C46A]/20 sticky top-0 z-50 shadow-lg shadow-black/30\",\n                initial: {\n                    y: -100\n                },\n                animate: {\n                    y: 0\n                },\n                transition: {\n                    duration: 0.3\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16 lg:h-20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                className: \"flex items-center space-x-3\",\n                                whileHover: {\n                                    scale: 1.02\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative w-10 h-10 lg:w-12 lg:h-12 rounded-xl overflow-hidden shadow-lg ring-2 ring-brand-emerald/30\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            src: \"/LimitlessLogo.jpg\",\n                                            alt: \"Limitless Options Logo\",\n                                            fill: true,\n                                            className: \"object-cover\",\n                                            priority: true\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                            lineNumber: 76,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                        lineNumber: 75,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl lg:text-2xl font-heading font-bold gradient-text\",\n                                                children: \"Limitless Options\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                lineNumber: 85,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs lg:text-sm text-brand-gray font-medium\",\n                                                children: \"Trading Hub\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                lineNumber: 88,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                        lineNumber: 84,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                lineNumber: 71,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden lg:flex items-center space-x-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-semibold text-brand-ivory\",\n                                                    children: currentTime.toLocaleTimeString(\"en-US\", {\n                                                        hour: \"2-digit\",\n                                                        minute: \"2-digit\",\n                                                        hour12: true\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-brand-gray\",\n                                                    children: formattedDate.split(\",\")[0]\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                            lineNumber: 98,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                                className: \"\\n                    flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium\\n                    \".concat(isReadyToTrade ? \"bg-success-50 dark:bg-success-900/20 text-success-700 dark:text-success-400\" : \"bg-warning-50 dark:bg-warning-900/20 text-warning-700 dark:text-warning-400\", \"\\n                  \"),\n                                                animate: isReadyToTrade ? {\n                                                    scale: [\n                                                        1,\n                                                        1.05,\n                                                        1\n                                                    ]\n                                                } : {},\n                                                transition: {\n                                                    duration: 0.5,\n                                                    repeat: isReadyToTrade ? Infinity : 0,\n                                                    repeatDelay: 3\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                        lineNumber: 125,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: isReadyToTrade ? \"Ready to Trade\" : \"\".concat(3 - checkedCount, \" more needed\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                        lineNumber: 126,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                lineNumber: 114,\n                                                columnNumber: 17\n                                            }, this),\n                                            isSessionActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                                className: \"flex items-center space-x-2 px-3 py-2 bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-400 rounded-lg text-sm font-medium\",\n                                                initial: {\n                                                    opacity: 0,\n                                                    scale: 0.8\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    scale: 1\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: getSessionDuration()\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                lineNumber: 131,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                        lineNumber: 113,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_5__.IconButton, {\n                                                            icon: _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                                                            variant: \"ghost\",\n                                                            onClick: ()=>setShowNotifications(!showNotifications),\n                                                            className: \"relative\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                            lineNumber: 147,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        unreadNotifications > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                                            className: \"absolute -top-1 -right-1 w-5 h-5 bg-danger-500 text-white text-xs rounded-full flex items-center justify-center\",\n                                                            initial: {\n                                                                scale: 0\n                                                            },\n                                                            animate: {\n                                                                scale: 1\n                                                            },\n                                                            transition: {\n                                                                type: \"spring\",\n                                                                stiffness: 500,\n                                                                damping: 30\n                                                            },\n                                                            children: unreadNotifications\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                            lineNumber: 154,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                lineNumber: 145,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_5__.IconButton, {\n                                                icon: _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                                                variant: \"ghost\",\n                                                onClick: ()=>toggleModal(\"settings\")\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                lineNumber: 167,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                        lineNumber: 143,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:hidden flex items-center space-x-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_5__.IconButton, {\n                                    icon: showMobileMenu ? _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"] : _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                                    variant: \"ghost\",\n                                    onClick: toggleMobileMenu\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                    lineNumber: 177,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                lineNumber: 176,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                        lineNumber: 69,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                children: showMobileMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    className: \"lg:hidden fixed inset-0 z-40 bg-black/50 backdrop-blur-sm\",\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    onClick: toggleMobileMenu,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        className: \"absolute top-16 right-4 left-4 bg-white dark:bg-dark-800 rounded-2xl shadow-xl border border-gray-200 dark:border-dark-700 p-6\",\n                        initial: {\n                            opacity: 0,\n                            scale: 0.95,\n                            y: -20\n                        },\n                        animate: {\n                            opacity: 1,\n                            scale: 1,\n                            y: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            scale: 0.95,\n                            y: -20\n                        },\n                        onClick: (e)=>e.stopPropagation(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                children: currentTime.toLocaleTimeString(\"en-US\", {\n                                                    hour: \"2-digit\",\n                                                    minute: \"2-digit\",\n                                                    hour12: true\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                lineNumber: 207,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                children: formattedDate\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                lineNumber: 214,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                        lineNumber: 206,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"\\n                    flex items-center justify-center space-x-2 px-4 py-3 rounded-lg text-sm font-medium\\n                    \".concat(isReadyToTrade ? \"bg-success-50 dark:bg-success-900/20 text-success-700 dark:text-success-400\" : \"bg-warning-50 dark:bg-warning-900/20 text-warning-700 dark:text-warning-400\", \"\\n                  \"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: isReadyToTrade ? \"Ready to Trade\" : \"\".concat(3 - checkedCount, \" more needed\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                lineNumber: 220,\n                                                columnNumber: 19\n                                            }, this),\n                                            isSessionActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-2 px-4 py-3 bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-400 rounded-lg text-sm font-medium\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Session: \",\n                                                            getSessionDuration()\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                lineNumber: 232,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                        lineNumber: 219,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                lineNumber: 205,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        variant: \"ghost\",\n                                        fullWidth: true,\n                                        icon: _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                                        onClick: ()=>{\n                                            toggleModal(\"notifications\");\n                                            toggleMobileMenu();\n                                        },\n                                        className: \"justify-start\",\n                                        children: [\n                                            \"Notifications\",\n                                            unreadNotifications > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-auto bg-danger-500 text-white text-xs px-2 py-1 rounded-full\",\n                                                children: unreadNotifications\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                lineNumber: 254,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                        lineNumber: 242,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        variant: \"ghost\",\n                                        fullWidth: true,\n                                        icon: _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                                        onClick: ()=>{\n                                            toggleModal(\"stats\");\n                                            toggleMobileMenu();\n                                        },\n                                        className: \"justify-start\",\n                                        children: \"Statistics\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                        lineNumber: 260,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        variant: \"ghost\",\n                                        fullWidth: true,\n                                        icon: _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                                        onClick: ()=>{\n                                            toggleModal(\"settings\");\n                                            toggleMobileMenu();\n                                        },\n                                        className: \"justify-start\",\n                                        children: \"Settings\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                        lineNumber: 273,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                lineNumber: 241,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                        lineNumber: 197,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                    lineNumber: 190,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                lineNumber: 188,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_NotificationCenter__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: showNotifications,\n                onClose: ()=>setShowNotifications(false)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                lineNumber: 292,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Header, \"zNkunBbG0HAPpbu29KHOPKOKPz4=\", false, function() {\n    return [\n        _store_useStore__WEBPACK_IMPORTED_MODULE_4__.useChecklist,\n        _store_useStore__WEBPACK_IMPORTED_MODULE_4__.useNotifications,\n        _store_useStore__WEBPACK_IMPORTED_MODULE_4__.useStore\n    ];\n});\n_c = Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Header.js\n"));

/***/ })

});