"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/data/courseData.js":
/*!********************************!*\
  !*** ./src/data/courseData.js ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   COURSE_ACHIEVEMENTS: function() { return /* binding */ COURSE_ACHIEVEMENTS; },\n/* harmony export */   COURSE_MODULES: function() { return /* binding */ COURSE_MODULES; }\n/* harmony export */ });\n/**\n * Advanced Price Action Course Data\n * Professional SPY/QQQ Trading Course - Liquidity Sweeps, FVGs, and Confirmation Stacking\n * Complete educational content with real trading strategies\n */ const COURSE_MODULES = [\n    {\n        id: 1,\n        title: \"Market Structure & Liquidity Fundamentals\",\n        description: \"Master the institutional approach to reading market structure. Learn how professional traders identify liquidity zones and predict price movement on SPY/QQQ.\",\n        icon: \"Target\",\n        color: \"from-blue-500 to-blue-600\",\n        estimatedTime: \"90 minutes\",\n        difficulty: \"Beginner\",\n        prerequisites: \"Basic understanding of candlestick charts and support/resistance levels\",\n        learningObjectives: [\n            \"Decode institutional market structure patterns with professional precision\",\n            \"Identify high-probability liquidity zones where smart money operates\",\n            \"Distinguish between genuine breakouts and institutional stop hunts\",\n            \"Apply market structure analysis to SPY/QQQ for consistent profits\",\n            \"Master the professional trader's mindset: 'liquidity drives direction'\"\n        ],\n        lessons: [\n            {\n                id: 1,\n                title: \"Institutional Market Structure: Reading the Smart Money Footprints\",\n                description: \"Discover how professional traders analyze market structure to predict institutional moves and identify high-probability trading opportunities\",\n                imageUrl: \"https://images.unsplash.com/photo-1611974789855-9c2a0a7236a3?w=800&h=400&fit=crop\",\n                content: \"\\n# Chapter 1: Institutional Market Structure Analysis\\n\\n*\\\"The market is a voting machine in the short run, but a weighing machine in the long run. Understanding who's voting and why they're weighing gives you the ultimate edge.\\\"* - Professional Trading Axiom\\n\\n---\\n\\n## Section 1.1: The Professional Trader's Perspective\\n\\n### What Separates Institutional Traders from Retail?\\n\\nProfessional institutional traders operate with a fundamentally different approach than retail traders. While retail traders often focus on indicators and patterns, institutions analyze **market structure** - the underlying framework that reveals where smart money is positioned and where they're likely to move next.\\n\\n**Key Institutional Advantages:**\\n- Access to order flow data showing real-time buying/selling pressure\\n- Understanding of where retail stops cluster (their liquidity targets)\\n- Ability to move markets through large position sizes\\n- Advanced risk management systems that retail traders lack\\n\\n### The Market Structure Hierarchy\\n\\nMarket structure operates on multiple levels, each providing different insights:\\n\\n**1. Primary Structure (Daily/Weekly)**\\n- Major swing highs and lows that define long-term trends\\n- Key institutional accumulation and distribution zones\\n- Primary support and resistance levels that matter to big money\\n\\n**2. Secondary Structure (4H/1H)**\\n- Intermediate swings that show institutional positioning\\n- Areas where institutions build or reduce positions\\n- Critical levels for swing trading opportunities\\n\\n**3. Tertiary Structure (15M/5M)**\\n- Short-term swings for precise entry and exit timing\\n- Scalping opportunities within larger institutional moves\\n- Fine-tuning entries for optimal risk/reward ratios\\n\\n---\\n\\n## Section 1.2: Market Structure Components\\n\\n### Higher Highs and Higher Lows (Uptrend Structure)\\n\\nIn a healthy uptrend, price creates a series of higher highs (HH) and higher lows (HL). This pattern indicates:\\n\\n- **Institutional Accumulation**: Smart money is building long positions\\n- **Retail FOMO**: Fear of missing out drives retail buying at higher prices\\n- **Momentum Continuation**: Each pullback finds buyers at higher levels\\n\\n**Professional Trading Insight**: The quality of higher lows is more important than higher highs. Strong higher lows with volume support indicate institutional backing.\\n\\n### Lower Highs and Lower Lows (Downtrend Structure)\\n\\nIn a bearish market structure, price creates lower highs (LH) and lower lows (LL):\\n\\n- **Institutional Distribution**: Smart money is reducing long positions or building shorts\\n- **Retail Denial**: Retail traders often buy \\\"dips\\\" that continue falling\\n- **Momentum Breakdown**: Each rally fails at lower levels\\n\\n### Sideways Structure (Accumulation/Distribution)\\n\\nWhen price moves sideways between defined levels:\\n\\n- **Accumulation Phase**: Institutions quietly build positions before markup\\n- **Distribution Phase**: Institutions exit positions before markdown\\n- **Retail Confusion**: Sideways action frustrates retail traders into poor decisions\\n\\n---\\n\\n## Section 1.3: SPY/QQQ Specific Characteristics\\n\\n### SPY (S&P 500 ETF) Structure Patterns\\n\\n**Market Hours Behavior:**\\n- **9:30-10:30 AM**: Initial balance formation, often with false breakouts\\n- **10:30 AM-3:00 PM**: Trending moves or range-bound consolidation\\n- **3:00-4:00 PM**: Institutional positioning for overnight holds\\n\\n**Key SPY Levels:**\\n- **Psychological Levels**: Round numbers (400, 450, 500) act as magnets\\n- **Previous Day Extremes**: High/low from prior session are critical\\n- **Weekly/Monthly Levels**: Major institutional reference points\\n\\n### QQQ (Nasdaq 100 ETF) Structure Patterns\\n\\n**Technology Sector Sensitivity:**\\n- More volatile than SPY due to growth stock concentration\\n- Reacts strongly to tech earnings and guidance changes\\n- Higher beta creates larger structure swings\\n\\n**QQQ-Specific Considerations:**\\n- **After-hours Impact**: Tech stocks trade actively after market close\\n- **Correlation Breaks**: Sometimes diverges from SPY during sector rotation\\n- **Momentum Extremes**: Can extend further than SPY in both directions\\n\\n---\\n\\n## Section 1.4: Professional Structure Analysis Techniques\\n\\n### The Three-Timeframe Approach\\n\\n**1. Higher Timeframe Context (Daily/4H)**\\n- Identifies the primary trend direction\\n- Shows major institutional positioning\\n- Provides overall market bias for trading decisions\\n\\n**2. Entry Timeframe Analysis (1H/30M)**\\n- Pinpoints specific entry and exit opportunities\\n- Shows intermediate structure breaks and confirmations\\n- Balances precision with broader market context\\n\\n**3. Execution Timeframe Precision (15M/5M)**\\n- Fine-tunes exact entry and exit points\\n- Manages risk with tight stop-loss placement\\n- Maximizes risk/reward ratios through precise timing\\n\\n### Structure Quality Assessment\\n\\n**Strong Structure Characteristics:**\\n- Clear, well-defined swing points with significant price separation\\n- Volume confirmation at key structural levels\\n- Multiple timeframe alignment showing consistent patterns\\n- Clean breaks with follow-through momentum\\n\\n**Weak Structure Warning Signs:**\\n- Overlapping swing points creating confusion\\n- Low volume at critical structural levels\\n- Conflicting signals across different timeframes\\n- Failed breaks with immediate reversals\\n\\n---\\n\\n## Section 1.5: Practical Application Framework\\n\\n### Daily Structure Analysis Routine\\n\\n**Morning Preparation (Pre-Market):**\\n1. Identify overnight structure changes in SPY/QQQ\\n2. Mark key levels from previous session's structure\\n3. Note any gaps or significant news that might affect structure\\n4. Plan potential scenarios based on structure analysis\\n\\n**Intraday Monitoring:**\\n1. Track real-time structure development\\n2. Identify structure breaks as they occur\\n3. Assess the quality and follow-through of breaks\\n4. Adjust trading bias based on evolving structure\\n\\n**End-of-Day Review:**\\n1. Analyze how structure played out during the session\\n2. Identify successful and failed structure predictions\\n3. Update key levels for next trading session\\n4. Document lessons learned for continuous improvement\\n\\n### Risk Management Through Structure\\n\\n**Structure-Based Stop Placement:**\\n- Place stops beyond significant structure levels\\n- Use structure to determine position sizing\\n- Adjust stops as structure evolves intraday\\n\\n**Profit Target Selection:**\\n- Target next significant structure level\\n- Use structure to determine risk/reward ratios\\n- Scale out at multiple structure-based targets\\n        \",\n                type: \"foundational\",\n                duration: \"35 min\",\n                sections: [\n                    {\n                        title: \"The Professional Trader's Perspective\",\n                        duration: \"8 min\",\n                        keyPoints: [\n                            \"Institutional vs retail trading approaches\",\n                            \"Market structure hierarchy and timeframe analysis\",\n                            \"Professional advantages in market analysis\"\n                        ]\n                    },\n                    {\n                        title: \"Market Structure Components\",\n                        duration: \"12 min\",\n                        keyPoints: [\n                            \"Higher highs/higher lows in uptrend analysis\",\n                            \"Lower highs/lower lows in downtrend identification\",\n                            \"Sideways structure and accumulation/distribution phases\"\n                        ]\n                    },\n                    {\n                        title: \"SPY/QQQ Specific Analysis\",\n                        duration: \"10 min\",\n                        keyPoints: [\n                            \"SPY market hours behavior and key levels\",\n                            \"QQQ technology sector sensitivity patterns\",\n                            \"ETF-specific structure characteristics\"\n                        ]\n                    },\n                    {\n                        title: \"Professional Application Framework\",\n                        duration: \"5 min\",\n                        keyPoints: [\n                            \"Three-timeframe analysis approach\",\n                            \"Daily structure analysis routine\",\n                            \"Risk management through structure\"\n                        ]\n                    }\n                ],\n                keyPoints: [\n                    \"Market structure reveals institutional positioning and smart money flow\",\n                    \"Three-timeframe analysis provides context, entry signals, and execution precision\",\n                    \"SPY/QQQ structure patterns differ due to sector composition and volatility\",\n                    \"Professional traders use structure for risk management and profit targeting\",\n                    \"Quality structure assessment separates high-probability from low-probability setups\"\n                ],\n                practicalExercises: [\n                    \"Analyze current SPY daily chart and identify primary, secondary, and tertiary structure levels\",\n                    \"Compare SPY vs QQQ structure patterns over the past week and note differences\",\n                    \"Practice the three-timeframe approach: Daily context → 1H entries → 15M execution\",\n                    \"Create a daily structure analysis routine and apply it for one full trading week\"\n                ],\n                quiz: [\n                    {\n                        question: \"What is the most important difference between institutional and retail trading approaches?\",\n                        options: [\n                            \"Institutions use more indicators and technical analysis tools\",\n                            \"Institutions focus on market structure while retail focuses on indicators\",\n                            \"Institutions trade larger position sizes with more capital\",\n                            \"Institutions have access to better charting software\"\n                        ],\n                        correct: 1,\n                        explanation: \"The fundamental difference is analytical approach: institutions analyze market structure to understand where smart money is positioned, while retail traders typically rely on lagging indicators and patterns.\"\n                    },\n                    {\n                        question: \"In the three-timeframe approach, what is the primary purpose of the higher timeframe analysis?\",\n                        options: [\n                            \"To find exact entry and exit points\",\n                            \"To identify the primary trend direction and institutional positioning\",\n                            \"To determine precise stop-loss placement\",\n                            \"To calculate position sizing for trades\"\n                        ],\n                        correct: 1,\n                        explanation: \"Higher timeframe analysis (Daily/4H) provides the overall market context, showing the primary trend direction and major institutional positioning that guides all trading decisions.\"\n                    },\n                    {\n                        question: \"What makes QQQ structure analysis different from SPY?\",\n                        options: [\n                            \"QQQ has lower volatility and smaller price movements\",\n                            \"QQQ only trades during regular market hours\",\n                            \"QQQ is more sensitive to technology sector news and has higher volatility\",\n                            \"QQQ structure patterns are identical to SPY patterns\"\n                        ],\n                        correct: 2,\n                        explanation: \"QQQ's concentration in technology stocks makes it more volatile than SPY and highly sensitive to tech sector news, earnings, and guidance changes, creating different structure patterns.\"\n                    },\n                    {\n                        question: \"What characterizes 'strong structure' in professional analysis?\",\n                        options: [\n                            \"Many overlapping swing points with frequent reversals\",\n                            \"Clear, well-defined swing points with volume confirmation and timeframe alignment\",\n                            \"Low volume at structural levels with minimal price separation\",\n                            \"Conflicting signals across different timeframes\"\n                        ],\n                        correct: 1,\n                        explanation: \"Strong structure features clear swing points with significant price separation, volume confirmation at key levels, multiple timeframe alignment, and clean breaks with follow-through.\"\n                    },\n                    {\n                        question: \"During SPY's typical trading day, when do the most significant institutional positioning moves occur?\",\n                        options: [\n                            \"During the first 30 minutes after market open\",\n                            \"During the lunch hour (12:00-1:00 PM)\",\n                            \"During the final hour (3:00-4:00 PM) for overnight positioning\",\n                            \"Institutional moves are evenly distributed throughout the day\"\n                        ],\n                        correct: 2,\n                        explanation: \"The final trading hour (3:00-4:00 PM) is when institutions make their most significant positioning moves, preparing for overnight holds and next-day strategies.\"\n                    }\n                ]\n            },\n            {\n                id: 2,\n                title: \"Liquidity Sweeps: The Professional's Guide to Reading Institutional Moves\",\n                description: \"Master the art of identifying and trading liquidity sweeps - the institutional strategy that moves markets and creates the highest probability trading opportunities\",\n                imageUrl: \"https://images.unsplash.com/photo-1590283603385-17ffb3a7f29f?w=800&h=400&fit=crop\",\n                content: \"\\n# Trading After Liquidity Sweeps: Reversal vs Continuation\\n\\nOnce a liquidity sweep has occurred, two scenarios are in play – a reversal (mean-reversion back into the range) or a successful breakout (continuation of the trend). Understanding which scenario is unfolding is crucial for profitable trading.\\n\\n## Reversal Setup: Stop Hunt and Rejection\\n\\nThis is the classic outcome of a liquidity sweep. After price wicks beyond the prior high/low and falls back, your bias flips in the opposite direction of the wick.\\n\\n### Identifying a Reversal Setup\\n\\n**1. The Wick Formation**\\n- Price briefly breaks a key level (previous high/low)\\n- Immediately reverses back within the prior range\\n- Creates a long wick or spike on the chart\\n- Often accompanied by high volume\\n\\n**2. Confirmation Signals**\\n- **Market Structure Break**: Look for a break of a short-term swing low (if a high was swept) or swing high (if a low was swept)\\n- **Candlestick Patterns**: Engulfing candles, pin bars, or doji formations at the sweep level\\n- **Volume Analysis**: Volume spike on the sweep followed by sustained volume on the reversal\\n\\n### Trading the Reversal\\n\\n**Entry Strategy:**\\n- Wait for confirmation before entering\\n- Don't trade the exact top/bottom of the sweep\\n- Enter on the structure break in the reversal direction\\n\\n**Example: SPY High Sweep Reversal**\\n1. SPY rallies and takes out yesterday's high by $0.50\\n2. Price immediately reverses with a long upper wick\\n3. Wait for SPY to break below a recent swing low (confirmation)\\n4. Enter short position (put options) with stop above the sweep high\\n5. Target the opposite side of the range or previous support\\n\\n## Continuation Setup: Breakout with Follow-Through\\n\\nNot every push through a previous high/low is a fake-out. Sometimes the market intends to run further, and the liquidity sweep is the ignition of a larger move.\\n\\n### Identifying a Continuation Setup\\n\\n**1. Strong Follow-Through**\\n- Price holds above the broken level (for upward breakouts)\\n- Candles close firmly in the breakout direction\\n- Only shallow pullbacks that stay above the old high (now support)\\n\\n**2. Volume Confirmation**\\n- Sustained volume on subsequent bars\\n- Not just a one-off stop trigger\\n- Continued participation showing genuine interest\\n\\n### Trading the Continuation\\n\\n**Entry Strategy:**\\n- Wait for a retest of the broken level\\n- Look for the old resistance to act as new support\\n- Enter on the bounce with stops below the retest low\\n\\n**Example: QQQ Low Sweep Continuation**\\n1. QQQ drops and sweeps the previous day's low\\n2. Price quickly recovers and closes above the old low\\n3. Wait for a pullback to retest the broken low as support\\n4. Enter long position (call options) on the bounce\\n5. Stop below the retest low, target higher resistance\\n\\n## The Professional Approach: Confirmation Stacking\\n\\nNever trade a liquidity sweep in isolation. Stack multiple confirmations:\\n\\n### Primary Confirmations\\n- **Price Action**: Structure breaks, candlestick patterns\\n- **Volume**: Spikes on sweeps, sustained volume on follow-through\\n- **Time**: How quickly the reversal/continuation occurs\\n\\n### Secondary Confirmations\\n- **Higher Timeframe Context**: Is this aligned with the bigger picture?\\n- **Market Sentiment**: Risk-on vs risk-off environment\\n- **Options Flow**: Unusual activity in puts/calls\\n\\n## Real-World Examples\\n\\n### SPY Reversal Example (Bearish)\\n- **Setup**: SPY approaches yesterday's high at $450\\n- **Sweep**: Price hits $450.50, creating a wick\\n- **Confirmation**: 5-minute bearish engulfing + break of swing low\\n- **Entry**: Short at $449.50 after confirmation\\n- **Result**: Drop to $445 for 1% profit\\n\\n### QQQ Continuation Example (Bullish)\\n- **Setup**: QQQ tests overnight low at $350\\n- **Sweep**: Brief drop to $349.80, then recovery\\n- **Confirmation**: Strong close above $350 + volume increase\\n- **Entry**: Long on retest of $350 support\\n- **Result**: Rally to $355 for 1.4% profit\\n\\n## Key Takeaways\\n\\n- Liquidity sweeps provide directional bias, not immediate entries\\n- Always wait for confirmation before trading\\n- Reversals are more common than continuations\\n- Stack multiple confirmations for higher probability setups\\n- Use proper risk management with stops beyond the sweep levels\\n        \",\n                type: \"strategy\",\n                duration: \"22 min\",\n                keyPoints: [\n                    \"Liquidity sweeps can lead to either reversals or continuations\",\n                    \"Confirmation is essential - never trade the sweep itself\",\n                    \"Reversal setups are more common than continuation setups\",\n                    \"Volume and price action provide the best confirmation signals\",\n                    \"Stack multiple confirmations for higher probability trades\"\n                ],\n                practicalExercises: [\n                    \"Identify 3 liquidity sweeps on SPY/QQQ charts and classify as reversal or continuation\",\n                    \"Practice waiting for confirmation signals before entering trades\",\n                    \"Analyze volume patterns during sweep reversals vs continuations\",\n                    \"Create a checklist of confirmation signals for your trading plan\"\n                ],\n                quiz: [\n                    {\n                        question: \"What should you do immediately after identifying a liquidity sweep?\",\n                        options: [\n                            \"Enter a trade in the opposite direction\",\n                            \"Enter a trade in the same direction\",\n                            \"Wait for confirmation signals\",\n                            \"Close all existing positions\"\n                        ],\n                        correct: 2,\n                        explanation: \"Never trade immediately after a liquidity sweep. Always wait for confirmation signals like structure breaks, volume confirmation, or candlestick patterns.\"\n                    },\n                    {\n                        question: \"Which scenario is more common after a liquidity sweep?\",\n                        options: [\n                            \"Continuation breakouts\",\n                            \"Reversal setups\",\n                            \"Sideways consolidation\",\n                            \"Gap formations\"\n                        ],\n                        correct: 1,\n                        explanation: \"Reversal setups are more common after liquidity sweeps because most sweeps are designed to grab stops and then reverse, not to signal genuine breakouts.\"\n                    },\n                    {\n                        question: \"What is the best confirmation for a liquidity sweep reversal?\",\n                        options: [\n                            \"A single large volume bar\",\n                            \"Price returning to the sweep level\",\n                            \"Market structure break in the opposite direction\",\n                            \"A gap in the opposite direction\"\n                        ],\n                        correct: 2,\n                        explanation: \"A market structure break (like breaking a swing low after a high sweep) provides the strongest confirmation that the reversal is genuine and not just a temporary pullback.\"\n                    }\n                ]\n            },\n            {\n                id: 3,\n                title: \"Zone Validation Techniques\",\n                description: \"How to validate the strength and reliability of your zones\",\n                content: \"Not all zones are created equal. Learn to identify the strongest...\",\n                type: \"practical\",\n                duration: \"12 min\",\n                keyPoints: [\n                    \"Volume confirmation at zones\",\n                    \"Multiple timeframe validation\",\n                    \"Age and frequency of tests\"\n                ]\n            },\n            {\n                id: 4,\n                title: \"Interactive Zone Drawing Exercise\",\n                description: \"Practice identifying and drawing zones on real SPY/QQQ charts\",\n                content: \"Apply your knowledge with guided practice on live market examples\",\n                type: \"interactive\",\n                duration: \"8 min\",\n                keyPoints: [\n                    \"Real-time chart analysis\",\n                    \"Immediate feedback on zone placement\",\n                    \"Common mistakes to avoid\"\n                ]\n            }\n        ]\n    },\n    {\n        id: 2,\n        title: \"Fair Value Gaps (FVGs) Mastery\",\n        description: \"Master Fair Value Gaps - the institutional footprints left by rapid price movements and how to trade them for consistent profits\",\n        icon: \"BarChart3\",\n        color: \"from-green-500 to-green-600\",\n        estimatedTime: \"80 minutes\",\n        difficulty: \"Intermediate\",\n        prerequisites: \"Understanding of liquidity sweeps and market structure\",\n        learningObjectives: [\n            \"Identify and mark Fair Value Gaps with 95% accuracy\",\n            \"Understand the difference between HTF and LTF FVGs\",\n            \"Trade FVG fills and rejections for high-probability setups\",\n            \"Master Inversion Fair Value Gaps (IFVGs) for advanced entries\",\n            \"Combine FVGs with liquidity sweeps for confluence trading\"\n        ],\n        lessons: [\n            {\n                id: 1,\n                title: \"Fair Value Gap Theory & Formation\",\n                description: \"Deep dive into FVG formation, institutional causes, and market inefficiency concepts\",\n                content: \"\\n# Fair Value Gaps: Institutional Footprints in Price Action\\n\\nA Fair Value Gap (FVG) is a price range on a chart where an inefficient move occurred – essentially, a section where little or no trading took place. These gaps represent areas where fair value may have temporarily changed, and markets tend to revert to fill these inefficiencies over time.\\n\\n## What is a Fair Value Gap?\\n\\n**Definition:**\\nA Fair Value Gap appears within a three-candle sequence where one large momentum candle creates a \\\"void\\\" between the wick of the first candle and the wick of the third candle. The second candle (the big move) is so large that the third candle's low (in an up move) is still above the first candle's high, leaving a gap in between.\\n\\n**Key Concept:**\\nThis gap represents a price area where fair value may have temporarily changed – price zoomed in one direction without adequate two-way trading at those levels.\\n\\n## The Three-Candle Rule\\n\\n### Bullish FVG Formation:\\n1. **Candle 1**: Creates a high at a certain level\\n2. **Candle 2**: Large bullish candle that gaps up significantly\\n3. **Candle 3**: Low is still above Candle 1's high\\n4. **Result**: Gap between Candle 1 high and Candle 3 low = Bullish FVG\\n\\n### Bearish FVG Formation:\\n1. **Candle 1**: Creates a low at a certain level\\n2. **Candle 2**: Large bearish candle that gaps down significantly\\n3. **Candle 3**: High is still below Candle 1's low\\n4. **Result**: Gap between Candle 1 low and Candle 3 high = Bearish FVG\\n\\n## Why Do FVGs Matter?\\n\\n### Market Inefficiency Theory\\n- FVGs highlight areas where price moved too fast\\n- Represent zones with insufficient two-way trading\\n- Markets have \\\"memory\\\" of unfilled orders in these ranges\\n- Price often returns to rebalance these inefficiencies\\n\\n### Institutional Perspective\\n- Large orders couldn't be filled during rapid moves\\n- Institutions may have unfilled orders in FVG zones\\n- Smart money often waits for price to return to these levels\\n- FVGs act like magnets for future price action\\n\\n## Types of Fair Value Gaps\\n\\n### 1. Bullish FVG (Buy Side Imbalance)\\n- **Formation**: Left by strong upward movement\\n- **Expectation**: Acts as support when price returns\\n- **Trading**: Look for buying opportunities on first touch\\n- **Invalidation**: Price closes below the gap\\n\\n### 2. Bearish FVG (Sell Side Imbalance)\\n- **Formation**: Left by strong downward movement\\n- **Expectation**: Acts as resistance when price returns\\n- **Trading**: Look for selling opportunities on first touch\\n- **Invalidation**: Price closes above the gap\\n\\n## SPY/QQQ FVG Characteristics\\n\\n### SPY FVG Patterns:\\n- Often form during earnings reactions\\n- News-driven gaps create large FVGs\\n- Options expiration can trigger FVG formation\\n- Market open gaps frequently leave FVGs\\n\\n### QQQ FVG Patterns:\\n- Tech sector news creates significant FVGs\\n- Higher volatility = larger gap formations\\n- After-hours trading often leaves gaps\\n- Correlation with NASDAQ futures gaps\\n\\n## FVG Validation Criteria\\n\\n### Strong FVGs Have:\\n1. **Clean Formation**: Clear three-candle pattern\\n2. **Significant Size**: Gap represents meaningful price range\\n3. **Volume Context**: High volume on the gap-creating candle\\n4. **Timeframe Relevance**: Higher timeframes = stronger FVGs\\n\\n### Weak FVGs Show:\\n- Overlapping wicks between candles\\n- Very small gap size\\n- Low volume on formation\\n- Multiple gaps in same area\\n\\n## Real-World FVG Example\\n\\n**SPY Bullish FVG Formation:**\\n1. **9:30 AM**: SPY opens at $450, creates high at $450.50\\n2. **9:31 AM**: Strong buying pushes SPY from $450.75 to $452.25\\n3. **9:32 AM**: Pullback finds support at $451.00\\n4. **Result**: Bullish FVG from $450.50 to $451.00\\n\\n**Expected Behavior:**\\n- Price may return to $450.50-$451.00 zone\\n- First touch often provides buying opportunity\\n- Zone acts as support for future moves\\n- Invalidated if price closes below $450.50\\n        \",\n                type: \"theory\",\n                duration: \"22 min\",\n                keyPoints: [\n                    \"Fair Value Gaps represent price inefficiencies where insufficient two-way trading occurred\",\n                    \"FVGs form through the three-candle rule with clear gap between first and third candle wicks\",\n                    \"Bullish FVGs act as support zones, bearish FVGs act as resistance zones\",\n                    \"Higher timeframe FVGs are more significant and reliable than lower timeframe gaps\",\n                    \"SPY/QQQ FVGs often form during news events, market opens, and earnings reactions\"\n                ],\n                practicalExercises: [\n                    \"Identify 5 Fair Value Gaps on SPY daily chart using the three-candle rule\",\n                    \"Mark bullish and bearish FVGs with different colors on your charts\",\n                    \"Observe how price reacts when returning to previously identified FVG zones\"\n                ],\n                quiz: [\n                    {\n                        question: \"What defines a valid Fair Value Gap formation?\",\n                        options: [\n                            \"Any gap between two candles\",\n                            \"A three-candle pattern where the middle candle creates a gap between the first and third candle wicks\",\n                            \"A gap that forms at market open\",\n                            \"Any price movement with high volume\"\n                        ],\n                        correct: 1,\n                        explanation: \"A valid FVG requires a three-candle pattern where the large middle candle creates a clear gap between the first candle's high/low and the third candle's low/high.\"\n                    },\n                    {\n                        question: \"How should a bullish FVG behave when price returns to it?\",\n                        options: [\n                            \"Price should break through immediately\",\n                            \"Price should act as resistance\",\n                            \"Price should find support and potentially bounce\",\n                            \"Price should create more gaps\"\n                        ],\n                        correct: 2,\n                        explanation: \"A bullish FVG should act as a support zone when price returns to it, as the gap represents an area where buyers may step in to fill the inefficiency.\"\n                    },\n                    {\n                        question: \"When is a Fair Value Gap considered invalidated?\",\n                        options: [\n                            \"After one touch\",\n                            \"When price closes through the gap completely\",\n                            \"After 24 hours\",\n                            \"When volume decreases\"\n                        ],\n                        correct: 1,\n                        explanation: \"An FVG is invalidated when price closes completely through the gap, indicating the inefficiency has been filled and the zone no longer holds significance.\"\n                    }\n                ]\n            },\n            {\n                id: 2,\n                title: \"Higher Timeframe FVGs & Multi-Timeframe Analysis\",\n                description: \"Master the power of Higher Timeframe Fair Value Gaps and learn to combine multiple timeframes for precision entries\",\n                content: \"\\n# Higher Timeframe FVGs: The Institutional Magnets\\n\\nHigher timeframe Fair Value Gaps are among the most powerful tools in a professional trader's arsenal. These gaps act like magnets, drawing price back to fill inefficiencies left by rapid institutional moves.\\n\\n## Why Higher Timeframes Matter\\n\\n### Significance Hierarchy:\\n- **Weekly FVGs**: Extremely powerful, may take months to fill\\n- **Daily FVGs**: Very significant, often filled within days/weeks\\n- **4-Hour FVGs**: Strong levels, usually filled within days\\n- **1-Hour FVGs**: Moderate significance, filled within hours/days\\n- **15-Min FVGs**: Lower significance, often filled quickly\\n\\n### Volume and Participation:\\nHigher timeframe moves involve:\\n- More institutional participation\\n- Larger order sizes\\n- Greater market impact\\n- Broader market awareness\\n- Stronger magnetic effect\\n\\n## HTF FVG vs LTF FVG Comparison\\n\\n### Higher Timeframe FVGs (Daily+):\\n**Advantages:**\\n- Higher probability of being filled\\n- Stronger support/resistance when reached\\n- Better risk/reward opportunities\\n- Less noise and false signals\\n- Institutional relevance\\n\\n**Characteristics:**\\n- Take longer to reach\\n- Provide major turning points\\n- Often align with other key levels\\n- Create significant price reactions\\n\\n### Lower Timeframe FVGs (1H and below):\\n**Advantages:**\\n- More frequent opportunities\\n- Faster fills and reactions\\n- Good for scalping strategies\\n- Quick feedback on trades\\n\\n**Disadvantages:**\\n- Higher noise ratio\\n- More false signals\\n- Weaker reactions\\n- Less institutional relevance\\n\\n## Multi-Timeframe FVG Analysis\\n\\n### The Professional Approach:\\n1. **Mark HTF FVGs first** (Daily, 4H, 1H)\\n2. **Use LTF for entry timing** (15M, 5M)\\n3. **Combine with other confluences**\\n4. **Prioritize HTF over LTF**\\n\\n### Confluence Stacking:\\n**High-Probability Setup:**\\n- Daily FVG zone\\n- + Previous day high/low\\n- + Volume profile level\\n- + Liquidity sweep area\\n- = Maximum confluence\\n\\n## SPY/QQQ HTF FVG Patterns\\n\\n### SPY Daily FVG Characteristics:\\n- **Formation**: Often during earnings, Fed announcements, major news\\n- **Size**: Typically $2-8 gaps on daily charts\\n- **Fill Rate**: 85-90% eventually get filled\\n- **Timeframe**: Usually filled within 1-4 weeks\\n- **Reaction**: Strong bounces/rejections on first touch\\n\\n### QQQ Daily FVG Characteristics:\\n- **Formation**: Tech earnings, guidance changes, sector rotation\\n- **Size**: Typically $3-12 gaps due to higher volatility\\n- **Fill Rate**: 80-85% eventually get filled\\n- **Timeframe**: May take longer due to trend strength\\n- **Reaction**: More volatile reactions, wider zones needed\\n\\n## Trading HTF FVGs: The Professional Method\\n\\n### Step 1: Identification\\n- Scan daily/4H charts for clean FVG formations\\n- Mark gap boundaries clearly\\n- Note the context (news, earnings, etc.)\\n- Assess gap size and significance\\n\\n### Step 2: Patience\\n- Wait for price to approach the HTF FVG\\n- Don't chase - let the gap come to you\\n- Monitor lower timeframes for entry signals\\n- Prepare for potential strong reactions\\n\\n### Step 3: Entry Timing\\n- Use 15M/5M charts for precise entries\\n- Look for additional confirmations:\\n  - Lower timeframe structure breaks\\n  - Volume increases\\n  - Candlestick patterns\\n  - Momentum divergences\\n\\n### Step 4: Risk Management\\n- Stop loss beyond the FVG zone\\n- Take profits at logical levels\\n- Trail stops as trade develops\\n- Respect the power of HTF levels\\n\\n## Real-World HTF FVG Example\\n\\n**SPY Daily Bullish FVG Setup:**\\n- **Formation**: Fed announcement creates gap from $445-$448\\n- **Wait Period**: 2 weeks for price to return\\n- **Entry Signal**: 15M bullish engulfing at $446 (within FVG)\\n- **Confirmation**: Volume spike + break of 15M structure\\n- **Result**: Bounce to $452 for 1.3% profit\\n- **Risk**: Stop at $444 (below FVG) for 0.4% risk\\n- **R:R Ratio**: 3.25:1\\n\\n## Key HTF FVG Rules\\n\\n1. **Higher timeframe always wins** - HTF FVG overrides LTF signals\\n2. **First touch is strongest** - Best reactions occur on initial contact\\n3. **Partial fills are common** - Price may only fill 50-70% of gap\\n4. **Context matters** - Consider overall market trend and sentiment\\n5. **Patience pays** - Wait for proper setups, don't force trades\\n        \",\n                type: \"practical\",\n                duration: \"25 min\",\n                keyPoints: [\n                    \"Perfect liquidity sweeps follow a 4-phase pattern: approach, penetration, reversal, follow-through\",\n                    \"SPY sweeps typically extend 0.1-0.3% beyond levels with 20-50% above average volume\",\n                    \"Visual recognition includes wick formations, volume spikes, and immediate reversals\",\n                    \"Time-based patterns show highest probability during market open and close\",\n                    \"Multi-timeframe analysis provides confirmation and precise entry timing\"\n                ],\n                practicalExercises: [\n                    \"Identify and analyze 3 historical liquidity sweeps on SPY using the 4-phase pattern\",\n                    \"Mark 5 potential liquidity levels on current QQQ chart and monitor for sweep patterns\",\n                    \"Practice distinguishing between true sweeps and genuine breakouts using volume analysis\",\n                    \"Create alerts for price approaching identified liquidity levels for real-time practice\"\n                ],\n                quiz: [\n                    {\n                        question: \"What is the typical penetration distance for SPY liquidity sweeps?\",\n                        options: [\n                            \"1-2% beyond the level\",\n                            \"0.1-0.3% beyond the level\",\n                            \"5-10% beyond the level\",\n                            \"Exactly to the level\"\n                        ],\n                        correct: 1,\n                        explanation: \"SPY liquidity sweeps typically penetrate 0.1-0.3% beyond key levels - enough to trigger stops but not so much as to indicate a genuine breakout.\"\n                    },\n                    {\n                        question: \"Which phase of a liquidity sweep shows the highest volume?\",\n                        options: [\n                            \"The approach phase\",\n                            \"The penetration phase\",\n                            \"The reversal phase\",\n                            \"The follow-through phase\"\n                        ],\n                        correct: 2,\n                        explanation: \"The reversal phase typically shows the highest volume as institutional orders enter the market after stops are triggered.\"\n                    }\n                ]\n            },\n            {\n                id: 3,\n                title: \"Trading Liquidity Sweeps\",\n                description: \"How to position yourself to profit from sweep reversals\",\n                content: \"Once you identify a liquidity sweep, the next step is positioning...\",\n                type: \"strategy\",\n                duration: \"18 min\",\n                keyPoints: [\n                    \"Entry timing after sweep completion\",\n                    \"Stop loss placement strategies\",\n                    \"Target setting for sweep trades\"\n                ]\n            },\n            {\n                id: 4,\n                title: \"Sweep Analysis Workshop\",\n                description: \"Analyze real SPY/QQQ liquidity sweeps with expert commentary\",\n                content: \"Review historical examples of successful sweep trades\",\n                type: \"interactive\",\n                duration: \"7 min\",\n                keyPoints: [\n                    \"Case study analysis\",\n                    \"Pattern recognition practice\",\n                    \"Risk management examples\"\n                ]\n            }\n        ]\n    },\n    {\n        id: 3,\n        title: \"Confirmation Stacking & Multi-Factor Analysis\",\n        description: \"Master the art of stacking multiple confirmations for high-probability trades. Learn to combine price action, volume, and technical analysis for professional-level precision.\",\n        icon: \"Layers\",\n        color: \"from-purple-500 to-purple-600\",\n        estimatedTime: \"85 minutes\",\n        difficulty: \"Advanced\",\n        prerequisites: \"Understanding of liquidity sweeps and Fair Value Gaps\",\n        learningObjectives: [\n            \"Stack 3+ confirmations for every trade setup\",\n            \"Master market structure confirmations (BOS/CHoCH)\",\n            \"Integrate volume profile and order flow analysis\",\n            \"Combine multiple timeframes for precision entries\",\n            \"Develop a systematic approach to trade validation\"\n        ],\n        lessons: [\n            {\n                id: 1,\n                title: \"Confirmation Stacking Fundamentals\",\n                description: \"Learn the professional approach to stacking multiple confirmations for high-probability trade setups\",\n                content: '\\n# Confirmation Stacking: The Professional Edge\\n\\nEven when you have a strong level or setup in mind (be it a liquidity sweep or an FVG), jumping in without confirmation can be risky. Confirmation stacking means waiting for multiple signals to line up in your favor before committing to a trade.\\n\\n## The Philosophy of Confluence\\n\\n**Core Principle:**\\nRather than relying on a single indicator or one pattern, you look for an agreement among several independent clues – what traders often call confluence. The idea is to filter out low-quality setups and only act when many things point to the same conclusion.\\n\\n**Think of it this way:**\\nEach confirmation is like a piece of a puzzle. One piece alone doesn\\'t show the whole picture, but when several pieces fit together, you have a clearer image of where price might go.\\n\\n## The Five Pillars of Confirmation\\n\\n### 1. Market Structure & Price Action\\nThis refers to analyzing how price swings (highs and lows) are behaving to confirm a trend change or continuation.\\n\\n**Break of Structure (BOS):**\\n- Price takes out a significant previous high or low in the direction of a trend\\n- Confirms that trend\\'s strength\\n- Shows institutional participation\\n\\n**Change of Character (CHoCH):**\\n- Early sign of a possible trend reversal\\n- First break of a minor swing level against the trend\\n- Indicates potential shift in market sentiment\\n\\n**Example:** If QQQ has been making higher highs and higher lows (uptrend) and then suddenly makes a lower low, that\\'s a bearish CHoCH signaling the uptrend may be done.\\n\\n### 2. Volume Profile & Range Context\\nVolume Profile shows how volume has been distributed at each price, giving insight into what prices the market deems \"fair\" vs \"extreme.\"\\n\\n**Key Elements:**\\n- **Point of Control (POC):** Price with highest traded volume\\n- **Value Area (VA):** Price range where ~70% of volume occurred\\n- **Value Area High/Low (VAH/VAL):** Boundaries of fair value\\n\\n**Application:** If SPY rejects from yesterday\\'s Value Area High after a liquidity sweep, that\\'s confluence supporting a reversal trade.\\n\\n### 3. Order Flow Tools (Advanced)\\nReal-time confirmation of what\\'s happening under the hood through futures DOM, time and sales, or heatmap platforms.\\n\\n**Signals to Watch:**\\n- Absorption of selling/buying at key levels\\n- Cumulative volume delta divergences\\n- Large limit orders on the book\\n- Aggressive vs passive order flow\\n\\n### 4. Indicators & Overlays\\nTraditional technical indicators can be part of your confirmation stack, especially ones that measure trend or mean reversion.\\n\\n**VWAP (Volume Weighted Average Price):**\\n- Intraday equilibrium level\\n- Reclaiming VWAP after a sweep adds confidence\\n- Acts as dynamic support/resistance\\n\\n**Moving Averages:**\\n- 21 EMA, 50 EMA for trend confirmation\\n- Dynamic support on pullbacks\\n- Confluence with other levels\\n\\n### 5. Liquidity & HTF Levels\\nCombining liquidity sweeps and HTF FVGs as part of confirmation checklist.\\n\\n**High-Probability Setup:**\\n- Liquidity sweep at HTF FVG\\n- + Market structure confirmation\\n- + Volume profile level\\n- + VWAP reclaim\\n- = Maximum confluence\\n\\n## The Professional Confirmation Checklist\\n\\n### Minimum Requirements:\\n**For Entry:** At least 2-3 solid confirmations\\n**For High-Conviction Trades:** 4+ confirmations aligned\\n\\n### Example Checklist:\\n1. ✓ Liquidity sweep occurred\\n2. ✓ Price action confirmation (CHoCH/BOS)\\n3. ✓ Volume profile level confluence\\n4. ✓ HTF FVG zone\\n5. ✓ VWAP reclaim/rejection\\n\\n## Real-World Confirmation Stacking Example\\n\\n### SPY Bearish Setup:\\n**Setup:** SPY approaches yesterday\\'s high at $450\\n\\n**Confirmations:**\\n1. **Liquidity Sweep:** Price hits $450.50, sweeps stops\\n2. **HTF Level:** Daily bearish FVG zone at $450-451\\n3. **Price Action:** 5-minute bearish engulfing + CHoCH\\n4. **Volume Profile:** Rejection from yesterday\\'s VAH\\n5. **Volume:** Spike on sweep, sustained on reversal\\n\\n**Entry:** Short at $449.50 after all confirmations align\\n**Stop:** $451 (above sweep high)\\n**Target:** $445 (previous support)\\n**Result:** 1% profit with 0.3% risk = 3.3:1 R/R\\n\\n## Avoiding Analysis Paralysis\\n\\n### Balance is Key:\\n- Too few confirmations = low probability\\n- Too many confirmations = missed opportunities\\n- Sweet spot: 2-3 strong confirmations\\n\\n### Weighting Confirmations:\\n**Primary (Must Have):**\\n- Price action signal (structure break)\\n- Key level confluence (liquidity/FVG)\\n\\n**Secondary (Nice to Have):**\\n- Volume confirmation\\n- Indicator alignment\\n- Higher timeframe context\\n\\n## Common Confirmation Mistakes\\n\\n1. **Forcing Confluence:** Seeing confirmations that aren\\'t really there\\n2. **Over-Analysis:** Requiring too many signals\\n3. **Ignoring Context:** Not considering overall market environment\\n4. **Static Thinking:** Not adapting to changing market conditions\\n5. **Confirmation Bias:** Only seeing signals that support your bias\\n- Unfilled orders create demand/supply\\n- Technical traders target gap fills\\n- Self-fulfilling prophecy effect\\n\\n## FVG vs. Regular Gaps\\n\\n### Fair Value Gaps:\\n- Formed by 3-candle pattern\\n- Represent order flow imbalance\\n- High probability of fill (70-80%)\\n- Can be traded in both directions\\n- Show institutional activity\\n\\n### Regular Price Gaps:\\n- Formed between sessions (overnight)\\n- Caused by news or events\\n- Lower probability of fill (40-60%)\\n- Often indicate trend continuation\\n- May not represent institutional flow\\n\\n## Psychological Aspects of FVG Trading\\n\\n### Institutional Perspective:\\n- \"We moved price too fast\"\\n- \"Need to fill remaining orders\"\\n- \"Better prices available in the gap\"\\n- \"Risk management requires rebalancing\"\\n\\n### Retail Perspective:\\n- \"Price gapped away from me\"\\n- \"I missed the move\"\\n- \"Will it come back?\"\\n- \"Should I chase or wait?\"\\n\\n## Real-World FVG Examples\\n\\n### Example 1: SPY Bullish FVG\\n**Setup:** Fed announcement creates buying surge\\n**Formation:** 3-candle bullish FVG at $445-$447\\n**Fill:** Price returns to gap 5 days later\\n**Outcome:** Perfect bounce from gap support\\n\\n### Example 2: QQQ Bearish FVG\\n**Setup:** Tech earnings disappointment\\n**Formation:** 3-candle bearish FVG at $380-$382\\n**Fill:** Price rallies to gap 2 weeks later\\n**Outcome:** Strong resistance at gap level\\n\\n## Advanced FVG Concepts\\n\\n### 1. Nested FVGs\\n- Multiple gaps within larger gaps\\n- Provide multiple trading opportunities\\n- Show sustained institutional activity\\n- Require careful order management\\n\\n### 2. FVG Clusters\\n- Multiple gaps in same price area\\n- Extremely high probability zones\\n- Often mark major support/resistance\\n- Institutional accumulation/distribution areas\\n\\n### 3. Partial vs. Full Fills\\n- **Partial Fill:** Price touches gap but doesn\\'t close it\\n- **Full Fill:** Price completely closes the gap\\n- **Overfill:** Price extends beyond the gap\\n- Each has different trading implications\\n\\n## Common FVG Mistakes\\n\\n1. **Trading Every Gap:** Not all FVGs are equal quality\\n2. **Ignoring Context:** Market structure matters\\n3. **Poor Risk Management:** Gaps can extend before filling\\n4. **Wrong Timeframe:** Match timeframe to trading style\\n5. **Emotional Trading:** FOMO on gap formations\\n        ',\n                type: \"theory\",\n                duration: \"25 min\",\n                keyPoints: [\n                    \"Fair Value Gaps represent institutional order flow imbalances created by overwhelming buying/selling pressure\",\n                    \"FVGs require exactly 3 candles with no overlap between outer candles' high/low\",\n                    \"SPY FVGs typically range 0.2-0.8% while QQQ ranges 0.3-1.2% due to higher volatility\",\n                    \"70-80% of FVGs get filled within 5-20 sessions as markets seek price efficiency\",\n                    \"Inversion FVGs become powerful support/resistance after being filled\"\n                ],\n                practicalExercises: [\n                    \"Identify 5 bullish and 5 bearish FVGs on SPY 30-minute chart from last month\",\n                    \"Measure the size of each FVG as percentage of price and compare to typical ranges\",\n                    \"Track which FVGs got filled and calculate the fill rate for your sample\",\n                    \"Practice distinguishing between FVGs and regular overnight gaps\"\n                ],\n                quiz: [\n                    {\n                        question: \"How many candles are required to form a Fair Value Gap?\",\n                        options: [\n                            \"2 candles\",\n                            \"3 candles\",\n                            \"4 candles\",\n                            \"5 candles\"\n                        ],\n                        correct: 1,\n                        explanation: \"A Fair Value Gap requires exactly 3 consecutive candles, with the middle candle creating the imbalance and no overlap between the outer candles.\"\n                    },\n                    {\n                        question: \"What percentage of Fair Value Gaps typically get filled?\",\n                        options: [\n                            \"30-40%\",\n                            \"50-60%\",\n                            \"70-80%\",\n                            \"90-100%\"\n                        ],\n                        correct: 2,\n                        explanation: \"Approximately 70-80% of Fair Value Gaps get filled as markets naturally seek price efficiency and institutional orders get completed.\"\n                    }\n                ]\n            },\n            {\n                id: 2,\n                title: \"FVG Classification System\",\n                description: \"Learn to classify FVGs by strength and probability\",\n                content: \"Not all FVGs are equal. Learn the classification system...\",\n                type: \"practical\",\n                duration: \"15 min\",\n                keyPoints: [\n                    \"High probability vs low probability gaps\",\n                    \"Size and context importance\",\n                    \"Multiple timeframe FVG analysis\"\n                ]\n            },\n            {\n                id: 3,\n                title: \"Trading FVG Fills\",\n                description: \"Strategies for trading when price returns to fill gaps\",\n                content: \"FVG fills often provide excellent trading opportunities...\",\n                type: \"strategy\",\n                duration: \"16 min\",\n                keyPoints: [\n                    \"Partial vs full gap fills\",\n                    \"Entry and exit strategies\",\n                    \"Combining FVGs with other confluences\"\n                ]\n            },\n            {\n                id: 4,\n                title: \"FVG Recognition Challenge\",\n                description: \"Test your ability to spot and classify FVGs in real-time\",\n                content: \"Interactive challenge to identify FVGs on live charts\",\n                type: \"interactive\",\n                duration: \"7 min\",\n                keyPoints: [\n                    \"Speed recognition drills\",\n                    \"Classification accuracy\",\n                    \"Real-time decision making\"\n                ]\n            }\n        ]\n    },\n    {\n        id: 4,\n        title: \"Volume Analysis & Confirmation\",\n        description: \"Use volume analysis to confirm your price action signals\",\n        icon: \"Activity\",\n        color: \"from-orange-500 to-orange-600\",\n        estimatedTime: \"40 minutes\",\n        difficulty: \"Beginner\",\n        lessons: [\n            {\n                id: 1,\n                title: \"Volume Fundamentals\",\n                description: \"Understanding volume and its relationship to price movement\",\n                content: \"Volume is the fuel that drives price movement...\",\n                type: \"theory\",\n                duration: \"10 min\",\n                keyPoints: [\n                    \"Volume precedes price\",\n                    \"Accumulation vs distribution patterns\",\n                    \"Volume profile concepts\"\n                ]\n            },\n            {\n                id: 2,\n                title: \"Volume at Key Levels\",\n                description: \"Analyzing volume behavior at support/resistance zones\",\n                content: \"How volume behaves at key levels tells us about market sentiment...\",\n                type: \"practical\",\n                duration: \"15 min\",\n                keyPoints: [\n                    \"Rising volume on approach to zones\",\n                    \"Fading volume and false breakouts\",\n                    \"Climactic volume patterns\"\n                ]\n            },\n            {\n                id: 3,\n                title: \"Volume Confirmation Strategies\",\n                description: \"Using volume to confirm your trading signals\",\n                content: \"Volume confirmation can significantly improve trade success rates...\",\n                type: \"strategy\",\n                duration: \"12 min\",\n                keyPoints: [\n                    \"Volume divergence signals\",\n                    \"Confirmation vs contradiction\",\n                    \"Multiple timeframe volume analysis\"\n                ]\n            },\n            {\n                id: 4,\n                title: \"Volume Analysis Practice\",\n                description: \"Practice reading volume patterns on SPY/QQQ charts\",\n                content: \"Hands-on practice with volume analysis techniques\",\n                type: \"interactive\",\n                duration: \"3 min\",\n                keyPoints: [\n                    \"Pattern recognition\",\n                    \"Signal confirmation practice\",\n                    \"Real-world application\"\n                ]\n            }\n        ]\n    },\n    {\n        id: 5,\n        title: \"Confirmation Stacking\",\n        description: \"Learn to stack multiple confirmations for high-probability trades\",\n        icon: \"Layers\",\n        color: \"from-red-500 to-red-600\",\n        estimatedTime: \"55 minutes\",\n        difficulty: \"Advanced\",\n        lessons: [\n            {\n                id: 1,\n                title: \"The Stacking Methodology\",\n                description: \"Understanding the concept of confirmation stacking\",\n                content: \"Confirmation stacking involves combining multiple technical signals...\",\n                type: \"theory\",\n                duration: \"12 min\",\n                keyPoints: [\n                    \"Quality over quantity in confirmations\",\n                    \"Weighted confirmation systems\",\n                    \"Avoiding analysis paralysis\"\n                ]\n            },\n            {\n                id: 2,\n                title: \"Building Your Stack\",\n                description: \"How to systematically build confirmation stacks\",\n                content: \"Learn the systematic approach to building robust confirmation stacks...\",\n                type: \"practical\",\n                duration: \"18 min\",\n                keyPoints: [\n                    \"Primary vs secondary confirmations\",\n                    \"Timeframe hierarchy\",\n                    \"Confluence zone identification\"\n                ]\n            },\n            {\n                id: 3,\n                title: \"Advanced Stacking Techniques\",\n                description: \"Professional-level confirmation stacking strategies\",\n                content: \"Advanced techniques used by professional traders...\",\n                type: \"strategy\",\n                duration: \"20 min\",\n                keyPoints: [\n                    \"Multi-timeframe stacking\",\n                    \"Intermarket confirmations\",\n                    \"Sentiment-based confirmations\"\n                ]\n            },\n            {\n                id: 4,\n                title: \"Stacking Mastery Challenge\",\n                description: \"Put your stacking skills to the test with complex scenarios\",\n                content: \"Advanced challenge scenarios to test your mastery\",\n                type: \"interactive\",\n                duration: \"5 min\",\n                keyPoints: [\n                    \"Complex scenario analysis\",\n                    \"Decision-making under pressure\",\n                    \"Professional-level execution\"\n                ]\n            }\n        ]\n    },\n    {\n        id: 6,\n        title: \"Risk Management & Psychology\",\n        description: \"Master the mental game and risk management for consistent profits\",\n        icon: \"Shield\",\n        color: \"from-indigo-500 to-indigo-600\",\n        estimatedTime: \"45 minutes\",\n        difficulty: \"Intermediate\",\n        lessons: [\n            {\n                id: 1,\n                title: \"Position Sizing Fundamentals\",\n                description: \"Calculate optimal position sizes for your account\",\n                content: \"Proper position sizing is the foundation of risk management...\",\n                type: \"theory\",\n                duration: \"12 min\",\n                keyPoints: [\n                    \"Risk percentage rules\",\n                    \"Account size considerations\",\n                    \"Volatility-adjusted sizing\"\n                ]\n            },\n            {\n                id: 2,\n                title: \"Stop Loss Strategies\",\n                description: \"Advanced stop loss placement and management techniques\",\n                content: \"Stop losses are your insurance policy in trading...\",\n                type: \"practical\",\n                duration: \"15 min\",\n                keyPoints: [\n                    \"Technical vs percentage stops\",\n                    \"Trailing stop strategies\",\n                    \"Stop loss psychology\"\n                ]\n            },\n            {\n                id: 3,\n                title: \"Trading Psychology Mastery\",\n                description: \"Develop the mental discipline required for consistent trading\",\n                content: \"Trading psychology often determines success more than technical skills...\",\n                type: \"strategy\",\n                duration: \"15 min\",\n                keyPoints: [\n                    \"Emotional regulation techniques\",\n                    \"Dealing with losses\",\n                    \"Maintaining discipline\"\n                ]\n            },\n            {\n                id: 4,\n                title: \"Psychology Assessment\",\n                description: \"Evaluate your trading psychology and identify areas for improvement\",\n                content: \"Self-assessment tools for trading psychology\",\n                type: \"interactive\",\n                duration: \"3 min\",\n                keyPoints: [\n                    \"Psychological profiling\",\n                    \"Weakness identification\",\n                    \"Improvement planning\"\n                ]\n            }\n        ]\n    },\n    {\n        id: 7,\n        title: \"Advanced Pattern Recognition\",\n        description: \"Identify complex patterns and market structures for professional-level trading\",\n        icon: \"Eye\",\n        color: \"from-teal-500 to-teal-600\",\n        estimatedTime: \"65 minutes\",\n        difficulty: \"Advanced\",\n        lessons: [\n            {\n                id: 1,\n                title: \"Complex Pattern Structures\",\n                description: \"Understanding advanced chart patterns and their implications\",\n                content: \"Advanced patterns often provide the highest probability setups...\",\n                type: \"theory\",\n                duration: \"18 min\",\n                keyPoints: [\n                    \"Multi-timeframe pattern analysis\",\n                    \"Pattern failure and continuation\",\n                    \"Context-dependent patterns\"\n                ]\n            },\n            {\n                id: 2,\n                title: \"Market Structure Shifts\",\n                description: \"Identifying when market structure changes and how to adapt\",\n                content: \"Market structure shifts signal major changes in sentiment...\",\n                type: \"practical\",\n                duration: \"20 min\",\n                keyPoints: [\n                    \"Break of structure signals\",\n                    \"Change of character patterns\",\n                    \"Trend transition identification\"\n                ]\n            },\n            {\n                id: 3,\n                title: \"Professional Pattern Trading\",\n                description: \"How professionals trade complex patterns for maximum profit\",\n                content: \"Professional trading strategies for advanced patterns...\",\n                type: \"strategy\",\n                duration: \"22 min\",\n                keyPoints: [\n                    \"Entry and exit optimization\",\n                    \"Risk-reward maximization\",\n                    \"Pattern-specific strategies\"\n                ]\n            },\n            {\n                id: 4,\n                title: \"Pattern Mastery Exam\",\n                description: \"Final examination of your pattern recognition abilities\",\n                content: \"Comprehensive test of all pattern recognition skills\",\n                type: \"interactive\",\n                duration: \"5 min\",\n                keyPoints: [\n                    \"Comprehensive pattern test\",\n                    \"Speed and accuracy assessment\",\n                    \"Professional certification\"\n                ]\n            }\n        ]\n    }\n];\nconst COURSE_ACHIEVEMENTS = [\n    {\n        id: \"first_lesson\",\n        title: \"Getting Started\",\n        description: \"Complete your first lesson\",\n        icon: \"Play\",\n        points: 10\n    },\n    {\n        id: \"first_module\",\n        title: \"Module Master\",\n        description: \"Complete your first module\",\n        icon: \"Award\",\n        points: 50\n    },\n    {\n        id: \"quiz_master\",\n        title: \"Quiz Master\",\n        description: \"Score 90% or higher on 5 quizzes\",\n        icon: \"Brain\",\n        points: 100\n    },\n    {\n        id: \"speed_learner\",\n        title: \"Speed Learner\",\n        description: \"Complete 3 lessons in one day\",\n        icon: \"Zap\",\n        points: 75\n    },\n    {\n        id: \"course_complete\",\n        title: \"Course Graduate\",\n        description: \"Complete the entire course\",\n        icon: \"GraduationCap\",\n        points: 500\n    }\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/data/courseData.js\n"));

/***/ })

});