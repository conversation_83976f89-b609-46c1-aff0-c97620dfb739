"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/data/courseData.js":
/*!********************************!*\
  !*** ./src/data/courseData.js ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   COURSE_ACHIEVEMENTS: function() { return /* binding */ COURSE_ACHIEVEMENTS; },\n/* harmony export */   COURSE_MODULES: function() { return /* binding */ COURSE_MODULES; }\n/* harmony export */ });\n/**\n * Advanced Price Action Course Data\n * Professional SPY/QQQ Trading Course - Liquidity Sweeps, FVGs, and Confirmation Stacking\n * Complete educational content with real trading strategies\n */ const COURSE_MODULES = [\n    {\n        id: 1,\n        title: \"Market Structure & Liquidity Fundamentals\",\n        description: \"Master the institutional approach to reading market structure. Learn how professional traders identify liquidity zones and predict price movement on SPY/QQQ.\",\n        icon: \"Target\",\n        color: \"from-blue-500 to-blue-600\",\n        estimatedTime: \"90 minutes\",\n        difficulty: \"Beginner\",\n        prerequisites: \"Basic understanding of candlestick charts and support/resistance levels\",\n        learningObjectives: [\n            \"Decode institutional market structure patterns with professional precision\",\n            \"Identify high-probability liquidity zones where smart money operates\",\n            \"Distinguish between genuine breakouts and institutional stop hunts\",\n            \"Apply market structure analysis to SPY/QQQ for consistent profits\",\n            \"Master the professional trader's mindset: 'liquidity drives direction'\"\n        ],\n        lessons: [\n            {\n                id: 1,\n                title: \"Institutional Market Structure: Reading the Smart Money Footprints\",\n                description: \"Discover how professional traders analyze market structure to predict institutional moves and identify high-probability trading opportunities\",\n                imageUrl: \"https://images.unsplash.com/photo-1611974789855-9c2a0a7236a3?w=800&h=400&fit=crop\",\n                content: \"\\n# Chapter 1: Institutional Market Structure Analysis\\n\\n*\\\"The market is a voting machine in the short run, but a weighing machine in the long run. Understanding who's voting and why they're weighing gives you the ultimate edge.\\\"* - Professional Trading Axiom\\n\\n---\\n\\n## Section 1.1: The Professional Trader's Perspective\\n\\n### What Separates Institutional Traders from Retail?\\n\\nProfessional institutional traders operate with a fundamentally different approach than retail traders. While retail traders often focus on indicators and patterns, institutions analyze **market structure** - the underlying framework that reveals where smart money is positioned and where they're likely to move next.\\n\\n**Key Institutional Advantages:**\\n- Access to order flow data showing real-time buying/selling pressure\\n- Understanding of where retail stops cluster (their liquidity targets)\\n- Ability to move markets through large position sizes\\n- Advanced risk management systems that retail traders lack\\n\\n### The Market Structure Hierarchy\\n\\nMarket structure operates on multiple levels, each providing different insights:\\n\\n**1. Primary Structure (Daily/Weekly)**\\n- Major swing highs and lows that define long-term trends\\n- Key institutional accumulation and distribution zones\\n- Primary support and resistance levels that matter to big money\\n\\n**2. Secondary Structure (4H/1H)**\\n- Intermediate swings that show institutional positioning\\n- Areas where institutions build or reduce positions\\n- Critical levels for swing trading opportunities\\n\\n**3. Tertiary Structure (15M/5M)**\\n- Short-term swings for precise entry and exit timing\\n- Scalping opportunities within larger institutional moves\\n- Fine-tuning entries for optimal risk/reward ratios\\n\\n---\\n\\n## Section 1.2: Market Structure Components\\n\\n### Higher Highs and Higher Lows (Uptrend Structure)\\n\\nIn a healthy uptrend, price creates a series of higher highs (HH) and higher lows (HL). This pattern indicates:\\n\\n- **Institutional Accumulation**: Smart money is building long positions\\n- **Retail FOMO**: Fear of missing out drives retail buying at higher prices\\n- **Momentum Continuation**: Each pullback finds buyers at higher levels\\n\\n**Professional Trading Insight**: The quality of higher lows is more important than higher highs. Strong higher lows with volume support indicate institutional backing.\\n\\n### Lower Highs and Lower Lows (Downtrend Structure)\\n\\nIn a bearish market structure, price creates lower highs (LH) and lower lows (LL):\\n\\n- **Institutional Distribution**: Smart money is reducing long positions or building shorts\\n- **Retail Denial**: Retail traders often buy \\\"dips\\\" that continue falling\\n- **Momentum Breakdown**: Each rally fails at lower levels\\n\\n### Sideways Structure (Accumulation/Distribution)\\n\\nWhen price moves sideways between defined levels:\\n\\n- **Accumulation Phase**: Institutions quietly build positions before markup\\n- **Distribution Phase**: Institutions exit positions before markdown\\n- **Retail Confusion**: Sideways action frustrates retail traders into poor decisions\\n\\n---\\n\\n## Section 1.3: SPY/QQQ Specific Characteristics\\n\\n### SPY (S&P 500 ETF) Structure Patterns\\n\\n**Market Hours Behavior:**\\n- **9:30-10:30 AM**: Initial balance formation, often with false breakouts\\n- **10:30 AM-3:00 PM**: Trending moves or range-bound consolidation\\n- **3:00-4:00 PM**: Institutional positioning for overnight holds\\n\\n**Key SPY Levels:**\\n- **Psychological Levels**: Round numbers (400, 450, 500) act as magnets\\n- **Previous Day Extremes**: High/low from prior session are critical\\n- **Weekly/Monthly Levels**: Major institutional reference points\\n\\n### QQQ (Nasdaq 100 ETF) Structure Patterns\\n\\n**Technology Sector Sensitivity:**\\n- More volatile than SPY due to growth stock concentration\\n- Reacts strongly to tech earnings and guidance changes\\n- Higher beta creates larger structure swings\\n\\n**QQQ-Specific Considerations:**\\n- **After-hours Impact**: Tech stocks trade actively after market close\\n- **Correlation Breaks**: Sometimes diverges from SPY during sector rotation\\n- **Momentum Extremes**: Can extend further than SPY in both directions\\n\\n---\\n\\n## Section 1.4: Professional Structure Analysis Techniques\\n\\n### The Three-Timeframe Approach\\n\\n**1. Higher Timeframe Context (Daily/4H)**\\n- Identifies the primary trend direction\\n- Shows major institutional positioning\\n- Provides overall market bias for trading decisions\\n\\n**2. Entry Timeframe Analysis (1H/30M)**\\n- Pinpoints specific entry and exit opportunities\\n- Shows intermediate structure breaks and confirmations\\n- Balances precision with broader market context\\n\\n**3. Execution Timeframe Precision (15M/5M)**\\n- Fine-tunes exact entry and exit points\\n- Manages risk with tight stop-loss placement\\n- Maximizes risk/reward ratios through precise timing\\n\\n### Structure Quality Assessment\\n\\n**Strong Structure Characteristics:**\\n- Clear, well-defined swing points with significant price separation\\n- Volume confirmation at key structural levels\\n- Multiple timeframe alignment showing consistent patterns\\n- Clean breaks with follow-through momentum\\n\\n**Weak Structure Warning Signs:**\\n- Overlapping swing points creating confusion\\n- Low volume at critical structural levels\\n- Conflicting signals across different timeframes\\n- Failed breaks with immediate reversals\\n\\n---\\n\\n## Section 1.5: Practical Application Framework\\n\\n### Daily Structure Analysis Routine\\n\\n**Morning Preparation (Pre-Market):**\\n1. Identify overnight structure changes in SPY/QQQ\\n2. Mark key levels from previous session's structure\\n3. Note any gaps or significant news that might affect structure\\n4. Plan potential scenarios based on structure analysis\\n\\n**Intraday Monitoring:**\\n1. Track real-time structure development\\n2. Identify structure breaks as they occur\\n3. Assess the quality and follow-through of breaks\\n4. Adjust trading bias based on evolving structure\\n\\n**End-of-Day Review:**\\n1. Analyze how structure played out during the session\\n2. Identify successful and failed structure predictions\\n3. Update key levels for next trading session\\n4. Document lessons learned for continuous improvement\\n\\n### Risk Management Through Structure\\n\\n**Structure-Based Stop Placement:**\\n- Place stops beyond significant structure levels\\n- Use structure to determine position sizing\\n- Adjust stops as structure evolves intraday\\n\\n**Profit Target Selection:**\\n- Target next significant structure level\\n- Use structure to determine risk/reward ratios\\n- Scale out at multiple structure-based targets\\n        \",\n                type: \"foundational\",\n                duration: \"35 min\",\n                sections: [\n                    {\n                        title: \"The Professional Trader's Perspective\",\n                        duration: \"8 min\",\n                        keyPoints: [\n                            \"Institutional vs retail trading approaches\",\n                            \"Market structure hierarchy and timeframe analysis\",\n                            \"Professional advantages in market analysis\"\n                        ]\n                    },\n                    {\n                        title: \"Market Structure Components\",\n                        duration: \"12 min\",\n                        keyPoints: [\n                            \"Higher highs/higher lows in uptrend analysis\",\n                            \"Lower highs/lower lows in downtrend identification\",\n                            \"Sideways structure and accumulation/distribution phases\"\n                        ]\n                    },\n                    {\n                        title: \"SPY/QQQ Specific Analysis\",\n                        duration: \"10 min\",\n                        keyPoints: [\n                            \"SPY market hours behavior and key levels\",\n                            \"QQQ technology sector sensitivity patterns\",\n                            \"ETF-specific structure characteristics\"\n                        ]\n                    },\n                    {\n                        title: \"Professional Application Framework\",\n                        duration: \"5 min\",\n                        keyPoints: [\n                            \"Three-timeframe analysis approach\",\n                            \"Daily structure analysis routine\",\n                            \"Risk management through structure\"\n                        ]\n                    }\n                ],\n                keyPoints: [\n                    \"Market structure reveals institutional positioning and smart money flow\",\n                    \"Three-timeframe analysis provides context, entry signals, and execution precision\",\n                    \"SPY/QQQ structure patterns differ due to sector composition and volatility\",\n                    \"Professional traders use structure for risk management and profit targeting\",\n                    \"Quality structure assessment separates high-probability from low-probability setups\"\n                ],\n                practicalExercises: [\n                    \"Analyze current SPY daily chart and identify primary, secondary, and tertiary structure levels\",\n                    \"Compare SPY vs QQQ structure patterns over the past week and note differences\",\n                    \"Practice the three-timeframe approach: Daily context → 1H entries → 15M execution\",\n                    \"Create a daily structure analysis routine and apply it for one full trading week\"\n                ],\n                quiz: [\n                    {\n                        question: \"What is the most important difference between institutional and retail trading approaches?\",\n                        options: [\n                            \"Institutions use more indicators and technical analysis tools\",\n                            \"Institutions focus on market structure while retail focuses on indicators\",\n                            \"Institutions trade larger position sizes with more capital\",\n                            \"Institutions have access to better charting software\"\n                        ],\n                        correct: 1,\n                        explanation: \"The fundamental difference is analytical approach: institutions analyze market structure to understand where smart money is positioned, while retail traders typically rely on lagging indicators and patterns.\"\n                    },\n                    {\n                        question: \"In the three-timeframe approach, what is the primary purpose of the higher timeframe analysis?\",\n                        options: [\n                            \"To find exact entry and exit points\",\n                            \"To identify the primary trend direction and institutional positioning\",\n                            \"To determine precise stop-loss placement\",\n                            \"To calculate position sizing for trades\"\n                        ],\n                        correct: 1,\n                        explanation: \"Higher timeframe analysis (Daily/4H) provides the overall market context, showing the primary trend direction and major institutional positioning that guides all trading decisions.\"\n                    },\n                    {\n                        question: \"What makes QQQ structure analysis different from SPY?\",\n                        options: [\n                            \"QQQ has lower volatility and smaller price movements\",\n                            \"QQQ only trades during regular market hours\",\n                            \"QQQ is more sensitive to technology sector news and has higher volatility\",\n                            \"QQQ structure patterns are identical to SPY patterns\"\n                        ],\n                        correct: 2,\n                        explanation: \"QQQ's concentration in technology stocks makes it more volatile than SPY and highly sensitive to tech sector news, earnings, and guidance changes, creating different structure patterns.\"\n                    },\n                    {\n                        question: \"What characterizes 'strong structure' in professional analysis?\",\n                        options: [\n                            \"Many overlapping swing points with frequent reversals\",\n                            \"Clear, well-defined swing points with volume confirmation and timeframe alignment\",\n                            \"Low volume at structural levels with minimal price separation\",\n                            \"Conflicting signals across different timeframes\"\n                        ],\n                        correct: 1,\n                        explanation: \"Strong structure features clear swing points with significant price separation, volume confirmation at key levels, multiple timeframe alignment, and clean breaks with follow-through.\"\n                    },\n                    {\n                        question: \"During SPY's typical trading day, when do the most significant institutional positioning moves occur?\",\n                        options: [\n                            \"During the first 30 minutes after market open\",\n                            \"During the lunch hour (12:00-1:00 PM)\",\n                            \"During the final hour (3:00-4:00 PM) for overnight positioning\",\n                            \"Institutional moves are evenly distributed throughout the day\"\n                        ],\n                        correct: 2,\n                        explanation: \"The final trading hour (3:00-4:00 PM) is when institutions make their most significant positioning moves, preparing for overnight holds and next-day strategies.\"\n                    }\n                ]\n            },\n            {\n                id: 2,\n                title: \"Liquidity Sweeps: The Professional's Guide to Reading Institutional Moves\",\n                description: \"Master the art of identifying and trading liquidity sweeps - the institutional strategy that moves markets and creates the highest probability trading opportunities\",\n                imageUrl: \"https://images.unsplash.com/photo-1590283603385-17ffb3a7f29f?w=800&h=400&fit=crop\",\n                content: \"\\n# Chapter 2: Liquidity Sweeps - Decoding Institutional Market Manipulation\\n\\n*\\\"The best trades come from understanding where the weak hands are positioned and how the strong hands will exploit them.\\\"* - Professional Trading Principle\\n\\n---\\n\\n## Section 2.1: The Institutional Liquidity Strategy\\n\\n### What Are Liquidity Sweeps?\\n\\nA **liquidity sweep** is a deliberate institutional strategy where large market participants drive price through obvious levels to trigger clusters of retail stop orders. This isn't random market movement - it's calculated exploitation of predictable retail behavior.\\n\\n**The Institutional Advantage:**\\n- Institutions know where retail stops cluster (obvious levels)\\n- They have the capital to move price temporarily\\n- They use retail liquidity to fill their large orders\\n- They profit from the subsequent reversal\\n\\n### Why Institutions Need Liquidity\\n\\n**The Large Order Problem:**\\nWhen institutions need to buy or sell millions of dollars worth of stock, they face a critical challenge:\\n\\n- **Market Impact**: Large orders move prices against them\\n- **Slippage**: Poor fills reduce profitability\\n- **Visibility**: Other institutions can front-run their orders\\n- **Timing**: They need liquidity when they want it, not when it's naturally available\\n\\n**The Liquidity Solution:**\\nBy triggering retail stops, institutions create artificial liquidity pools they can exploit for better fills.\\n\\n---\\n\\n## Section 2.2: The Anatomy of a Professional Liquidity Sweep\\n\\n### Phase 1: The Setup (Accumulation)\\n**Duration**: Hours to days\\n**Characteristics**:\\n- Price consolidates near a key level\\n- Retail traders place obvious stops just beyond the level\\n- Institutional algorithms identify the stop clusters\\n- Volume decreases as the setup develops\\n\\n**Professional Insight**: The longer the consolidation, the more stops accumulate, creating a larger liquidity pool for institutions to exploit.\\n\\n### Phase 2: The Hunt (Execution)\\n**Duration**: Minutes to hours\\n**Characteristics**:\\n- Price accelerates toward the target level\\n- Momentum builds as breakout traders join\\n- Volume increases as the level approaches\\n- Retail FOMO (fear of missing out) intensifies\\n\\n**Key Indicators**:\\n- Sudden volume spikes without news catalysts\\n- Price acceleration into obvious levels\\n- Breakout confirmation signals triggering\\n\\n### Phase 3: The Sweep (Liquidity Grab)\\n**Duration**: Seconds to minutes\\n**Characteristics**:\\n- Price penetrates the level by a small margin (typically 0.1-0.3% for SPY)\\n- Massive volume spike as stops trigger\\n- Institutions absorb the triggered orders\\n- Price immediately stalls or reverses\\n\\n**Critical Measurements**:\\n- **SPY Penetration**: Usually $0.50-$1.50 beyond level\\n- **QQQ Penetration**: Usually $0.75-$2.25 beyond level\\n- **Volume Spike**: 200-500% of average volume\\n- **Time Duration**: Rarely sustains beyond 5-15 minutes\\n\\n### Phase 4: The Reversal (Institutional Profit)\\n**Duration**: Minutes to hours\\n**Characteristics**:\\n- Sharp reversal back through the swept level\\n- Higher volume than the initial sweep\\n- Sustained momentum in the reversal direction\\n- Retail traders trapped in losing positions\\n\\n**Professional Recognition**:\\n- Volume on reversal exceeds sweep volume\\n- Price moves decisively away from swept level\\n- Previous support becomes resistance (or vice versa)\\n- Follow-through confirms institutional participation\\n\\n---\\n\\n## Section 2.3: SPY/QQQ Specific Sweep Patterns\\n\\n### SPY Liquidity Characteristics\\n\\n**High-Probability Sweep Levels:**\\n- **Previous Day High/Low**: Most reliable sweep targets\\n- **Round Numbers**: $400, $450, $500 act as psychological magnets\\n- **Weekly/Monthly Extremes**: Major institutional reference points\\n- **Gap Levels**: Overnight gaps create obvious targets\\n\\n**SPY Sweep Timing Patterns:**\\n- **9:30-10:00 AM**: Morning volatility creates sweep opportunities\\n- **11:30 AM-12:30 PM**: Pre-lunch positioning sweeps\\n- **3:00-4:00 PM**: End-of-day institutional positioning\\n- **Economic Releases**: News-driven sweeps during data releases\\n\\n### QQQ Unique Sweep Characteristics\\n\\n**Technology Sector Amplification:**\\n- Higher volatility creates larger sweep distances\\n- After-hours news impacts next-day sweep probability\\n- Earnings season increases sweep frequency\\n- Growth stock sensitivity amplifies reversal moves\\n\\n**QQQ-Specific Patterns:**\\n- **Tech Earnings Sweeps**: Pre/post earnings volatility\\n- **Fed Meeting Sweeps**: Interest rate sensitivity\\n- **Sector Rotation Sweeps**: Growth vs value transitions\\n- **Options Expiration Sweeps**: Monthly/weekly expiry effects\\n        \",\n                type: \"advanced\",\n                duration: \"40 min\",\n                sections: [\n                    {\n                        title: \"The Institutional Liquidity Strategy\",\n                        duration: \"10 min\",\n                        keyPoints: [\n                            \"Understanding why institutions need liquidity sweeps\",\n                            \"The large order problem and institutional solutions\",\n                            \"How institutions exploit predictable retail behavior\"\n                        ]\n                    },\n                    {\n                        title: \"Anatomy of Professional Liquidity Sweeps\",\n                        duration: \"15 min\",\n                        keyPoints: [\n                            \"Four-phase sweep analysis: Setup, Hunt, Sweep, Reversal\",\n                            \"Critical measurements for SPY/QQQ sweep identification\",\n                            \"Professional recognition techniques and timing\"\n                        ]\n                    },\n                    {\n                        title: \"SPY/QQQ Specific Sweep Patterns\",\n                        duration: \"15 min\",\n                        keyPoints: [\n                            \"High-probability sweep levels and timing patterns\",\n                            \"Technology sector amplification in QQQ sweeps\",\n                            \"Market hours behavior and institutional positioning\"\n                        ]\n                    }\n                ],\n                keyPoints: [\n                    \"Liquidity sweeps are calculated institutional strategies, not random market movements\",\n                    \"Four-phase anatomy: Setup → Hunt → Sweep → Reversal provides professional framework\",\n                    \"SPY penetrations typically $0.50-$1.50, QQQ $0.75-$2.25 beyond key levels\",\n                    \"Volume spikes of 200-500% average confirm institutional participation\",\n                    \"Timing patterns reveal institutional positioning strategies throughout trading day\"\n                ],\n                practicalExercises: [\n                    \"Identify and analyze 5 liquidity sweeps on SPY using the four-phase framework\",\n                    \"Measure penetration distances and volume spikes on recent QQQ sweeps\",\n                    \"Track sweep timing patterns during different market hours for one week\",\n                    \"Create alerts for high-probability sweep levels based on previous day extremes\"\n                ],\n                quiz: [\n                    {\n                        question: \"What should you do immediately after identifying a liquidity sweep?\",\n                        options: [\n                            \"Enter a trade in the opposite direction\",\n                            \"Enter a trade in the same direction\",\n                            \"Wait for confirmation signals\",\n                            \"Close all existing positions\"\n                        ],\n                        correct: 2,\n                        explanation: \"Never trade immediately after a liquidity sweep. Always wait for confirmation signals like structure breaks, volume confirmation, or candlestick patterns.\"\n                    },\n                    {\n                        question: \"Which scenario is more common after a liquidity sweep?\",\n                        options: [\n                            \"Continuation breakouts\",\n                            \"Reversal setups\",\n                            \"Sideways consolidation\",\n                            \"Gap formations\"\n                        ],\n                        correct: 1,\n                        explanation: \"Reversal setups are more common after liquidity sweeps because most sweeps are designed to grab stops and then reverse, not to signal genuine breakouts.\"\n                    },\n                    {\n                        question: \"What is the best confirmation for a liquidity sweep reversal?\",\n                        options: [\n                            \"A single large volume bar\",\n                            \"Price returning to the sweep level\",\n                            \"Market structure break in the opposite direction\",\n                            \"A gap in the opposite direction\"\n                        ],\n                        correct: 2,\n                        explanation: \"A market structure break (like breaking a swing low after a high sweep) provides the strongest confirmation that the reversal is genuine and not just a temporary pullback.\"\n                    }\n                ]\n            },\n            {\n                id: 3,\n                title: \"Zone Validation Techniques\",\n                description: \"How to validate the strength and reliability of your zones\",\n                content: \"Not all zones are created equal. Learn to identify the strongest...\",\n                type: \"practical\",\n                duration: \"12 min\",\n                keyPoints: [\n                    \"Volume confirmation at zones\",\n                    \"Multiple timeframe validation\",\n                    \"Age and frequency of tests\"\n                ]\n            },\n            {\n                id: 4,\n                title: \"Interactive Zone Drawing Exercise\",\n                description: \"Practice identifying and drawing zones on real SPY/QQQ charts\",\n                content: \"Apply your knowledge with guided practice on live market examples\",\n                type: \"interactive\",\n                duration: \"8 min\",\n                keyPoints: [\n                    \"Real-time chart analysis\",\n                    \"Immediate feedback on zone placement\",\n                    \"Common mistakes to avoid\"\n                ]\n            }\n        ]\n    },\n    {\n        id: 2,\n        title: \"Fair Value Gaps (FVGs) Mastery\",\n        description: \"Master Fair Value Gaps - the institutional footprints left by rapid price movements and how to trade them for consistent profits\",\n        icon: \"BarChart3\",\n        color: \"from-green-500 to-green-600\",\n        estimatedTime: \"80 minutes\",\n        difficulty: \"Intermediate\",\n        prerequisites: \"Understanding of liquidity sweeps and market structure\",\n        learningObjectives: [\n            \"Identify and mark Fair Value Gaps with 95% accuracy\",\n            \"Understand the difference between HTF and LTF FVGs\",\n            \"Trade FVG fills and rejections for high-probability setups\",\n            \"Master Inversion Fair Value Gaps (IFVGs) for advanced entries\",\n            \"Combine FVGs with liquidity sweeps for confluence trading\"\n        ],\n        lessons: [\n            {\n                id: 1,\n                title: \"Fair Value Gap Theory & Formation\",\n                description: \"Deep dive into FVG formation, institutional causes, and market inefficiency concepts\",\n                content: \"\\n# Fair Value Gaps: Institutional Footprints in Price Action\\n\\nA Fair Value Gap (FVG) is a price range on a chart where an inefficient move occurred – essentially, a section where little or no trading took place. These gaps represent areas where fair value may have temporarily changed, and markets tend to revert to fill these inefficiencies over time.\\n\\n## What is a Fair Value Gap?\\n\\n**Definition:**\\nA Fair Value Gap appears within a three-candle sequence where one large momentum candle creates a \\\"void\\\" between the wick of the first candle and the wick of the third candle. The second candle (the big move) is so large that the third candle's low (in an up move) is still above the first candle's high, leaving a gap in between.\\n\\n**Key Concept:**\\nThis gap represents a price area where fair value may have temporarily changed – price zoomed in one direction without adequate two-way trading at those levels.\\n\\n## The Three-Candle Rule\\n\\n### Bullish FVG Formation:\\n1. **Candle 1**: Creates a high at a certain level\\n2. **Candle 2**: Large bullish candle that gaps up significantly\\n3. **Candle 3**: Low is still above Candle 1's high\\n4. **Result**: Gap between Candle 1 high and Candle 3 low = Bullish FVG\\n\\n### Bearish FVG Formation:\\n1. **Candle 1**: Creates a low at a certain level\\n2. **Candle 2**: Large bearish candle that gaps down significantly\\n3. **Candle 3**: High is still below Candle 1's low\\n4. **Result**: Gap between Candle 1 low and Candle 3 high = Bearish FVG\\n\\n## Why Do FVGs Matter?\\n\\n### Market Inefficiency Theory\\n- FVGs highlight areas where price moved too fast\\n- Represent zones with insufficient two-way trading\\n- Markets have \\\"memory\\\" of unfilled orders in these ranges\\n- Price often returns to rebalance these inefficiencies\\n\\n### Institutional Perspective\\n- Large orders couldn't be filled during rapid moves\\n- Institutions may have unfilled orders in FVG zones\\n- Smart money often waits for price to return to these levels\\n- FVGs act like magnets for future price action\\n\\n## Types of Fair Value Gaps\\n\\n### 1. Bullish FVG (Buy Side Imbalance)\\n- **Formation**: Left by strong upward movement\\n- **Expectation**: Acts as support when price returns\\n- **Trading**: Look for buying opportunities on first touch\\n- **Invalidation**: Price closes below the gap\\n\\n### 2. Bearish FVG (Sell Side Imbalance)\\n- **Formation**: Left by strong downward movement\\n- **Expectation**: Acts as resistance when price returns\\n- **Trading**: Look for selling opportunities on first touch\\n- **Invalidation**: Price closes above the gap\\n\\n## SPY/QQQ FVG Characteristics\\n\\n### SPY FVG Patterns:\\n- Often form during earnings reactions\\n- News-driven gaps create large FVGs\\n- Options expiration can trigger FVG formation\\n- Market open gaps frequently leave FVGs\\n\\n### QQQ FVG Patterns:\\n- Tech sector news creates significant FVGs\\n- Higher volatility = larger gap formations\\n- After-hours trading often leaves gaps\\n- Correlation with NASDAQ futures gaps\\n\\n## FVG Validation Criteria\\n\\n### Strong FVGs Have:\\n1. **Clean Formation**: Clear three-candle pattern\\n2. **Significant Size**: Gap represents meaningful price range\\n3. **Volume Context**: High volume on the gap-creating candle\\n4. **Timeframe Relevance**: Higher timeframes = stronger FVGs\\n\\n### Weak FVGs Show:\\n- Overlapping wicks between candles\\n- Very small gap size\\n- Low volume on formation\\n- Multiple gaps in same area\\n\\n## Real-World FVG Example\\n\\n**SPY Bullish FVG Formation:**\\n1. **9:30 AM**: SPY opens at $450, creates high at $450.50\\n2. **9:31 AM**: Strong buying pushes SPY from $450.75 to $452.25\\n3. **9:32 AM**: Pullback finds support at $451.00\\n4. **Result**: Bullish FVG from $450.50 to $451.00\\n\\n**Expected Behavior:**\\n- Price may return to $450.50-$451.00 zone\\n- First touch often provides buying opportunity\\n- Zone acts as support for future moves\\n- Invalidated if price closes below $450.50\\n        \",\n                type: \"theory\",\n                duration: \"22 min\",\n                keyPoints: [\n                    \"Fair Value Gaps represent price inefficiencies where insufficient two-way trading occurred\",\n                    \"FVGs form through the three-candle rule with clear gap between first and third candle wicks\",\n                    \"Bullish FVGs act as support zones, bearish FVGs act as resistance zones\",\n                    \"Higher timeframe FVGs are more significant and reliable than lower timeframe gaps\",\n                    \"SPY/QQQ FVGs often form during news events, market opens, and earnings reactions\"\n                ],\n                practicalExercises: [\n                    \"Identify 5 Fair Value Gaps on SPY daily chart using the three-candle rule\",\n                    \"Mark bullish and bearish FVGs with different colors on your charts\",\n                    \"Observe how price reacts when returning to previously identified FVG zones\"\n                ],\n                quiz: [\n                    {\n                        question: \"What defines a valid Fair Value Gap formation?\",\n                        options: [\n                            \"Any gap between two candles\",\n                            \"A three-candle pattern where the middle candle creates a gap between the first and third candle wicks\",\n                            \"A gap that forms at market open\",\n                            \"Any price movement with high volume\"\n                        ],\n                        correct: 1,\n                        explanation: \"A valid FVG requires a three-candle pattern where the large middle candle creates a clear gap between the first candle's high/low and the third candle's low/high.\"\n                    },\n                    {\n                        question: \"How should a bullish FVG behave when price returns to it?\",\n                        options: [\n                            \"Price should break through immediately\",\n                            \"Price should act as resistance\",\n                            \"Price should find support and potentially bounce\",\n                            \"Price should create more gaps\"\n                        ],\n                        correct: 2,\n                        explanation: \"A bullish FVG should act as a support zone when price returns to it, as the gap represents an area where buyers may step in to fill the inefficiency.\"\n                    },\n                    {\n                        question: \"When is a Fair Value Gap considered invalidated?\",\n                        options: [\n                            \"After one touch\",\n                            \"When price closes through the gap completely\",\n                            \"After 24 hours\",\n                            \"When volume decreases\"\n                        ],\n                        correct: 1,\n                        explanation: \"An FVG is invalidated when price closes completely through the gap, indicating the inefficiency has been filled and the zone no longer holds significance.\"\n                    }\n                ]\n            },\n            {\n                id: 2,\n                title: \"Higher Timeframe FVGs & Multi-Timeframe Analysis\",\n                description: \"Master the power of Higher Timeframe Fair Value Gaps and learn to combine multiple timeframes for precision entries\",\n                content: \"\\n# Higher Timeframe FVGs: The Institutional Magnets\\n\\nHigher timeframe Fair Value Gaps are among the most powerful tools in a professional trader's arsenal. These gaps act like magnets, drawing price back to fill inefficiencies left by rapid institutional moves.\\n\\n## Why Higher Timeframes Matter\\n\\n### Significance Hierarchy:\\n- **Weekly FVGs**: Extremely powerful, may take months to fill\\n- **Daily FVGs**: Very significant, often filled within days/weeks\\n- **4-Hour FVGs**: Strong levels, usually filled within days\\n- **1-Hour FVGs**: Moderate significance, filled within hours/days\\n- **15-Min FVGs**: Lower significance, often filled quickly\\n\\n### Volume and Participation:\\nHigher timeframe moves involve:\\n- More institutional participation\\n- Larger order sizes\\n- Greater market impact\\n- Broader market awareness\\n- Stronger magnetic effect\\n\\n## HTF FVG vs LTF FVG Comparison\\n\\n### Higher Timeframe FVGs (Daily+):\\n**Advantages:**\\n- Higher probability of being filled\\n- Stronger support/resistance when reached\\n- Better risk/reward opportunities\\n- Less noise and false signals\\n- Institutional relevance\\n\\n**Characteristics:**\\n- Take longer to reach\\n- Provide major turning points\\n- Often align with other key levels\\n- Create significant price reactions\\n\\n### Lower Timeframe FVGs (1H and below):\\n**Advantages:**\\n- More frequent opportunities\\n- Faster fills and reactions\\n- Good for scalping strategies\\n- Quick feedback on trades\\n\\n**Disadvantages:**\\n- Higher noise ratio\\n- More false signals\\n- Weaker reactions\\n- Less institutional relevance\\n\\n## Multi-Timeframe FVG Analysis\\n\\n### The Professional Approach:\\n1. **Mark HTF FVGs first** (Daily, 4H, 1H)\\n2. **Use LTF for entry timing** (15M, 5M)\\n3. **Combine with other confluences**\\n4. **Prioritize HTF over LTF**\\n\\n### Confluence Stacking:\\n**High-Probability Setup:**\\n- Daily FVG zone\\n- + Previous day high/low\\n- + Volume profile level\\n- + Liquidity sweep area\\n- = Maximum confluence\\n\\n## SPY/QQQ HTF FVG Patterns\\n\\n### SPY Daily FVG Characteristics:\\n- **Formation**: Often during earnings, Fed announcements, major news\\n- **Size**: Typically $2-8 gaps on daily charts\\n- **Fill Rate**: 85-90% eventually get filled\\n- **Timeframe**: Usually filled within 1-4 weeks\\n- **Reaction**: Strong bounces/rejections on first touch\\n\\n### QQQ Daily FVG Characteristics:\\n- **Formation**: Tech earnings, guidance changes, sector rotation\\n- **Size**: Typically $3-12 gaps due to higher volatility\\n- **Fill Rate**: 80-85% eventually get filled\\n- **Timeframe**: May take longer due to trend strength\\n- **Reaction**: More volatile reactions, wider zones needed\\n\\n## Trading HTF FVGs: The Professional Method\\n\\n### Step 1: Identification\\n- Scan daily/4H charts for clean FVG formations\\n- Mark gap boundaries clearly\\n- Note the context (news, earnings, etc.)\\n- Assess gap size and significance\\n\\n### Step 2: Patience\\n- Wait for price to approach the HTF FVG\\n- Don't chase - let the gap come to you\\n- Monitor lower timeframes for entry signals\\n- Prepare for potential strong reactions\\n\\n### Step 3: Entry Timing\\n- Use 15M/5M charts for precise entries\\n- Look for additional confirmations:\\n  - Lower timeframe structure breaks\\n  - Volume increases\\n  - Candlestick patterns\\n  - Momentum divergences\\n\\n### Step 4: Risk Management\\n- Stop loss beyond the FVG zone\\n- Take profits at logical levels\\n- Trail stops as trade develops\\n- Respect the power of HTF levels\\n\\n## Real-World HTF FVG Example\\n\\n**SPY Daily Bullish FVG Setup:**\\n- **Formation**: Fed announcement creates gap from $445-$448\\n- **Wait Period**: 2 weeks for price to return\\n- **Entry Signal**: 15M bullish engulfing at $446 (within FVG)\\n- **Confirmation**: Volume spike + break of 15M structure\\n- **Result**: Bounce to $452 for 1.3% profit\\n- **Risk**: Stop at $444 (below FVG) for 0.4% risk\\n- **R:R Ratio**: 3.25:1\\n\\n## Key HTF FVG Rules\\n\\n1. **Higher timeframe always wins** - HTF FVG overrides LTF signals\\n2. **First touch is strongest** - Best reactions occur on initial contact\\n3. **Partial fills are common** - Price may only fill 50-70% of gap\\n4. **Context matters** - Consider overall market trend and sentiment\\n5. **Patience pays** - Wait for proper setups, don't force trades\\n        \",\n                type: \"practical\",\n                duration: \"25 min\",\n                keyPoints: [\n                    \"Perfect liquidity sweeps follow a 4-phase pattern: approach, penetration, reversal, follow-through\",\n                    \"SPY sweeps typically extend 0.1-0.3% beyond levels with 20-50% above average volume\",\n                    \"Visual recognition includes wick formations, volume spikes, and immediate reversals\",\n                    \"Time-based patterns show highest probability during market open and close\",\n                    \"Multi-timeframe analysis provides confirmation and precise entry timing\"\n                ],\n                practicalExercises: [\n                    \"Identify and analyze 3 historical liquidity sweeps on SPY using the 4-phase pattern\",\n                    \"Mark 5 potential liquidity levels on current QQQ chart and monitor for sweep patterns\",\n                    \"Practice distinguishing between true sweeps and genuine breakouts using volume analysis\",\n                    \"Create alerts for price approaching identified liquidity levels for real-time practice\"\n                ],\n                quiz: [\n                    {\n                        question: \"What is the typical penetration distance for SPY liquidity sweeps?\",\n                        options: [\n                            \"1-2% beyond the level\",\n                            \"0.1-0.3% beyond the level\",\n                            \"5-10% beyond the level\",\n                            \"Exactly to the level\"\n                        ],\n                        correct: 1,\n                        explanation: \"SPY liquidity sweeps typically penetrate 0.1-0.3% beyond key levels - enough to trigger stops but not so much as to indicate a genuine breakout.\"\n                    },\n                    {\n                        question: \"Which phase of a liquidity sweep shows the highest volume?\",\n                        options: [\n                            \"The approach phase\",\n                            \"The penetration phase\",\n                            \"The reversal phase\",\n                            \"The follow-through phase\"\n                        ],\n                        correct: 2,\n                        explanation: \"The reversal phase typically shows the highest volume as institutional orders enter the market after stops are triggered.\"\n                    }\n                ]\n            },\n            {\n                id: 3,\n                title: \"Trading Liquidity Sweeps\",\n                description: \"How to position yourself to profit from sweep reversals\",\n                content: \"Once you identify a liquidity sweep, the next step is positioning...\",\n                type: \"strategy\",\n                duration: \"18 min\",\n                keyPoints: [\n                    \"Entry timing after sweep completion\",\n                    \"Stop loss placement strategies\",\n                    \"Target setting for sweep trades\"\n                ]\n            },\n            {\n                id: 4,\n                title: \"Sweep Analysis Workshop\",\n                description: \"Analyze real SPY/QQQ liquidity sweeps with expert commentary\",\n                content: \"Review historical examples of successful sweep trades\",\n                type: \"interactive\",\n                duration: \"7 min\",\n                keyPoints: [\n                    \"Case study analysis\",\n                    \"Pattern recognition practice\",\n                    \"Risk management examples\"\n                ]\n            }\n        ]\n    },\n    {\n        id: 3,\n        title: \"Confirmation Stacking & Multi-Factor Analysis\",\n        description: \"Master the art of stacking multiple confirmations for high-probability trades. Learn to combine price action, volume, and technical analysis for professional-level precision.\",\n        icon: \"Layers\",\n        color: \"from-purple-500 to-purple-600\",\n        estimatedTime: \"85 minutes\",\n        difficulty: \"Advanced\",\n        prerequisites: \"Understanding of liquidity sweeps and Fair Value Gaps\",\n        learningObjectives: [\n            \"Stack 3+ confirmations for every trade setup\",\n            \"Master market structure confirmations (BOS/CHoCH)\",\n            \"Integrate volume profile and order flow analysis\",\n            \"Combine multiple timeframes for precision entries\",\n            \"Develop a systematic approach to trade validation\"\n        ],\n        lessons: [\n            {\n                id: 1,\n                title: \"Confirmation Stacking Fundamentals\",\n                description: \"Learn the professional approach to stacking multiple confirmations for high-probability trade setups\",\n                content: '\\n# Confirmation Stacking: The Professional Edge\\n\\nEven when you have a strong level or setup in mind (be it a liquidity sweep or an FVG), jumping in without confirmation can be risky. Confirmation stacking means waiting for multiple signals to line up in your favor before committing to a trade.\\n\\n## The Philosophy of Confluence\\n\\n**Core Principle:**\\nRather than relying on a single indicator or one pattern, you look for an agreement among several independent clues – what traders often call confluence. The idea is to filter out low-quality setups and only act when many things point to the same conclusion.\\n\\n**Think of it this way:**\\nEach confirmation is like a piece of a puzzle. One piece alone doesn\\'t show the whole picture, but when several pieces fit together, you have a clearer image of where price might go.\\n\\n## The Five Pillars of Confirmation\\n\\n### 1. Market Structure & Price Action\\nThis refers to analyzing how price swings (highs and lows) are behaving to confirm a trend change or continuation.\\n\\n**Break of Structure (BOS):**\\n- Price takes out a significant previous high or low in the direction of a trend\\n- Confirms that trend\\'s strength\\n- Shows institutional participation\\n\\n**Change of Character (CHoCH):**\\n- Early sign of a possible trend reversal\\n- First break of a minor swing level against the trend\\n- Indicates potential shift in market sentiment\\n\\n**Example:** If QQQ has been making higher highs and higher lows (uptrend) and then suddenly makes a lower low, that\\'s a bearish CHoCH signaling the uptrend may be done.\\n\\n### 2. Volume Profile & Range Context\\nVolume Profile shows how volume has been distributed at each price, giving insight into what prices the market deems \"fair\" vs \"extreme.\"\\n\\n**Key Elements:**\\n- **Point of Control (POC):** Price with highest traded volume\\n- **Value Area (VA):** Price range where ~70% of volume occurred\\n- **Value Area High/Low (VAH/VAL):** Boundaries of fair value\\n\\n**Application:** If SPY rejects from yesterday\\'s Value Area High after a liquidity sweep, that\\'s confluence supporting a reversal trade.\\n\\n### 3. Order Flow Tools (Advanced)\\nReal-time confirmation of what\\'s happening under the hood through futures DOM, time and sales, or heatmap platforms.\\n\\n**Signals to Watch:**\\n- Absorption of selling/buying at key levels\\n- Cumulative volume delta divergences\\n- Large limit orders on the book\\n- Aggressive vs passive order flow\\n\\n### 4. Indicators & Overlays\\nTraditional technical indicators can be part of your confirmation stack, especially ones that measure trend or mean reversion.\\n\\n**VWAP (Volume Weighted Average Price):**\\n- Intraday equilibrium level\\n- Reclaiming VWAP after a sweep adds confidence\\n- Acts as dynamic support/resistance\\n\\n**Moving Averages:**\\n- 21 EMA, 50 EMA for trend confirmation\\n- Dynamic support on pullbacks\\n- Confluence with other levels\\n\\n### 5. Liquidity & HTF Levels\\nCombining liquidity sweeps and HTF FVGs as part of confirmation checklist.\\n\\n**High-Probability Setup:**\\n- Liquidity sweep at HTF FVG\\n- + Market structure confirmation\\n- + Volume profile level\\n- + VWAP reclaim\\n- = Maximum confluence\\n\\n## The Professional Confirmation Checklist\\n\\n### Minimum Requirements:\\n**For Entry:** At least 2-3 solid confirmations\\n**For High-Conviction Trades:** 4+ confirmations aligned\\n\\n### Example Checklist:\\n1. ✓ Liquidity sweep occurred\\n2. ✓ Price action confirmation (CHoCH/BOS)\\n3. ✓ Volume profile level confluence\\n4. ✓ HTF FVG zone\\n5. ✓ VWAP reclaim/rejection\\n\\n## Real-World Confirmation Stacking Example\\n\\n### SPY Bearish Setup:\\n**Setup:** SPY approaches yesterday\\'s high at $450\\n\\n**Confirmations:**\\n1. **Liquidity Sweep:** Price hits $450.50, sweeps stops\\n2. **HTF Level:** Daily bearish FVG zone at $450-451\\n3. **Price Action:** 5-minute bearish engulfing + CHoCH\\n4. **Volume Profile:** Rejection from yesterday\\'s VAH\\n5. **Volume:** Spike on sweep, sustained on reversal\\n\\n**Entry:** Short at $449.50 after all confirmations align\\n**Stop:** $451 (above sweep high)\\n**Target:** $445 (previous support)\\n**Result:** 1% profit with 0.3% risk = 3.3:1 R/R\\n\\n## Avoiding Analysis Paralysis\\n\\n### Balance is Key:\\n- Too few confirmations = low probability\\n- Too many confirmations = missed opportunities\\n- Sweet spot: 2-3 strong confirmations\\n\\n### Weighting Confirmations:\\n**Primary (Must Have):**\\n- Price action signal (structure break)\\n- Key level confluence (liquidity/FVG)\\n\\n**Secondary (Nice to Have):**\\n- Volume confirmation\\n- Indicator alignment\\n- Higher timeframe context\\n\\n## Common Confirmation Mistakes\\n\\n1. **Forcing Confluence:** Seeing confirmations that aren\\'t really there\\n2. **Over-Analysis:** Requiring too many signals\\n3. **Ignoring Context:** Not considering overall market environment\\n4. **Static Thinking:** Not adapting to changing market conditions\\n5. **Confirmation Bias:** Only seeing signals that support your bias\\n- Unfilled orders create demand/supply\\n- Technical traders target gap fills\\n- Self-fulfilling prophecy effect\\n\\n## FVG vs. Regular Gaps\\n\\n### Fair Value Gaps:\\n- Formed by 3-candle pattern\\n- Represent order flow imbalance\\n- High probability of fill (70-80%)\\n- Can be traded in both directions\\n- Show institutional activity\\n\\n### Regular Price Gaps:\\n- Formed between sessions (overnight)\\n- Caused by news or events\\n- Lower probability of fill (40-60%)\\n- Often indicate trend continuation\\n- May not represent institutional flow\\n\\n## Psychological Aspects of FVG Trading\\n\\n### Institutional Perspective:\\n- \"We moved price too fast\"\\n- \"Need to fill remaining orders\"\\n- \"Better prices available in the gap\"\\n- \"Risk management requires rebalancing\"\\n\\n### Retail Perspective:\\n- \"Price gapped away from me\"\\n- \"I missed the move\"\\n- \"Will it come back?\"\\n- \"Should I chase or wait?\"\\n\\n## Real-World FVG Examples\\n\\n### Example 1: SPY Bullish FVG\\n**Setup:** Fed announcement creates buying surge\\n**Formation:** 3-candle bullish FVG at $445-$447\\n**Fill:** Price returns to gap 5 days later\\n**Outcome:** Perfect bounce from gap support\\n\\n### Example 2: QQQ Bearish FVG\\n**Setup:** Tech earnings disappointment\\n**Formation:** 3-candle bearish FVG at $380-$382\\n**Fill:** Price rallies to gap 2 weeks later\\n**Outcome:** Strong resistance at gap level\\n\\n## Advanced FVG Concepts\\n\\n### 1. Nested FVGs\\n- Multiple gaps within larger gaps\\n- Provide multiple trading opportunities\\n- Show sustained institutional activity\\n- Require careful order management\\n\\n### 2. FVG Clusters\\n- Multiple gaps in same price area\\n- Extremely high probability zones\\n- Often mark major support/resistance\\n- Institutional accumulation/distribution areas\\n\\n### 3. Partial vs. Full Fills\\n- **Partial Fill:** Price touches gap but doesn\\'t close it\\n- **Full Fill:** Price completely closes the gap\\n- **Overfill:** Price extends beyond the gap\\n- Each has different trading implications\\n\\n## Common FVG Mistakes\\n\\n1. **Trading Every Gap:** Not all FVGs are equal quality\\n2. **Ignoring Context:** Market structure matters\\n3. **Poor Risk Management:** Gaps can extend before filling\\n4. **Wrong Timeframe:** Match timeframe to trading style\\n5. **Emotional Trading:** FOMO on gap formations\\n        ',\n                type: \"theory\",\n                duration: \"25 min\",\n                keyPoints: [\n                    \"Fair Value Gaps represent institutional order flow imbalances created by overwhelming buying/selling pressure\",\n                    \"FVGs require exactly 3 candles with no overlap between outer candles' high/low\",\n                    \"SPY FVGs typically range 0.2-0.8% while QQQ ranges 0.3-1.2% due to higher volatility\",\n                    \"70-80% of FVGs get filled within 5-20 sessions as markets seek price efficiency\",\n                    \"Inversion FVGs become powerful support/resistance after being filled\"\n                ],\n                practicalExercises: [\n                    \"Identify 5 bullish and 5 bearish FVGs on SPY 30-minute chart from last month\",\n                    \"Measure the size of each FVG as percentage of price and compare to typical ranges\",\n                    \"Track which FVGs got filled and calculate the fill rate for your sample\",\n                    \"Practice distinguishing between FVGs and regular overnight gaps\"\n                ],\n                quiz: [\n                    {\n                        question: \"How many candles are required to form a Fair Value Gap?\",\n                        options: [\n                            \"2 candles\",\n                            \"3 candles\",\n                            \"4 candles\",\n                            \"5 candles\"\n                        ],\n                        correct: 1,\n                        explanation: \"A Fair Value Gap requires exactly 3 consecutive candles, with the middle candle creating the imbalance and no overlap between the outer candles.\"\n                    },\n                    {\n                        question: \"What percentage of Fair Value Gaps typically get filled?\",\n                        options: [\n                            \"30-40%\",\n                            \"50-60%\",\n                            \"70-80%\",\n                            \"90-100%\"\n                        ],\n                        correct: 2,\n                        explanation: \"Approximately 70-80% of Fair Value Gaps get filled as markets naturally seek price efficiency and institutional orders get completed.\"\n                    }\n                ]\n            },\n            {\n                id: 2,\n                title: \"FVG Classification System\",\n                description: \"Learn to classify FVGs by strength and probability\",\n                content: \"Not all FVGs are equal. Learn the classification system...\",\n                type: \"practical\",\n                duration: \"15 min\",\n                keyPoints: [\n                    \"High probability vs low probability gaps\",\n                    \"Size and context importance\",\n                    \"Multiple timeframe FVG analysis\"\n                ]\n            },\n            {\n                id: 3,\n                title: \"Trading FVG Fills\",\n                description: \"Strategies for trading when price returns to fill gaps\",\n                content: \"FVG fills often provide excellent trading opportunities...\",\n                type: \"strategy\",\n                duration: \"16 min\",\n                keyPoints: [\n                    \"Partial vs full gap fills\",\n                    \"Entry and exit strategies\",\n                    \"Combining FVGs with other confluences\"\n                ]\n            },\n            {\n                id: 4,\n                title: \"FVG Recognition Challenge\",\n                description: \"Test your ability to spot and classify FVGs in real-time\",\n                content: \"Interactive challenge to identify FVGs on live charts\",\n                type: \"interactive\",\n                duration: \"7 min\",\n                keyPoints: [\n                    \"Speed recognition drills\",\n                    \"Classification accuracy\",\n                    \"Real-time decision making\"\n                ]\n            }\n        ]\n    },\n    {\n        id: 4,\n        title: \"Volume Analysis & Confirmation\",\n        description: \"Use volume analysis to confirm your price action signals\",\n        icon: \"Activity\",\n        color: \"from-orange-500 to-orange-600\",\n        estimatedTime: \"40 minutes\",\n        difficulty: \"Beginner\",\n        lessons: [\n            {\n                id: 1,\n                title: \"Volume Fundamentals\",\n                description: \"Understanding volume and its relationship to price movement\",\n                content: \"Volume is the fuel that drives price movement...\",\n                type: \"theory\",\n                duration: \"10 min\",\n                keyPoints: [\n                    \"Volume precedes price\",\n                    \"Accumulation vs distribution patterns\",\n                    \"Volume profile concepts\"\n                ]\n            },\n            {\n                id: 2,\n                title: \"Volume at Key Levels\",\n                description: \"Analyzing volume behavior at support/resistance zones\",\n                content: \"How volume behaves at key levels tells us about market sentiment...\",\n                type: \"practical\",\n                duration: \"15 min\",\n                keyPoints: [\n                    \"Rising volume on approach to zones\",\n                    \"Fading volume and false breakouts\",\n                    \"Climactic volume patterns\"\n                ]\n            },\n            {\n                id: 3,\n                title: \"Volume Confirmation Strategies\",\n                description: \"Using volume to confirm your trading signals\",\n                content: \"Volume confirmation can significantly improve trade success rates...\",\n                type: \"strategy\",\n                duration: \"12 min\",\n                keyPoints: [\n                    \"Volume divergence signals\",\n                    \"Confirmation vs contradiction\",\n                    \"Multiple timeframe volume analysis\"\n                ]\n            },\n            {\n                id: 4,\n                title: \"Volume Analysis Practice\",\n                description: \"Practice reading volume patterns on SPY/QQQ charts\",\n                content: \"Hands-on practice with volume analysis techniques\",\n                type: \"interactive\",\n                duration: \"3 min\",\n                keyPoints: [\n                    \"Pattern recognition\",\n                    \"Signal confirmation practice\",\n                    \"Real-world application\"\n                ]\n            }\n        ]\n    },\n    {\n        id: 5,\n        title: \"Confirmation Stacking\",\n        description: \"Learn to stack multiple confirmations for high-probability trades\",\n        icon: \"Layers\",\n        color: \"from-red-500 to-red-600\",\n        estimatedTime: \"55 minutes\",\n        difficulty: \"Advanced\",\n        lessons: [\n            {\n                id: 1,\n                title: \"The Stacking Methodology\",\n                description: \"Understanding the concept of confirmation stacking\",\n                content: \"Confirmation stacking involves combining multiple technical signals...\",\n                type: \"theory\",\n                duration: \"12 min\",\n                keyPoints: [\n                    \"Quality over quantity in confirmations\",\n                    \"Weighted confirmation systems\",\n                    \"Avoiding analysis paralysis\"\n                ]\n            },\n            {\n                id: 2,\n                title: \"Building Your Stack\",\n                description: \"How to systematically build confirmation stacks\",\n                content: \"Learn the systematic approach to building robust confirmation stacks...\",\n                type: \"practical\",\n                duration: \"18 min\",\n                keyPoints: [\n                    \"Primary vs secondary confirmations\",\n                    \"Timeframe hierarchy\",\n                    \"Confluence zone identification\"\n                ]\n            },\n            {\n                id: 3,\n                title: \"Advanced Stacking Techniques\",\n                description: \"Professional-level confirmation stacking strategies\",\n                content: \"Advanced techniques used by professional traders...\",\n                type: \"strategy\",\n                duration: \"20 min\",\n                keyPoints: [\n                    \"Multi-timeframe stacking\",\n                    \"Intermarket confirmations\",\n                    \"Sentiment-based confirmations\"\n                ]\n            },\n            {\n                id: 4,\n                title: \"Stacking Mastery Challenge\",\n                description: \"Put your stacking skills to the test with complex scenarios\",\n                content: \"Advanced challenge scenarios to test your mastery\",\n                type: \"interactive\",\n                duration: \"5 min\",\n                keyPoints: [\n                    \"Complex scenario analysis\",\n                    \"Decision-making under pressure\",\n                    \"Professional-level execution\"\n                ]\n            }\n        ]\n    },\n    {\n        id: 6,\n        title: \"Risk Management & Psychology\",\n        description: \"Master the mental game and risk management for consistent profits\",\n        icon: \"Shield\",\n        color: \"from-indigo-500 to-indigo-600\",\n        estimatedTime: \"45 minutes\",\n        difficulty: \"Intermediate\",\n        lessons: [\n            {\n                id: 1,\n                title: \"Position Sizing Fundamentals\",\n                description: \"Calculate optimal position sizes for your account\",\n                content: \"Proper position sizing is the foundation of risk management...\",\n                type: \"theory\",\n                duration: \"12 min\",\n                keyPoints: [\n                    \"Risk percentage rules\",\n                    \"Account size considerations\",\n                    \"Volatility-adjusted sizing\"\n                ]\n            },\n            {\n                id: 2,\n                title: \"Stop Loss Strategies\",\n                description: \"Advanced stop loss placement and management techniques\",\n                content: \"Stop losses are your insurance policy in trading...\",\n                type: \"practical\",\n                duration: \"15 min\",\n                keyPoints: [\n                    \"Technical vs percentage stops\",\n                    \"Trailing stop strategies\",\n                    \"Stop loss psychology\"\n                ]\n            },\n            {\n                id: 3,\n                title: \"Trading Psychology Mastery\",\n                description: \"Develop the mental discipline required for consistent trading\",\n                content: \"Trading psychology often determines success more than technical skills...\",\n                type: \"strategy\",\n                duration: \"15 min\",\n                keyPoints: [\n                    \"Emotional regulation techniques\",\n                    \"Dealing with losses\",\n                    \"Maintaining discipline\"\n                ]\n            },\n            {\n                id: 4,\n                title: \"Psychology Assessment\",\n                description: \"Evaluate your trading psychology and identify areas for improvement\",\n                content: \"Self-assessment tools for trading psychology\",\n                type: \"interactive\",\n                duration: \"3 min\",\n                keyPoints: [\n                    \"Psychological profiling\",\n                    \"Weakness identification\",\n                    \"Improvement planning\"\n                ]\n            }\n        ]\n    },\n    {\n        id: 7,\n        title: \"Advanced Pattern Recognition\",\n        description: \"Identify complex patterns and market structures for professional-level trading\",\n        icon: \"Eye\",\n        color: \"from-teal-500 to-teal-600\",\n        estimatedTime: \"65 minutes\",\n        difficulty: \"Advanced\",\n        lessons: [\n            {\n                id: 1,\n                title: \"Complex Pattern Structures\",\n                description: \"Understanding advanced chart patterns and their implications\",\n                content: \"Advanced patterns often provide the highest probability setups...\",\n                type: \"theory\",\n                duration: \"18 min\",\n                keyPoints: [\n                    \"Multi-timeframe pattern analysis\",\n                    \"Pattern failure and continuation\",\n                    \"Context-dependent patterns\"\n                ]\n            },\n            {\n                id: 2,\n                title: \"Market Structure Shifts\",\n                description: \"Identifying when market structure changes and how to adapt\",\n                content: \"Market structure shifts signal major changes in sentiment...\",\n                type: \"practical\",\n                duration: \"20 min\",\n                keyPoints: [\n                    \"Break of structure signals\",\n                    \"Change of character patterns\",\n                    \"Trend transition identification\"\n                ]\n            },\n            {\n                id: 3,\n                title: \"Professional Pattern Trading\",\n                description: \"How professionals trade complex patterns for maximum profit\",\n                content: \"Professional trading strategies for advanced patterns...\",\n                type: \"strategy\",\n                duration: \"22 min\",\n                keyPoints: [\n                    \"Entry and exit optimization\",\n                    \"Risk-reward maximization\",\n                    \"Pattern-specific strategies\"\n                ]\n            },\n            {\n                id: 4,\n                title: \"Pattern Mastery Exam\",\n                description: \"Final examination of your pattern recognition abilities\",\n                content: \"Comprehensive test of all pattern recognition skills\",\n                type: \"interactive\",\n                duration: \"5 min\",\n                keyPoints: [\n                    \"Comprehensive pattern test\",\n                    \"Speed and accuracy assessment\",\n                    \"Professional certification\"\n                ]\n            }\n        ]\n    }\n];\nconst COURSE_ACHIEVEMENTS = [\n    {\n        id: \"first_lesson\",\n        title: \"Getting Started\",\n        description: \"Complete your first lesson\",\n        icon: \"Play\",\n        points: 10\n    },\n    {\n        id: \"first_module\",\n        title: \"Module Master\",\n        description: \"Complete your first module\",\n        icon: \"Award\",\n        points: 50\n    },\n    {\n        id: \"quiz_master\",\n        title: \"Quiz Master\",\n        description: \"Score 90% or higher on 5 quizzes\",\n        icon: \"Brain\",\n        points: 100\n    },\n    {\n        id: \"speed_learner\",\n        title: \"Speed Learner\",\n        description: \"Complete 3 lessons in one day\",\n        icon: \"Zap\",\n        points: 75\n    },\n    {\n        id: \"course_complete\",\n        title: \"Course Graduate\",\n        description: \"Complete the entire course\",\n        icon: \"GraduationCap\",\n        points: 500\n    }\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/data/courseData.js\n"));

/***/ })

});