"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/data/courseData.js":
/*!********************************!*\
  !*** ./src/data/courseData.js ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   COURSE_ACHIEVEMENTS: function() { return /* binding */ COURSE_ACHIEVEMENTS; },\n/* harmony export */   COURSE_MODULES: function() { return /* binding */ COURSE_MODULES; }\n/* harmony export */ });\n/**\n * Advanced Price Action Course Data\n * Professional SPY/QQQ Trading Course - Liquidity Sweeps, FVGs, and Confirmation Stacking\n * Complete educational content with real trading strategies\n */ const COURSE_MODULES = [\n    {\n        id: 1,\n        title: \"Market Structure & Zone Identification\",\n        description: \"Master the fundamentals of market structure analysis and precise zone identification on SPY/QQQ using 15-30 minute timeframes\",\n        icon: \"Target\",\n        color: \"from-blue-500 to-blue-600\",\n        estimatedTime: \"60 minutes\",\n        difficulty: \"Beginner\",\n        prerequisites: \"Basic understanding of candlestick charts\",\n        learningObjectives: [\n            \"Identify market structure shifts with 95% accuracy\",\n            \"Draw precise support/resistance zones using institutional levels\",\n            \"Recognize trend vs range-bound market conditions\",\n            \"Apply zone validation techniques for high-probability setups\"\n        ],\n        lessons: [\n            {\n                id: 1,\n                title: \"Market Structure Fundamentals\",\n                description: \"Deep dive into market structure analysis - the foundation of professional price action trading\",\n                content: \"\\n# Market Structure: The Foundation of Professional Trading\\n\\nMarket structure is the backbone of all successful price action trading. Unlike retail traders who focus on indicators, institutional traders analyze market structure to identify where smart money is positioned.\\n\\n## What is Market Structure?\\n\\nMarket structure refers to the way price moves and creates patterns that reveal the underlying supply and demand dynamics. It's the language that institutional traders use to communicate their intentions through price action.\\n\\n### Key Components:\\n\\n**1. Swing Highs and Swing Lows**\\n- A swing high is formed when price creates a peak with lower highs on both sides\\n- A swing low is formed when price creates a valley with higher lows on both sides\\n- These points represent areas where institutional orders were executed\\n\\n**2. Trend Identification**\\n- **Uptrend**: Series of higher highs (HH) and higher lows (HL)\\n- **Downtrend**: Series of lower highs (LH) and lower lows (LL)\\n- **Sideways**: Price oscillates between defined levels without clear direction\\n\\n**3. Market Phases**\\n- **Accumulation**: Smart money quietly builds positions\\n- **Markup/Markdown**: Directional movement as institutions move price\\n- **Distribution**: Smart money exits positions to retail traders\\n\\n## Why SPY/QQQ Are Perfect for Structure Analysis\\n\\nSPY and QQQ are ideal instruments for market structure analysis because:\\n- High liquidity ensures clean price action\\n- Institutional participation creates clear structural levels\\n- ETF nature reduces individual stock noise\\n- Strong correlation with overall market sentiment\\n\\n## Practical Application\\n\\nWhen analyzing SPY/QQQ structure:\\n1. Start with higher timeframes (daily/4H) for context\\n2. Use 15-30 minute charts for precise entry timing\\n3. Mark significant swing highs and lows\\n4. Identify the current trend phase\\n5. Look for structure breaks as trend change signals\\n        \",\n                type: \"theory\",\n                duration: \"15 min\",\n                keyPoints: [\n                    \"Market structure reveals institutional order flow and smart money positioning\",\n                    \"Swing highs and lows mark areas of significant institutional activity\",\n                    \"Trend identification through higher highs/lows vs lower highs/lows patterns\",\n                    \"SPY/QQQ provide clean structure due to high institutional participation\",\n                    \"Structure breaks often precede major trend changes and trading opportunities\"\n                ],\n                practicalExercises: [\n                    \"Identify 5 swing highs and 5 swing lows on a SPY 30-minute chart\",\n                    \"Determine current trend direction using structure analysis\",\n                    \"Mark the most recent structure break and analyze the subsequent price action\"\n                ],\n                quiz: [\n                    {\n                        question: \"What defines an uptrend in market structure analysis?\",\n                        options: [\n                            \"Price moving above a moving average\",\n                            \"Series of higher highs and higher lows\",\n                            \"Increasing volume\",\n                            \"Bullish candlestick patterns\"\n                        ],\n                        correct: 1,\n                        explanation: \"An uptrend is defined by a series of higher highs and higher lows, indicating that buyers are willing to pay progressively higher prices.\"\n                    },\n                    {\n                        question: \"Why are SPY and QQQ ideal for market structure analysis?\",\n                        options: [\n                            \"They have low volatility\",\n                            \"They only move during market hours\",\n                            \"High liquidity and institutional participation create clean price action\",\n                            \"They always trend upward\"\n                        ],\n                        correct: 2,\n                        explanation: \"SPY and QQQ's high liquidity and heavy institutional participation result in clean, reliable price action that clearly shows market structure.\"\n                    }\n                ]\n            },\n            {\n                id: 2,\n                title: \"Drawing Support and Resistance Zones\",\n                description: \"Practical techniques for drawing accurate zones on 15-30m charts\",\n                content: \"Support and resistance zones are areas where price has historically shown significant reactions. Unlike single price levels, zones account for the natural volatility and spread in price action. This lesson teaches you the proper techniques for drawing these zones on 15-30 minute timeframes, which provide the optimal balance between detail and broader market context.\",\n                type: \"practical\",\n                duration: \"15 min\",\n                keyPoints: [\n                    \"Zone thickness and significance\",\n                    \"Multiple touches increase zone strength\",\n                    \"Time frame correlation\"\n                ]\n            },\n            {\n                id: 3,\n                title: \"Zone Validation Techniques\",\n                description: \"How to validate the strength and reliability of your zones\",\n                content: \"Not all zones are created equal. Learn to identify the strongest...\",\n                type: \"practical\",\n                duration: \"12 min\",\n                keyPoints: [\n                    \"Volume confirmation at zones\",\n                    \"Multiple timeframe validation\",\n                    \"Age and frequency of tests\"\n                ]\n            },\n            {\n                id: 4,\n                title: \"Interactive Zone Drawing Exercise\",\n                description: \"Practice identifying and drawing zones on real SPY/QQQ charts\",\n                content: \"Apply your knowledge with guided practice on live market examples\",\n                type: \"interactive\",\n                duration: \"8 min\",\n                keyPoints: [\n                    \"Real-time chart analysis\",\n                    \"Immediate feedback on zone placement\",\n                    \"Common mistakes to avoid\"\n                ]\n            }\n        ]\n    },\n    {\n        id: 2,\n        title: \"Liquidity Sweeps & Price Action\",\n        description: \"Identify and capitalize on liquidity sweeps above/below recent highs and lows\",\n        icon: \"TrendingUp\",\n        color: \"from-green-500 to-green-600\",\n        estimatedTime: \"60 minutes\",\n        difficulty: \"Intermediate\",\n        lessons: [\n            {\n                id: 1,\n                title: \"Understanding Liquidity Concepts\",\n                description: \"What is liquidity and why do markets sweep it?\",\n                content: \"Liquidity represents areas where stop losses and pending orders cluster...\",\n                type: \"theory\",\n                duration: \"15 min\",\n                keyPoints: [\n                    \"Stop loss clusters above/below key levels\",\n                    \"Institutional order flow dynamics\",\n                    \"Smart money vs retail positioning\"\n                ]\n            },\n            {\n                id: 2,\n                title: \"Identifying Sweep Patterns\",\n                description: \"Recognize the telltale signs of liquidity sweeps\",\n                content: \"Liquidity sweeps often appear as brief spikes beyond key levels...\",\n                type: \"practical\",\n                duration: \"20 min\",\n                keyPoints: [\n                    \"False breakout characteristics\",\n                    \"Volume patterns during sweeps\",\n                    \"Time-based sweep identification\"\n                ]\n            },\n            {\n                id: 3,\n                title: \"Trading Liquidity Sweeps\",\n                description: \"How to position yourself to profit from sweep reversals\",\n                content: \"Once you identify a liquidity sweep, the next step is positioning...\",\n                type: \"strategy\",\n                duration: \"18 min\",\n                keyPoints: [\n                    \"Entry timing after sweep completion\",\n                    \"Stop loss placement strategies\",\n                    \"Target setting for sweep trades\"\n                ]\n            },\n            {\n                id: 4,\n                title: \"Sweep Analysis Workshop\",\n                description: \"Analyze real SPY/QQQ liquidity sweeps with expert commentary\",\n                content: \"Review historical examples of successful sweep trades\",\n                type: \"interactive\",\n                duration: \"7 min\",\n                keyPoints: [\n                    \"Case study analysis\",\n                    \"Pattern recognition practice\",\n                    \"Risk management examples\"\n                ]\n            }\n        ]\n    },\n    {\n        id: 3,\n        title: \"Fair Value Gaps (FVGs)\",\n        description: \"Master the identification and trading of Fair Value Gaps in price action\",\n        icon: \"BarChart3\",\n        color: \"from-purple-500 to-purple-600\",\n        estimatedTime: \"50 minutes\",\n        difficulty: \"Intermediate\",\n        lessons: [\n            {\n                id: 1,\n                title: \"FVG Theory and Formation\",\n                description: \"Understanding how and why Fair Value Gaps form in the market\",\n                content: \"Fair Value Gaps represent imbalances in price discovery...\",\n                type: \"theory\",\n                duration: \"12 min\",\n                keyPoints: [\n                    \"Imbalance vs inefficiency concepts\",\n                    \"Three-candle FVG formation\",\n                    \"Institutional vs retail gaps\"\n                ]\n            },\n            {\n                id: 2,\n                title: \"FVG Classification System\",\n                description: \"Learn to classify FVGs by strength and probability\",\n                content: \"Not all FVGs are equal. Learn the classification system...\",\n                type: \"practical\",\n                duration: \"15 min\",\n                keyPoints: [\n                    \"High probability vs low probability gaps\",\n                    \"Size and context importance\",\n                    \"Multiple timeframe FVG analysis\"\n                ]\n            },\n            {\n                id: 3,\n                title: \"Trading FVG Fills\",\n                description: \"Strategies for trading when price returns to fill gaps\",\n                content: \"FVG fills often provide excellent trading opportunities...\",\n                type: \"strategy\",\n                duration: \"16 min\",\n                keyPoints: [\n                    \"Partial vs full gap fills\",\n                    \"Entry and exit strategies\",\n                    \"Combining FVGs with other confluences\"\n                ]\n            },\n            {\n                id: 4,\n                title: \"FVG Recognition Challenge\",\n                description: \"Test your ability to spot and classify FVGs in real-time\",\n                content: \"Interactive challenge to identify FVGs on live charts\",\n                type: \"interactive\",\n                duration: \"7 min\",\n                keyPoints: [\n                    \"Speed recognition drills\",\n                    \"Classification accuracy\",\n                    \"Real-time decision making\"\n                ]\n            }\n        ]\n    },\n    {\n        id: 4,\n        title: \"Volume Analysis & Confirmation\",\n        description: \"Use volume analysis to confirm your price action signals\",\n        icon: \"Activity\",\n        color: \"from-orange-500 to-orange-600\",\n        estimatedTime: \"40 minutes\",\n        difficulty: \"Beginner\",\n        lessons: [\n            {\n                id: 1,\n                title: \"Volume Fundamentals\",\n                description: \"Understanding volume and its relationship to price movement\",\n                content: \"Volume is the fuel that drives price movement...\",\n                type: \"theory\",\n                duration: \"10 min\",\n                keyPoints: [\n                    \"Volume precedes price\",\n                    \"Accumulation vs distribution patterns\",\n                    \"Volume profile concepts\"\n                ]\n            },\n            {\n                id: 2,\n                title: \"Volume at Key Levels\",\n                description: \"Analyzing volume behavior at support/resistance zones\",\n                content: \"How volume behaves at key levels tells us about market sentiment...\",\n                type: \"practical\",\n                duration: \"15 min\",\n                keyPoints: [\n                    \"Rising volume on approach to zones\",\n                    \"Fading volume and false breakouts\",\n                    \"Climactic volume patterns\"\n                ]\n            },\n            {\n                id: 3,\n                title: \"Volume Confirmation Strategies\",\n                description: \"Using volume to confirm your trading signals\",\n                content: \"Volume confirmation can significantly improve trade success rates...\",\n                type: \"strategy\",\n                duration: \"12 min\",\n                keyPoints: [\n                    \"Volume divergence signals\",\n                    \"Confirmation vs contradiction\",\n                    \"Multiple timeframe volume analysis\"\n                ]\n            },\n            {\n                id: 4,\n                title: \"Volume Analysis Practice\",\n                description: \"Practice reading volume patterns on SPY/QQQ charts\",\n                content: \"Hands-on practice with volume analysis techniques\",\n                type: \"interactive\",\n                duration: \"3 min\",\n                keyPoints: [\n                    \"Pattern recognition\",\n                    \"Signal confirmation practice\",\n                    \"Real-world application\"\n                ]\n            }\n        ]\n    },\n    {\n        id: 5,\n        title: \"Confirmation Stacking\",\n        description: \"Learn to stack multiple confirmations for high-probability trades\",\n        icon: \"Layers\",\n        color: \"from-red-500 to-red-600\",\n        estimatedTime: \"55 minutes\",\n        difficulty: \"Advanced\",\n        lessons: [\n            {\n                id: 1,\n                title: \"The Stacking Methodology\",\n                description: \"Understanding the concept of confirmation stacking\",\n                content: \"Confirmation stacking involves combining multiple technical signals...\",\n                type: \"theory\",\n                duration: \"12 min\",\n                keyPoints: [\n                    \"Quality over quantity in confirmations\",\n                    \"Weighted confirmation systems\",\n                    \"Avoiding analysis paralysis\"\n                ]\n            },\n            {\n                id: 2,\n                title: \"Building Your Stack\",\n                description: \"How to systematically build confirmation stacks\",\n                content: \"Learn the systematic approach to building robust confirmation stacks...\",\n                type: \"practical\",\n                duration: \"18 min\",\n                keyPoints: [\n                    \"Primary vs secondary confirmations\",\n                    \"Timeframe hierarchy\",\n                    \"Confluence zone identification\"\n                ]\n            },\n            {\n                id: 3,\n                title: \"Advanced Stacking Techniques\",\n                description: \"Professional-level confirmation stacking strategies\",\n                content: \"Advanced techniques used by professional traders...\",\n                type: \"strategy\",\n                duration: \"20 min\",\n                keyPoints: [\n                    \"Multi-timeframe stacking\",\n                    \"Intermarket confirmations\",\n                    \"Sentiment-based confirmations\"\n                ]\n            },\n            {\n                id: 4,\n                title: \"Stacking Mastery Challenge\",\n                description: \"Put your stacking skills to the test with complex scenarios\",\n                content: \"Advanced challenge scenarios to test your mastery\",\n                type: \"interactive\",\n                duration: \"5 min\",\n                keyPoints: [\n                    \"Complex scenario analysis\",\n                    \"Decision-making under pressure\",\n                    \"Professional-level execution\"\n                ]\n            }\n        ]\n    },\n    {\n        id: 6,\n        title: \"Risk Management & Psychology\",\n        description: \"Master the mental game and risk management for consistent profits\",\n        icon: \"Shield\",\n        color: \"from-indigo-500 to-indigo-600\",\n        estimatedTime: \"45 minutes\",\n        difficulty: \"Intermediate\",\n        lessons: [\n            {\n                id: 1,\n                title: \"Position Sizing Fundamentals\",\n                description: \"Calculate optimal position sizes for your account\",\n                content: \"Proper position sizing is the foundation of risk management...\",\n                type: \"theory\",\n                duration: \"12 min\",\n                keyPoints: [\n                    \"Risk percentage rules\",\n                    \"Account size considerations\",\n                    \"Volatility-adjusted sizing\"\n                ]\n            },\n            {\n                id: 2,\n                title: \"Stop Loss Strategies\",\n                description: \"Advanced stop loss placement and management techniques\",\n                content: \"Stop losses are your insurance policy in trading...\",\n                type: \"practical\",\n                duration: \"15 min\",\n                keyPoints: [\n                    \"Technical vs percentage stops\",\n                    \"Trailing stop strategies\",\n                    \"Stop loss psychology\"\n                ]\n            },\n            {\n                id: 3,\n                title: \"Trading Psychology Mastery\",\n                description: \"Develop the mental discipline required for consistent trading\",\n                content: \"Trading psychology often determines success more than technical skills...\",\n                type: \"strategy\",\n                duration: \"15 min\",\n                keyPoints: [\n                    \"Emotional regulation techniques\",\n                    \"Dealing with losses\",\n                    \"Maintaining discipline\"\n                ]\n            },\n            {\n                id: 4,\n                title: \"Psychology Assessment\",\n                description: \"Evaluate your trading psychology and identify areas for improvement\",\n                content: \"Self-assessment tools for trading psychology\",\n                type: \"interactive\",\n                duration: \"3 min\",\n                keyPoints: [\n                    \"Psychological profiling\",\n                    \"Weakness identification\",\n                    \"Improvement planning\"\n                ]\n            }\n        ]\n    },\n    {\n        id: 7,\n        title: \"Advanced Pattern Recognition\",\n        description: \"Identify complex patterns and market structures for professional-level trading\",\n        icon: \"Eye\",\n        color: \"from-teal-500 to-teal-600\",\n        estimatedTime: \"65 minutes\",\n        difficulty: \"Advanced\",\n        lessons: [\n            {\n                id: 1,\n                title: \"Complex Pattern Structures\",\n                description: \"Understanding advanced chart patterns and their implications\",\n                content: \"Advanced patterns often provide the highest probability setups...\",\n                type: \"theory\",\n                duration: \"18 min\",\n                keyPoints: [\n                    \"Multi-timeframe pattern analysis\",\n                    \"Pattern failure and continuation\",\n                    \"Context-dependent patterns\"\n                ]\n            },\n            {\n                id: 2,\n                title: \"Market Structure Shifts\",\n                description: \"Identifying when market structure changes and how to adapt\",\n                content: \"Market structure shifts signal major changes in sentiment...\",\n                type: \"practical\",\n                duration: \"20 min\",\n                keyPoints: [\n                    \"Break of structure signals\",\n                    \"Change of character patterns\",\n                    \"Trend transition identification\"\n                ]\n            },\n            {\n                id: 3,\n                title: \"Professional Pattern Trading\",\n                description: \"How professionals trade complex patterns for maximum profit\",\n                content: \"Professional trading strategies for advanced patterns...\",\n                type: \"strategy\",\n                duration: \"22 min\",\n                keyPoints: [\n                    \"Entry and exit optimization\",\n                    \"Risk-reward maximization\",\n                    \"Pattern-specific strategies\"\n                ]\n            },\n            {\n                id: 4,\n                title: \"Pattern Mastery Exam\",\n                description: \"Final examination of your pattern recognition abilities\",\n                content: \"Comprehensive test of all pattern recognition skills\",\n                type: \"interactive\",\n                duration: \"5 min\",\n                keyPoints: [\n                    \"Comprehensive pattern test\",\n                    \"Speed and accuracy assessment\",\n                    \"Professional certification\"\n                ]\n            }\n        ]\n    }\n];\nconst COURSE_ACHIEVEMENTS = [\n    {\n        id: \"first_lesson\",\n        title: \"Getting Started\",\n        description: \"Complete your first lesson\",\n        icon: \"Play\",\n        points: 10\n    },\n    {\n        id: \"first_module\",\n        title: \"Module Master\",\n        description: \"Complete your first module\",\n        icon: \"Award\",\n        points: 50\n    },\n    {\n        id: \"quiz_master\",\n        title: \"Quiz Master\",\n        description: \"Score 90% or higher on 5 quizzes\",\n        icon: \"Brain\",\n        points: 100\n    },\n    {\n        id: \"speed_learner\",\n        title: \"Speed Learner\",\n        description: \"Complete 3 lessons in one day\",\n        icon: \"Zap\",\n        points: 75\n    },\n    {\n        id: \"course_complete\",\n        title: \"Course Graduate\",\n        description: \"Complete the entire course\",\n        icon: \"GraduationCap\",\n        points: 500\n    }\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/data/courseData.js\n"));

/***/ })

});