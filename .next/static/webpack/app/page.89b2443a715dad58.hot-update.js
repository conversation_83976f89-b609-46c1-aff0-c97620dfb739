"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/data/courseData.js":
/*!********************************!*\
  !*** ./src/data/courseData.js ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   COURSE_ACHIEVEMENTS: function() { return /* binding */ COURSE_ACHIEVEMENTS; },\n/* harmony export */   COURSE_MODULES: function() { return /* binding */ COURSE_MODULES; }\n/* harmony export */ });\n/**\n * Advanced Price Action Course Data\n * Professional SPY/QQQ Trading Course - Liquidity Sweeps, FVGs, and Confirmation Stacking\n * Complete educational content with real trading strategies\n */ const COURSE_MODULES = [\n    {\n        id: 1,\n        title: \"Market Structure & Zone Identification\",\n        description: \"Master the fundamentals of market structure analysis and precise zone identification on SPY/QQQ using 15-30 minute timeframes\",\n        icon: \"Target\",\n        color: \"from-blue-500 to-blue-600\",\n        estimatedTime: \"60 minutes\",\n        difficulty: \"Beginner\",\n        prerequisites: \"Basic understanding of candlestick charts\",\n        learningObjectives: [\n            \"Identify market structure shifts with 95% accuracy\",\n            \"Draw precise support/resistance zones using institutional levels\",\n            \"Recognize trend vs range-bound market conditions\",\n            \"Apply zone validation techniques for high-probability setups\"\n        ],\n        lessons: [\n            {\n                id: 1,\n                title: \"Market Structure Fundamentals\",\n                description: \"Deep dive into market structure analysis - the foundation of professional price action trading\",\n                content: \"\\n# Market Structure: The Foundation of Professional Trading\\n\\nMarket structure is the backbone of all successful price action trading. Unlike retail traders who focus on indicators, institutional traders analyze market structure to identify where smart money is positioned.\\n\\n## What is Market Structure?\\n\\nMarket structure refers to the way price moves and creates patterns that reveal the underlying supply and demand dynamics. It's the language that institutional traders use to communicate their intentions through price action.\\n\\n### Key Components:\\n\\n**1. Swing Highs and Swing Lows**\\n- A swing high is formed when price creates a peak with lower highs on both sides\\n- A swing low is formed when price creates a valley with higher lows on both sides\\n- These points represent areas where institutional orders were executed\\n\\n**2. Trend Identification**\\n- **Uptrend**: Series of higher highs (HH) and higher lows (HL)\\n- **Downtrend**: Series of lower highs (LH) and lower lows (LL)\\n- **Sideways**: Price oscillates between defined levels without clear direction\\n\\n**3. Market Phases**\\n- **Accumulation**: Smart money quietly builds positions\\n- **Markup/Markdown**: Directional movement as institutions move price\\n- **Distribution**: Smart money exits positions to retail traders\\n\\n## Why SPY/QQQ Are Perfect for Structure Analysis\\n\\nSPY and QQQ are ideal instruments for market structure analysis because:\\n- High liquidity ensures clean price action\\n- Institutional participation creates clear structural levels\\n- ETF nature reduces individual stock noise\\n- Strong correlation with overall market sentiment\\n\\n## Practical Application\\n\\nWhen analyzing SPY/QQQ structure:\\n1. Start with higher timeframes (daily/4H) for context\\n2. Use 15-30 minute charts for precise entry timing\\n3. Mark significant swing highs and lows\\n4. Identify the current trend phase\\n5. Look for structure breaks as trend change signals\\n        \",\n                type: \"theory\",\n                duration: \"15 min\",\n                keyPoints: [\n                    \"Market structure reveals institutional order flow and smart money positioning\",\n                    \"Swing highs and lows mark areas of significant institutional activity\",\n                    \"Trend identification through higher highs/lows vs lower highs/lows patterns\",\n                    \"SPY/QQQ provide clean structure due to high institutional participation\",\n                    \"Structure breaks often precede major trend changes and trading opportunities\"\n                ],\n                practicalExercises: [\n                    \"Identify 5 swing highs and 5 swing lows on a SPY 30-minute chart\",\n                    \"Determine current trend direction using structure analysis\",\n                    \"Mark the most recent structure break and analyze the subsequent price action\"\n                ],\n                quiz: [\n                    {\n                        question: \"What defines an uptrend in market structure analysis?\",\n                        options: [\n                            \"Price moving above a moving average\",\n                            \"Series of higher highs and higher lows\",\n                            \"Increasing volume\",\n                            \"Bullish candlestick patterns\"\n                        ],\n                        correct: 1,\n                        explanation: \"An uptrend is defined by a series of higher highs and higher lows, indicating that buyers are willing to pay progressively higher prices.\"\n                    },\n                    {\n                        question: \"Why are SPY and QQQ ideal for market structure analysis?\",\n                        options: [\n                            \"They have low volatility\",\n                            \"They only move during market hours\",\n                            \"High liquidity and institutional participation create clean price action\",\n                            \"They always trend upward\"\n                        ],\n                        correct: 2,\n                        explanation: \"SPY and QQQ's high liquidity and heavy institutional participation result in clean, reliable price action that clearly shows market structure.\"\n                    }\n                ]\n            },\n            {\n                id: 2,\n                title: \"Professional Zone Drawing Techniques\",\n                description: \"Master the art of drawing institutional-grade support and resistance zones using proven professional methods\",\n                content: \"\\n# Professional Zone Drawing: The Institutional Approach\\n\\nProfessional traders don't draw single lines - they draw zones. This lesson teaches you the exact techniques used by institutional traders to identify and draw high-probability support and resistance zones.\\n\\n## Why Zones, Not Lines?\\n\\n**The Reality of Price Action:**\\n- Price rarely respects exact levels\\n- Institutional orders create zones of activity\\n- Market volatility requires zone-based thinking\\n- Zones account for spread and slippage\\n\\n## The Professional Zone Drawing Method\\n\\n### Step 1: Identify Significant Price Reactions\\nLook for areas where price showed strong reactions:\\n- Sharp reversals with long wicks\\n- Multiple touches over time\\n- High volume at the level\\n- Confluence with other technical factors\\n\\n### Step 2: Determine Zone Boundaries\\n**Upper Boundary:** The highest point of the reaction area\\n**Lower Boundary:** The lowest point where price found support/resistance\\n**Zone Thickness:** Typically 0.1% to 0.3% of the instrument's price\\n\\n### Step 3: Zone Validation Criteria\\nA strong zone must have:\\n- **Multiple Touches:** At least 2-3 significant reactions\\n- **Time Significance:** Held importance over multiple sessions\\n- **Volume Confirmation:** Higher volume at the zone\\n- **Clean Reactions:** Clear bounces without excessive penetration\\n\\n## SPY/QQQ Specific Techniques\\n\\n### For SPY (S&P 500 ETF):\\n- Use psychological levels (round numbers)\\n- Focus on previous day's high/low\\n- Watch for gap fill levels\\n- Consider options strike prices\\n\\n### For QQQ (NASDAQ ETF):\\n- Tech sector sensitivity to news\\n- Higher volatility requires wider zones\\n- Watch for correlation with major tech stocks\\n- Consider after-hours trading impact\\n\\n## Timeframe Considerations\\n\\n**15-Minute Charts:**\\n- Best for intraday precision\\n- Ideal for scalping entries\\n- Shows micro-structure clearly\\n- Good for tight stop losses\\n\\n**30-Minute Charts:**\\n- Perfect balance of detail and context\\n- Reduces noise while maintaining precision\\n- Ideal for swing trading setups\\n- Professional standard for zone identification\\n\\n## Common Mistakes to Avoid\\n\\n1. **Drawing zones too narrow** - Account for natural volatility\\n2. **Ignoring volume** - Zones without volume confirmation are weak\\n3. **Over-analyzing** - Too many zones create confusion\\n4. **Wrong timeframe** - Match timeframe to trading style\\n5. **Static thinking** - Zones can strengthen or weaken over time\\n\\n## Practical Zone Drawing Exercise\\n\\n**Step-by-Step Process:**\\n1. Open SPY 30-minute chart\\n2. Identify last 5 significant swing highs/lows\\n3. Look for areas with multiple touches\\n4. Draw zones around these areas\\n5. Validate with volume analysis\\n6. Test zones with subsequent price action\\n        \",\n                type: \"practical\",\n                duration: \"20 min\",\n                keyPoints: [\n                    \"Professional traders use zones, not lines, to account for market volatility and institutional order flow\",\n                    \"Zone thickness should be 0.1-0.3% of instrument price for SPY/QQQ trading\",\n                    \"Multiple touches, time significance, and volume confirmation validate zone strength\",\n                    \"15-30 minute timeframes provide optimal balance between precision and context\",\n                    \"SPY zones often align with psychological levels and options strikes\"\n                ],\n                practicalExercises: [\n                    \"Draw 3 support zones and 3 resistance zones on current SPY 30-minute chart\",\n                    \"Validate each zone using the 3-criteria method (touches, time, volume)\",\n                    \"Measure zone thickness and ensure it's within professional parameters\",\n                    \"Test zones by observing how price reacts at these levels over next trading session\"\n                ],\n                quiz: [\n                    {\n                        question: \"What is the ideal zone thickness for SPY/QQQ trading?\",\n                        options: [\n                            \"Exactly 1 point\",\n                            \"0.1% to 0.3% of the instrument's price\",\n                            \"5% of the current price\",\n                            \"Whatever looks good on the chart\"\n                        ],\n                        correct: 1,\n                        explanation: \"Professional zone thickness should be 0.1-0.3% of the instrument's price to account for natural volatility while maintaining precision.\"\n                    },\n                    {\n                        question: \"Which timeframe combination is considered professional standard for zone identification?\",\n                        options: [\n                            \"1-minute and 5-minute\",\n                            \"15-minute and 30-minute\",\n                            \"1-hour and 4-hour\",\n                            \"Daily and weekly\"\n                        ],\n                        correct: 1,\n                        explanation: \"15-30 minute timeframes provide the optimal balance between precision for entries and broader market context.\"\n                    }\n                ]\n            },\n            {\n                id: 3,\n                title: \"Zone Validation Techniques\",\n                description: \"How to validate the strength and reliability of your zones\",\n                content: \"Not all zones are created equal. Learn to identify the strongest...\",\n                type: \"practical\",\n                duration: \"12 min\",\n                keyPoints: [\n                    \"Volume confirmation at zones\",\n                    \"Multiple timeframe validation\",\n                    \"Age and frequency of tests\"\n                ]\n            },\n            {\n                id: 4,\n                title: \"Interactive Zone Drawing Exercise\",\n                description: \"Practice identifying and drawing zones on real SPY/QQQ charts\",\n                content: \"Apply your knowledge with guided practice on live market examples\",\n                type: \"interactive\",\n                duration: \"8 min\",\n                keyPoints: [\n                    \"Real-time chart analysis\",\n                    \"Immediate feedback on zone placement\",\n                    \"Common mistakes to avoid\"\n                ]\n            }\n        ]\n    },\n    {\n        id: 2,\n        title: \"Liquidity Sweeps & Price Action\",\n        description: \"Identify and capitalize on liquidity sweeps above/below recent highs and lows\",\n        icon: \"TrendingUp\",\n        color: \"from-green-500 to-green-600\",\n        estimatedTime: \"60 minutes\",\n        difficulty: \"Intermediate\",\n        lessons: [\n            {\n                id: 1,\n                title: \"Understanding Liquidity Concepts\",\n                description: \"What is liquidity and why do markets sweep it?\",\n                content: \"Liquidity represents areas where stop losses and pending orders cluster...\",\n                type: \"theory\",\n                duration: \"15 min\",\n                keyPoints: [\n                    \"Stop loss clusters above/below key levels\",\n                    \"Institutional order flow dynamics\",\n                    \"Smart money vs retail positioning\"\n                ]\n            },\n            {\n                id: 2,\n                title: \"Identifying Sweep Patterns\",\n                description: \"Recognize the telltale signs of liquidity sweeps\",\n                content: \"Liquidity sweeps often appear as brief spikes beyond key levels...\",\n                type: \"practical\",\n                duration: \"20 min\",\n                keyPoints: [\n                    \"False breakout characteristics\",\n                    \"Volume patterns during sweeps\",\n                    \"Time-based sweep identification\"\n                ]\n            },\n            {\n                id: 3,\n                title: \"Trading Liquidity Sweeps\",\n                description: \"How to position yourself to profit from sweep reversals\",\n                content: \"Once you identify a liquidity sweep, the next step is positioning...\",\n                type: \"strategy\",\n                duration: \"18 min\",\n                keyPoints: [\n                    \"Entry timing after sweep completion\",\n                    \"Stop loss placement strategies\",\n                    \"Target setting for sweep trades\"\n                ]\n            },\n            {\n                id: 4,\n                title: \"Sweep Analysis Workshop\",\n                description: \"Analyze real SPY/QQQ liquidity sweeps with expert commentary\",\n                content: \"Review historical examples of successful sweep trades\",\n                type: \"interactive\",\n                duration: \"7 min\",\n                keyPoints: [\n                    \"Case study analysis\",\n                    \"Pattern recognition practice\",\n                    \"Risk management examples\"\n                ]\n            }\n        ]\n    },\n    {\n        id: 3,\n        title: \"Fair Value Gaps (FVGs)\",\n        description: \"Master the identification and trading of Fair Value Gaps in price action\",\n        icon: \"BarChart3\",\n        color: \"from-purple-500 to-purple-600\",\n        estimatedTime: \"50 minutes\",\n        difficulty: \"Intermediate\",\n        lessons: [\n            {\n                id: 1,\n                title: \"FVG Theory and Formation\",\n                description: \"Understanding how and why Fair Value Gaps form in the market\",\n                content: \"Fair Value Gaps represent imbalances in price discovery...\",\n                type: \"theory\",\n                duration: \"12 min\",\n                keyPoints: [\n                    \"Imbalance vs inefficiency concepts\",\n                    \"Three-candle FVG formation\",\n                    \"Institutional vs retail gaps\"\n                ]\n            },\n            {\n                id: 2,\n                title: \"FVG Classification System\",\n                description: \"Learn to classify FVGs by strength and probability\",\n                content: \"Not all FVGs are equal. Learn the classification system...\",\n                type: \"practical\",\n                duration: \"15 min\",\n                keyPoints: [\n                    \"High probability vs low probability gaps\",\n                    \"Size and context importance\",\n                    \"Multiple timeframe FVG analysis\"\n                ]\n            },\n            {\n                id: 3,\n                title: \"Trading FVG Fills\",\n                description: \"Strategies for trading when price returns to fill gaps\",\n                content: \"FVG fills often provide excellent trading opportunities...\",\n                type: \"strategy\",\n                duration: \"16 min\",\n                keyPoints: [\n                    \"Partial vs full gap fills\",\n                    \"Entry and exit strategies\",\n                    \"Combining FVGs with other confluences\"\n                ]\n            },\n            {\n                id: 4,\n                title: \"FVG Recognition Challenge\",\n                description: \"Test your ability to spot and classify FVGs in real-time\",\n                content: \"Interactive challenge to identify FVGs on live charts\",\n                type: \"interactive\",\n                duration: \"7 min\",\n                keyPoints: [\n                    \"Speed recognition drills\",\n                    \"Classification accuracy\",\n                    \"Real-time decision making\"\n                ]\n            }\n        ]\n    },\n    {\n        id: 4,\n        title: \"Volume Analysis & Confirmation\",\n        description: \"Use volume analysis to confirm your price action signals\",\n        icon: \"Activity\",\n        color: \"from-orange-500 to-orange-600\",\n        estimatedTime: \"40 minutes\",\n        difficulty: \"Beginner\",\n        lessons: [\n            {\n                id: 1,\n                title: \"Volume Fundamentals\",\n                description: \"Understanding volume and its relationship to price movement\",\n                content: \"Volume is the fuel that drives price movement...\",\n                type: \"theory\",\n                duration: \"10 min\",\n                keyPoints: [\n                    \"Volume precedes price\",\n                    \"Accumulation vs distribution patterns\",\n                    \"Volume profile concepts\"\n                ]\n            },\n            {\n                id: 2,\n                title: \"Volume at Key Levels\",\n                description: \"Analyzing volume behavior at support/resistance zones\",\n                content: \"How volume behaves at key levels tells us about market sentiment...\",\n                type: \"practical\",\n                duration: \"15 min\",\n                keyPoints: [\n                    \"Rising volume on approach to zones\",\n                    \"Fading volume and false breakouts\",\n                    \"Climactic volume patterns\"\n                ]\n            },\n            {\n                id: 3,\n                title: \"Volume Confirmation Strategies\",\n                description: \"Using volume to confirm your trading signals\",\n                content: \"Volume confirmation can significantly improve trade success rates...\",\n                type: \"strategy\",\n                duration: \"12 min\",\n                keyPoints: [\n                    \"Volume divergence signals\",\n                    \"Confirmation vs contradiction\",\n                    \"Multiple timeframe volume analysis\"\n                ]\n            },\n            {\n                id: 4,\n                title: \"Volume Analysis Practice\",\n                description: \"Practice reading volume patterns on SPY/QQQ charts\",\n                content: \"Hands-on practice with volume analysis techniques\",\n                type: \"interactive\",\n                duration: \"3 min\",\n                keyPoints: [\n                    \"Pattern recognition\",\n                    \"Signal confirmation practice\",\n                    \"Real-world application\"\n                ]\n            }\n        ]\n    },\n    {\n        id: 5,\n        title: \"Confirmation Stacking\",\n        description: \"Learn to stack multiple confirmations for high-probability trades\",\n        icon: \"Layers\",\n        color: \"from-red-500 to-red-600\",\n        estimatedTime: \"55 minutes\",\n        difficulty: \"Advanced\",\n        lessons: [\n            {\n                id: 1,\n                title: \"The Stacking Methodology\",\n                description: \"Understanding the concept of confirmation stacking\",\n                content: \"Confirmation stacking involves combining multiple technical signals...\",\n                type: \"theory\",\n                duration: \"12 min\",\n                keyPoints: [\n                    \"Quality over quantity in confirmations\",\n                    \"Weighted confirmation systems\",\n                    \"Avoiding analysis paralysis\"\n                ]\n            },\n            {\n                id: 2,\n                title: \"Building Your Stack\",\n                description: \"How to systematically build confirmation stacks\",\n                content: \"Learn the systematic approach to building robust confirmation stacks...\",\n                type: \"practical\",\n                duration: \"18 min\",\n                keyPoints: [\n                    \"Primary vs secondary confirmations\",\n                    \"Timeframe hierarchy\",\n                    \"Confluence zone identification\"\n                ]\n            },\n            {\n                id: 3,\n                title: \"Advanced Stacking Techniques\",\n                description: \"Professional-level confirmation stacking strategies\",\n                content: \"Advanced techniques used by professional traders...\",\n                type: \"strategy\",\n                duration: \"20 min\",\n                keyPoints: [\n                    \"Multi-timeframe stacking\",\n                    \"Intermarket confirmations\",\n                    \"Sentiment-based confirmations\"\n                ]\n            },\n            {\n                id: 4,\n                title: \"Stacking Mastery Challenge\",\n                description: \"Put your stacking skills to the test with complex scenarios\",\n                content: \"Advanced challenge scenarios to test your mastery\",\n                type: \"interactive\",\n                duration: \"5 min\",\n                keyPoints: [\n                    \"Complex scenario analysis\",\n                    \"Decision-making under pressure\",\n                    \"Professional-level execution\"\n                ]\n            }\n        ]\n    },\n    {\n        id: 6,\n        title: \"Risk Management & Psychology\",\n        description: \"Master the mental game and risk management for consistent profits\",\n        icon: \"Shield\",\n        color: \"from-indigo-500 to-indigo-600\",\n        estimatedTime: \"45 minutes\",\n        difficulty: \"Intermediate\",\n        lessons: [\n            {\n                id: 1,\n                title: \"Position Sizing Fundamentals\",\n                description: \"Calculate optimal position sizes for your account\",\n                content: \"Proper position sizing is the foundation of risk management...\",\n                type: \"theory\",\n                duration: \"12 min\",\n                keyPoints: [\n                    \"Risk percentage rules\",\n                    \"Account size considerations\",\n                    \"Volatility-adjusted sizing\"\n                ]\n            },\n            {\n                id: 2,\n                title: \"Stop Loss Strategies\",\n                description: \"Advanced stop loss placement and management techniques\",\n                content: \"Stop losses are your insurance policy in trading...\",\n                type: \"practical\",\n                duration: \"15 min\",\n                keyPoints: [\n                    \"Technical vs percentage stops\",\n                    \"Trailing stop strategies\",\n                    \"Stop loss psychology\"\n                ]\n            },\n            {\n                id: 3,\n                title: \"Trading Psychology Mastery\",\n                description: \"Develop the mental discipline required for consistent trading\",\n                content: \"Trading psychology often determines success more than technical skills...\",\n                type: \"strategy\",\n                duration: \"15 min\",\n                keyPoints: [\n                    \"Emotional regulation techniques\",\n                    \"Dealing with losses\",\n                    \"Maintaining discipline\"\n                ]\n            },\n            {\n                id: 4,\n                title: \"Psychology Assessment\",\n                description: \"Evaluate your trading psychology and identify areas for improvement\",\n                content: \"Self-assessment tools for trading psychology\",\n                type: \"interactive\",\n                duration: \"3 min\",\n                keyPoints: [\n                    \"Psychological profiling\",\n                    \"Weakness identification\",\n                    \"Improvement planning\"\n                ]\n            }\n        ]\n    },\n    {\n        id: 7,\n        title: \"Advanced Pattern Recognition\",\n        description: \"Identify complex patterns and market structures for professional-level trading\",\n        icon: \"Eye\",\n        color: \"from-teal-500 to-teal-600\",\n        estimatedTime: \"65 minutes\",\n        difficulty: \"Advanced\",\n        lessons: [\n            {\n                id: 1,\n                title: \"Complex Pattern Structures\",\n                description: \"Understanding advanced chart patterns and their implications\",\n                content: \"Advanced patterns often provide the highest probability setups...\",\n                type: \"theory\",\n                duration: \"18 min\",\n                keyPoints: [\n                    \"Multi-timeframe pattern analysis\",\n                    \"Pattern failure and continuation\",\n                    \"Context-dependent patterns\"\n                ]\n            },\n            {\n                id: 2,\n                title: \"Market Structure Shifts\",\n                description: \"Identifying when market structure changes and how to adapt\",\n                content: \"Market structure shifts signal major changes in sentiment...\",\n                type: \"practical\",\n                duration: \"20 min\",\n                keyPoints: [\n                    \"Break of structure signals\",\n                    \"Change of character patterns\",\n                    \"Trend transition identification\"\n                ]\n            },\n            {\n                id: 3,\n                title: \"Professional Pattern Trading\",\n                description: \"How professionals trade complex patterns for maximum profit\",\n                content: \"Professional trading strategies for advanced patterns...\",\n                type: \"strategy\",\n                duration: \"22 min\",\n                keyPoints: [\n                    \"Entry and exit optimization\",\n                    \"Risk-reward maximization\",\n                    \"Pattern-specific strategies\"\n                ]\n            },\n            {\n                id: 4,\n                title: \"Pattern Mastery Exam\",\n                description: \"Final examination of your pattern recognition abilities\",\n                content: \"Comprehensive test of all pattern recognition skills\",\n                type: \"interactive\",\n                duration: \"5 min\",\n                keyPoints: [\n                    \"Comprehensive pattern test\",\n                    \"Speed and accuracy assessment\",\n                    \"Professional certification\"\n                ]\n            }\n        ]\n    }\n];\nconst COURSE_ACHIEVEMENTS = [\n    {\n        id: \"first_lesson\",\n        title: \"Getting Started\",\n        description: \"Complete your first lesson\",\n        icon: \"Play\",\n        points: 10\n    },\n    {\n        id: \"first_module\",\n        title: \"Module Master\",\n        description: \"Complete your first module\",\n        icon: \"Award\",\n        points: 50\n    },\n    {\n        id: \"quiz_master\",\n        title: \"Quiz Master\",\n        description: \"Score 90% or higher on 5 quizzes\",\n        icon: \"Brain\",\n        points: 100\n    },\n    {\n        id: \"speed_learner\",\n        title: \"Speed Learner\",\n        description: \"Complete 3 lessons in one day\",\n        icon: \"Zap\",\n        points: 75\n    },\n    {\n        id: \"course_complete\",\n        title: \"Course Graduate\",\n        description: \"Complete the entire course\",\n        icon: \"GraduationCap\",\n        points: 500\n    }\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/data/courseData.js\n"));

/***/ })

});