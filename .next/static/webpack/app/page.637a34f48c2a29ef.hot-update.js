"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/data/courseData.js":
/*!********************************!*\
  !*** ./src/data/courseData.js ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   COURSE_ACHIEVEMENTS: function() { return /* binding */ COURSE_ACHIEVEMENTS; },\n/* harmony export */   COURSE_MODULES: function() { return /* binding */ COURSE_MODULES; }\n/* harmony export */ });\n/**\n * Advanced Price Action Course Data\n * Professional SPY/QQQ Trading Course - Liquidity Sweeps, FVGs, and Confirmation Stacking\n * Complete educational content with real trading strategies\n */ const COURSE_MODULES = [\n    {\n        id: 1,\n        title: \"Market Structure & Liquidity Fundamentals\",\n        description: \"Master the institutional approach to reading market structure. Learn how professional traders identify liquidity zones and predict price movement on SPY/QQQ.\",\n        icon: \"Target\",\n        color: \"from-blue-500 to-blue-600\",\n        estimatedTime: \"90 minutes\",\n        difficulty: \"Beginner\",\n        prerequisites: \"Basic understanding of candlestick charts and support/resistance levels\",\n        learningObjectives: [\n            \"Decode institutional market structure patterns with professional precision\",\n            \"Identify high-probability liquidity zones where smart money operates\",\n            \"Distinguish between genuine breakouts and institutional stop hunts\",\n            \"Apply market structure analysis to SPY/QQQ for consistent profits\",\n            \"Master the professional trader's mindset: 'liquidity drives direction'\"\n        ],\n        lessons: [\n            {\n                id: 1,\n                title: \"Understanding Liquidity Sweeps & Stop Hunts\",\n                description: \"Learn how institutional players manipulate price to grab liquidity and how to identify these moves on SPY/QQQ\",\n                content: \"\\n# Liquidity Sweeps: The Institutional Edge\\n\\n## What Does \\\"Sweeping Liquidity\\\" Mean?\\n\\nIn trading, a **liquidity sweep** refers to price briefly breaching a key level – often a prior session's high or low – to trigger stop orders clustered there. For example, sweeping the previous day's high/low means price pokes above the prior day's high (or below the low), activating stop-losses and pending orders in that area, before often snapping back.\\n\\nThis phenomenon is commonly known as a **stop hunt** or **liquidity grab**. It's not just random volatility; it's usually caused by large players exploiting those liquidity pockets.\\n\\n## Why Do Institutions/Algos Do This?\\n\\nBig market participants (like institutions or algorithms) need substantial liquidity to fill large orders without moving the market too much against themselves. Running price into an area with many resting orders supplies that liquidity.\\n\\nIn a liquidity sweep, significant players deliberately drive price through a known level to trigger clusters of orders (stops, breakout entries) – creating a surge of executions they can use to enter or exit positions.\\n\\n**Key Concept**: They use the liquidity from trapped traders (the \\\"fish\\\") to fill their nets.\\n\\nOnce those orders are triggered (buy stops above highs or sell stops below lows), the market often reverses direction abruptly because the smart money has taken the opposite side of those orders.\\n\\n## The Golden Rule: Price Seeks Liquidity Before Direction\\n\\nPrice will usually either:\\n- **Fake-out and reverse** after grabbing liquidity\\n- **Break out and continue** if genuine buying/selling pressure exists\\n\\n## How to Spot a Liquidity Sweep\\n\\n### 1. Wick Beyond a Key Level\\nPrice breaks the previous session's high/low momentarily, then retreats back within the prior range. On a chart this appears as a sharp wick poking through resistance or support.\\n\\n### 2. Volume Surge\\nLiquidity sweeps trigger a burst of trading volume. All those stop orders getting hit creates a large volume spike.\\n\\n### 3. Failed Breakout Behavior\\nFollowing the initial poke through the level, momentum stalls out. Price quickly falls back below the taken high or back above the swept low.\\n\\n### 4. Stop Clusters on Chart\\nMarkets often target equal highs or equal lows (retail traders' stop placements). When these get taken out by just a small margin and reverse, you've spotted a liquidity sweep.\\n\\n## Real SPY/QQQ Examples\\n\\n**Morning Gap Sweep**: SPY gaps up at open, takes out yesterday's high by $0.50, then immediately reverses as institutional sellers absorb the breakout buying.\\n\\n**Overnight Low Hunt**: QQQ drops in pre-market, sweeps the previous day's low, then rockets higher as smart money accumulates shares from panicked sellers.\\n        \",\n                type: \"theory\",\n                duration: \"18 min\",\n                keyPoints: [\n                    \"Liquidity sweeps are deliberate moves by institutions to grab stop orders\",\n                    \"Price seeks liquidity before establishing true direction\",\n                    \"Volume spikes and wicks are key indicators of sweeps\",\n                    \"SPY/QQQ provide excellent examples due to high institutional activity\"\n                ],\n                practicalExercises: [\n                    \"Identify 3 liquidity sweeps on a SPY daily chart from the past month\",\n                    \"Mark areas where stop orders likely cluster (equal highs/lows)\",\n                    \"Analyze volume patterns during suspected liquidity sweeps\"\n                ],\n                quiz: [\n                    {\n                        question: \"What is the primary purpose of a liquidity sweep?\",\n                        options: [\n                            \"To create volatility in the market\",\n                            \"To allow institutions to fill large orders using retail stop orders\",\n                            \"To test technical support and resistance levels\",\n                            \"To signal the start of a new trend\"\n                        ],\n                        correct: 1,\n                        explanation: \"Institutions use liquidity sweeps to fill large orders by triggering clusters of retail stop orders, providing them with the liquidity they need without moving the market against themselves.\"\n                    },\n                    {\n                        question: \"Which chart pattern typically indicates a liquidity sweep has occurred?\",\n                        options: [\n                            \"A long wick beyond a key level with quick reversal\",\n                            \"A strong breakout with high volume continuation\",\n                            \"A gradual move through resistance with steady volume\",\n                            \"A sideways consolidation pattern\"\n                        ],\n                        correct: 0,\n                        explanation: \"A long wick (or spike) beyond a key level followed by a quick reversal is the classic signature of a liquidity sweep, showing price briefly grabbed stops before reversing.\"\n                    },\n                    {\n                        question: \"According to the golden rule, what does price seek before establishing direction?\",\n                        options: [\n                            \"Technical confirmation\",\n                            \"Volume validation\",\n                            \"Liquidity\",\n                            \"Trend continuation\"\n                        ],\n                        correct: 2,\n                        explanation: \"The golden rule states that 'price seeks liquidity before direction' - meaning price will often move to areas where stop orders cluster before establishing its true directional bias.\"\n                    }\n                ]\n            },\n            {\n                id: 2,\n                title: \"Trading After Liquidity Sweeps: Reversal vs Continuation\",\n                description: \"Learn how to distinguish between false breakouts and genuine breakouts after liquidity sweeps, and how to trade both scenarios\",\n                content: \"\\n# Trading After Liquidity Sweeps: Reversal vs Continuation\\n\\nOnce a liquidity sweep has occurred, two scenarios are in play – a reversal (mean-reversion back into the range) or a successful breakout (continuation of the trend). Understanding which scenario is unfolding is crucial for profitable trading.\\n\\n## Reversal Setup: Stop Hunt and Rejection\\n\\nThis is the classic outcome of a liquidity sweep. After price wicks beyond the prior high/low and falls back, your bias flips in the opposite direction of the wick.\\n\\n### Identifying a Reversal Setup\\n\\n**1. The Wick Formation**\\n- Price briefly breaks a key level (previous high/low)\\n- Immediately reverses back within the prior range\\n- Creates a long wick or spike on the chart\\n- Often accompanied by high volume\\n\\n**2. Confirmation Signals**\\n- **Market Structure Break**: Look for a break of a short-term swing low (if a high was swept) or swing high (if a low was swept)\\n- **Candlestick Patterns**: Engulfing candles, pin bars, or doji formations at the sweep level\\n- **Volume Analysis**: Volume spike on the sweep followed by sustained volume on the reversal\\n\\n### Trading the Reversal\\n\\n**Entry Strategy:**\\n- Wait for confirmation before entering\\n- Don't trade the exact top/bottom of the sweep\\n- Enter on the structure break in the reversal direction\\n\\n**Example: SPY High Sweep Reversal**\\n1. SPY rallies and takes out yesterday's high by $0.50\\n2. Price immediately reverses with a long upper wick\\n3. Wait for SPY to break below a recent swing low (confirmation)\\n4. Enter short position (put options) with stop above the sweep high\\n5. Target the opposite side of the range or previous support\\n\\n## Continuation Setup: Breakout with Follow-Through\\n\\nNot every push through a previous high/low is a fake-out. Sometimes the market intends to run further, and the liquidity sweep is the ignition of a larger move.\\n\\n### Identifying a Continuation Setup\\n\\n**1. Strong Follow-Through**\\n- Price holds above the broken level (for upward breakouts)\\n- Candles close firmly in the breakout direction\\n- Only shallow pullbacks that stay above the old high (now support)\\n\\n**2. Volume Confirmation**\\n- Sustained volume on subsequent bars\\n- Not just a one-off stop trigger\\n- Continued participation showing genuine interest\\n\\n### Trading the Continuation\\n\\n**Entry Strategy:**\\n- Wait for a retest of the broken level\\n- Look for the old resistance to act as new support\\n- Enter on the bounce with stops below the retest low\\n\\n**Example: QQQ Low Sweep Continuation**\\n1. QQQ drops and sweeps the previous day's low\\n2. Price quickly recovers and closes above the old low\\n3. Wait for a pullback to retest the broken low as support\\n4. Enter long position (call options) on the bounce\\n5. Stop below the retest low, target higher resistance\\n\\n## The Professional Approach: Confirmation Stacking\\n\\nNever trade a liquidity sweep in isolation. Stack multiple confirmations:\\n\\n### Primary Confirmations\\n- **Price Action**: Structure breaks, candlestick patterns\\n- **Volume**: Spikes on sweeps, sustained volume on follow-through\\n- **Time**: How quickly the reversal/continuation occurs\\n\\n### Secondary Confirmations\\n- **Higher Timeframe Context**: Is this aligned with the bigger picture?\\n- **Market Sentiment**: Risk-on vs risk-off environment\\n- **Options Flow**: Unusual activity in puts/calls\\n\\n## Real-World Examples\\n\\n### SPY Reversal Example (Bearish)\\n- **Setup**: SPY approaches yesterday's high at $450\\n- **Sweep**: Price hits $450.50, creating a wick\\n- **Confirmation**: 5-minute bearish engulfing + break of swing low\\n- **Entry**: Short at $449.50 after confirmation\\n- **Result**: Drop to $445 for 1% profit\\n\\n### QQQ Continuation Example (Bullish)\\n- **Setup**: QQQ tests overnight low at $350\\n- **Sweep**: Brief drop to $349.80, then recovery\\n- **Confirmation**: Strong close above $350 + volume increase\\n- **Entry**: Long on retest of $350 support\\n- **Result**: Rally to $355 for 1.4% profit\\n\\n## Key Takeaways\\n\\n- Liquidity sweeps provide directional bias, not immediate entries\\n- Always wait for confirmation before trading\\n- Reversals are more common than continuations\\n- Stack multiple confirmations for higher probability setups\\n- Use proper risk management with stops beyond the sweep levels\\n        \",\n                type: \"strategy\",\n                duration: \"22 min\",\n                keyPoints: [\n                    \"Liquidity sweeps can lead to either reversals or continuations\",\n                    \"Confirmation is essential - never trade the sweep itself\",\n                    \"Reversal setups are more common than continuation setups\",\n                    \"Volume and price action provide the best confirmation signals\",\n                    \"Stack multiple confirmations for higher probability trades\"\n                ],\n                practicalExercises: [\n                    \"Identify 3 liquidity sweeps on SPY/QQQ charts and classify as reversal or continuation\",\n                    \"Practice waiting for confirmation signals before entering trades\",\n                    \"Analyze volume patterns during sweep reversals vs continuations\",\n                    \"Create a checklist of confirmation signals for your trading plan\"\n                ],\n                quiz: [\n                    {\n                        question: \"What should you do immediately after identifying a liquidity sweep?\",\n                        options: [\n                            \"Enter a trade in the opposite direction\",\n                            \"Enter a trade in the same direction\",\n                            \"Wait for confirmation signals\",\n                            \"Close all existing positions\"\n                        ],\n                        correct: 2,\n                        explanation: \"Never trade immediately after a liquidity sweep. Always wait for confirmation signals like structure breaks, volume confirmation, or candlestick patterns.\"\n                    },\n                    {\n                        question: \"Which scenario is more common after a liquidity sweep?\",\n                        options: [\n                            \"Continuation breakouts\",\n                            \"Reversal setups\",\n                            \"Sideways consolidation\",\n                            \"Gap formations\"\n                        ],\n                        correct: 1,\n                        explanation: \"Reversal setups are more common after liquidity sweeps because most sweeps are designed to grab stops and then reverse, not to signal genuine breakouts.\"\n                    },\n                    {\n                        question: \"What is the best confirmation for a liquidity sweep reversal?\",\n                        options: [\n                            \"A single large volume bar\",\n                            \"Price returning to the sweep level\",\n                            \"Market structure break in the opposite direction\",\n                            \"A gap in the opposite direction\"\n                        ],\n                        correct: 2,\n                        explanation: \"A market structure break (like breaking a swing low after a high sweep) provides the strongest confirmation that the reversal is genuine and not just a temporary pullback.\"\n                    }\n                ]\n            },\n            {\n                id: 3,\n                title: \"Zone Validation Techniques\",\n                description: \"How to validate the strength and reliability of your zones\",\n                content: \"Not all zones are created equal. Learn to identify the strongest...\",\n                type: \"practical\",\n                duration: \"12 min\",\n                keyPoints: [\n                    \"Volume confirmation at zones\",\n                    \"Multiple timeframe validation\",\n                    \"Age and frequency of tests\"\n                ]\n            },\n            {\n                id: 4,\n                title: \"Interactive Zone Drawing Exercise\",\n                description: \"Practice identifying and drawing zones on real SPY/QQQ charts\",\n                content: \"Apply your knowledge with guided practice on live market examples\",\n                type: \"interactive\",\n                duration: \"8 min\",\n                keyPoints: [\n                    \"Real-time chart analysis\",\n                    \"Immediate feedback on zone placement\",\n                    \"Common mistakes to avoid\"\n                ]\n            }\n        ]\n    },\n    {\n        id: 2,\n        title: \"Fair Value Gaps (FVGs) Mastery\",\n        description: \"Master Fair Value Gaps - the institutional footprints left by rapid price movements and how to trade them for consistent profits\",\n        icon: \"BarChart3\",\n        color: \"from-green-500 to-green-600\",\n        estimatedTime: \"80 minutes\",\n        difficulty: \"Intermediate\",\n        prerequisites: \"Understanding of liquidity sweeps and market structure\",\n        learningObjectives: [\n            \"Identify and mark Fair Value Gaps with 95% accuracy\",\n            \"Understand the difference between HTF and LTF FVGs\",\n            \"Trade FVG fills and rejections for high-probability setups\",\n            \"Master Inversion Fair Value Gaps (IFVGs) for advanced entries\",\n            \"Combine FVGs with liquidity sweeps for confluence trading\"\n        ],\n        lessons: [\n            {\n                id: 1,\n                title: \"Fair Value Gap Theory & Formation\",\n                description: \"Deep dive into FVG formation, institutional causes, and market inefficiency concepts\",\n                content: \"\\n# Fair Value Gaps: Institutional Footprints in Price Action\\n\\nA Fair Value Gap (FVG) is a price range on a chart where an inefficient move occurred – essentially, a section where little or no trading took place. These gaps represent areas where fair value may have temporarily changed, and markets tend to revert to fill these inefficiencies over time.\\n\\n## What is a Fair Value Gap?\\n\\n**Definition:**\\nA Fair Value Gap appears within a three-candle sequence where one large momentum candle creates a \\\"void\\\" between the wick of the first candle and the wick of the third candle. The second candle (the big move) is so large that the third candle's low (in an up move) is still above the first candle's high, leaving a gap in between.\\n\\n**Key Concept:**\\nThis gap represents a price area where fair value may have temporarily changed – price zoomed in one direction without adequate two-way trading at those levels.\\n\\n## The Three-Candle Rule\\n\\n### Bullish FVG Formation:\\n1. **Candle 1**: Creates a high at a certain level\\n2. **Candle 2**: Large bullish candle that gaps up significantly\\n3. **Candle 3**: Low is still above Candle 1's high\\n4. **Result**: Gap between Candle 1 high and Candle 3 low = Bullish FVG\\n\\n### Bearish FVG Formation:\\n1. **Candle 1**: Creates a low at a certain level\\n2. **Candle 2**: Large bearish candle that gaps down significantly\\n3. **Candle 3**: High is still below Candle 1's low\\n4. **Result**: Gap between Candle 1 low and Candle 3 high = Bearish FVG\\n\\n## Why Do FVGs Matter?\\n\\n### Market Inefficiency Theory\\n- FVGs highlight areas where price moved too fast\\n- Represent zones with insufficient two-way trading\\n- Markets have \\\"memory\\\" of unfilled orders in these ranges\\n- Price often returns to rebalance these inefficiencies\\n\\n### Institutional Perspective\\n- Large orders couldn't be filled during rapid moves\\n- Institutions may have unfilled orders in FVG zones\\n- Smart money often waits for price to return to these levels\\n- FVGs act like magnets for future price action\\n\\n## Types of Fair Value Gaps\\n\\n### 1. Bullish FVG (Buy Side Imbalance)\\n- **Formation**: Left by strong upward movement\\n- **Expectation**: Acts as support when price returns\\n- **Trading**: Look for buying opportunities on first touch\\n- **Invalidation**: Price closes below the gap\\n\\n### 2. Bearish FVG (Sell Side Imbalance)\\n- **Formation**: Left by strong downward movement\\n- **Expectation**: Acts as resistance when price returns\\n- **Trading**: Look for selling opportunities on first touch\\n- **Invalidation**: Price closes above the gap\\n\\n## SPY/QQQ FVG Characteristics\\n\\n### SPY FVG Patterns:\\n- Often form during earnings reactions\\n- News-driven gaps create large FVGs\\n- Options expiration can trigger FVG formation\\n- Market open gaps frequently leave FVGs\\n\\n### QQQ FVG Patterns:\\n- Tech sector news creates significant FVGs\\n- Higher volatility = larger gap formations\\n- After-hours trading often leaves gaps\\n- Correlation with NASDAQ futures gaps\\n\\n## FVG Validation Criteria\\n\\n### Strong FVGs Have:\\n1. **Clean Formation**: Clear three-candle pattern\\n2. **Significant Size**: Gap represents meaningful price range\\n3. **Volume Context**: High volume on the gap-creating candle\\n4. **Timeframe Relevance**: Higher timeframes = stronger FVGs\\n\\n### Weak FVGs Show:\\n- Overlapping wicks between candles\\n- Very small gap size\\n- Low volume on formation\\n- Multiple gaps in same area\\n\\n## Real-World FVG Example\\n\\n**SPY Bullish FVG Formation:**\\n1. **9:30 AM**: SPY opens at $450, creates high at $450.50\\n2. **9:31 AM**: Strong buying pushes SPY from $450.75 to $452.25\\n3. **9:32 AM**: Pullback finds support at $451.00\\n4. **Result**: Bullish FVG from $450.50 to $451.00\\n\\n**Expected Behavior:**\\n- Price may return to $450.50-$451.00 zone\\n- First touch often provides buying opportunity\\n- Zone acts as support for future moves\\n- Invalidated if price closes below $450.50\\n        \",\n                type: \"theory\",\n                duration: \"22 min\",\n                keyPoints: [\n                    \"Fair Value Gaps represent price inefficiencies where insufficient two-way trading occurred\",\n                    \"FVGs form through the three-candle rule with clear gap between first and third candle wicks\",\n                    \"Bullish FVGs act as support zones, bearish FVGs act as resistance zones\",\n                    \"Higher timeframe FVGs are more significant and reliable than lower timeframe gaps\",\n                    \"SPY/QQQ FVGs often form during news events, market opens, and earnings reactions\"\n                ],\n                practicalExercises: [\n                    \"Identify 5 Fair Value Gaps on SPY daily chart using the three-candle rule\",\n                    \"Mark bullish and bearish FVGs with different colors on your charts\",\n                    \"Observe how price reacts when returning to previously identified FVG zones\"\n                ],\n                quiz: [\n                    {\n                        question: \"What defines a valid Fair Value Gap formation?\",\n                        options: [\n                            \"Any gap between two candles\",\n                            \"A three-candle pattern where the middle candle creates a gap between the first and third candle wicks\",\n                            \"A gap that forms at market open\",\n                            \"Any price movement with high volume\"\n                        ],\n                        correct: 1,\n                        explanation: \"A valid FVG requires a three-candle pattern where the large middle candle creates a clear gap between the first candle's high/low and the third candle's low/high.\"\n                    },\n                    {\n                        question: \"How should a bullish FVG behave when price returns to it?\",\n                        options: [\n                            \"Price should break through immediately\",\n                            \"Price should act as resistance\",\n                            \"Price should find support and potentially bounce\",\n                            \"Price should create more gaps\"\n                        ],\n                        correct: 2,\n                        explanation: \"A bullish FVG should act as a support zone when price returns to it, as the gap represents an area where buyers may step in to fill the inefficiency.\"\n                    },\n                    {\n                        question: \"When is a Fair Value Gap considered invalidated?\",\n                        options: [\n                            \"After one touch\",\n                            \"When price closes through the gap completely\",\n                            \"After 24 hours\",\n                            \"When volume decreases\"\n                        ],\n                        correct: 1,\n                        explanation: \"An FVG is invalidated when price closes completely through the gap, indicating the inefficiency has been filled and the zone no longer holds significance.\"\n                    }\n                ]\n            },\n            {\n                id: 2,\n                title: \"Higher Timeframe FVGs & Multi-Timeframe Analysis\",\n                description: \"Master the power of Higher Timeframe Fair Value Gaps and learn to combine multiple timeframes for precision entries\",\n                content: \"\\n# Higher Timeframe FVGs: The Institutional Magnets\\n\\nHigher timeframe Fair Value Gaps are among the most powerful tools in a professional trader's arsenal. These gaps act like magnets, drawing price back to fill inefficiencies left by rapid institutional moves.\\n\\n## Why Higher Timeframes Matter\\n\\n### Significance Hierarchy:\\n- **Weekly FVGs**: Extremely powerful, may take months to fill\\n- **Daily FVGs**: Very significant, often filled within days/weeks\\n- **4-Hour FVGs**: Strong levels, usually filled within days\\n- **1-Hour FVGs**: Moderate significance, filled within hours/days\\n- **15-Min FVGs**: Lower significance, often filled quickly\\n\\n### Volume and Participation:\\nHigher timeframe moves involve:\\n- More institutional participation\\n- Larger order sizes\\n- Greater market impact\\n- Broader market awareness\\n- Stronger magnetic effect\\n\\n## HTF FVG vs LTF FVG Comparison\\n\\n### Higher Timeframe FVGs (Daily+):\\n**Advantages:**\\n- Higher probability of being filled\\n- Stronger support/resistance when reached\\n- Better risk/reward opportunities\\n- Less noise and false signals\\n- Institutional relevance\\n\\n**Characteristics:**\\n- Take longer to reach\\n- Provide major turning points\\n- Often align with other key levels\\n- Create significant price reactions\\n\\n### Lower Timeframe FVGs (1H and below):\\n**Advantages:**\\n- More frequent opportunities\\n- Faster fills and reactions\\n- Good for scalping strategies\\n- Quick feedback on trades\\n\\n**Disadvantages:**\\n- Higher noise ratio\\n- More false signals\\n- Weaker reactions\\n- Less institutional relevance\\n\\n## Multi-Timeframe FVG Analysis\\n\\n### The Professional Approach:\\n1. **Mark HTF FVGs first** (Daily, 4H, 1H)\\n2. **Use LTF for entry timing** (15M, 5M)\\n3. **Combine with other confluences**\\n4. **Prioritize HTF over LTF**\\n\\n### Confluence Stacking:\\n**High-Probability Setup:**\\n- Daily FVG zone\\n- + Previous day high/low\\n- + Volume profile level\\n- + Liquidity sweep area\\n- = Maximum confluence\\n\\n## SPY/QQQ HTF FVG Patterns\\n\\n### SPY Daily FVG Characteristics:\\n- **Formation**: Often during earnings, Fed announcements, major news\\n- **Size**: Typically $2-8 gaps on daily charts\\n- **Fill Rate**: 85-90% eventually get filled\\n- **Timeframe**: Usually filled within 1-4 weeks\\n- **Reaction**: Strong bounces/rejections on first touch\\n\\n### QQQ Daily FVG Characteristics:\\n- **Formation**: Tech earnings, guidance changes, sector rotation\\n- **Size**: Typically $3-12 gaps due to higher volatility\\n- **Fill Rate**: 80-85% eventually get filled\\n- **Timeframe**: May take longer due to trend strength\\n- **Reaction**: More volatile reactions, wider zones needed\\n\\n## Trading HTF FVGs: The Professional Method\\n\\n### Step 1: Identification\\n- Scan daily/4H charts for clean FVG formations\\n- Mark gap boundaries clearly\\n- Note the context (news, earnings, etc.)\\n- Assess gap size and significance\\n\\n### Step 2: Patience\\n- Wait for price to approach the HTF FVG\\n- Don't chase - let the gap come to you\\n- Monitor lower timeframes for entry signals\\n- Prepare for potential strong reactions\\n\\n### Step 3: Entry Timing\\n- Use 15M/5M charts for precise entries\\n- Look for additional confirmations:\\n  - Lower timeframe structure breaks\\n  - Volume increases\\n  - Candlestick patterns\\n  - Momentum divergences\\n\\n### Step 4: Risk Management\\n- Stop loss beyond the FVG zone\\n- Take profits at logical levels\\n- Trail stops as trade develops\\n- Respect the power of HTF levels\\n\\n## Real-World HTF FVG Example\\n\\n**SPY Daily Bullish FVG Setup:**\\n- **Formation**: Fed announcement creates gap from $445-$448\\n- **Wait Period**: 2 weeks for price to return\\n- **Entry Signal**: 15M bullish engulfing at $446 (within FVG)\\n- **Confirmation**: Volume spike + break of 15M structure\\n- **Result**: Bounce to $452 for 1.3% profit\\n- **Risk**: Stop at $444 (below FVG) for 0.4% risk\\n- **R:R Ratio**: 3.25:1\\n\\n## Key HTF FVG Rules\\n\\n1. **Higher timeframe always wins** - HTF FVG overrides LTF signals\\n2. **First touch is strongest** - Best reactions occur on initial contact\\n3. **Partial fills are common** - Price may only fill 50-70% of gap\\n4. **Context matters** - Consider overall market trend and sentiment\\n5. **Patience pays** - Wait for proper setups, don't force trades\\n        \",\n                type: \"practical\",\n                duration: \"25 min\",\n                keyPoints: [\n                    \"Perfect liquidity sweeps follow a 4-phase pattern: approach, penetration, reversal, follow-through\",\n                    \"SPY sweeps typically extend 0.1-0.3% beyond levels with 20-50% above average volume\",\n                    \"Visual recognition includes wick formations, volume spikes, and immediate reversals\",\n                    \"Time-based patterns show highest probability during market open and close\",\n                    \"Multi-timeframe analysis provides confirmation and precise entry timing\"\n                ],\n                practicalExercises: [\n                    \"Identify and analyze 3 historical liquidity sweeps on SPY using the 4-phase pattern\",\n                    \"Mark 5 potential liquidity levels on current QQQ chart and monitor for sweep patterns\",\n                    \"Practice distinguishing between true sweeps and genuine breakouts using volume analysis\",\n                    \"Create alerts for price approaching identified liquidity levels for real-time practice\"\n                ],\n                quiz: [\n                    {\n                        question: \"What is the typical penetration distance for SPY liquidity sweeps?\",\n                        options: [\n                            \"1-2% beyond the level\",\n                            \"0.1-0.3% beyond the level\",\n                            \"5-10% beyond the level\",\n                            \"Exactly to the level\"\n                        ],\n                        correct: 1,\n                        explanation: \"SPY liquidity sweeps typically penetrate 0.1-0.3% beyond key levels - enough to trigger stops but not so much as to indicate a genuine breakout.\"\n                    },\n                    {\n                        question: \"Which phase of a liquidity sweep shows the highest volume?\",\n                        options: [\n                            \"The approach phase\",\n                            \"The penetration phase\",\n                            \"The reversal phase\",\n                            \"The follow-through phase\"\n                        ],\n                        correct: 2,\n                        explanation: \"The reversal phase typically shows the highest volume as institutional orders enter the market after stops are triggered.\"\n                    }\n                ]\n            },\n            {\n                id: 3,\n                title: \"Trading Liquidity Sweeps\",\n                description: \"How to position yourself to profit from sweep reversals\",\n                content: \"Once you identify a liquidity sweep, the next step is positioning...\",\n                type: \"strategy\",\n                duration: \"18 min\",\n                keyPoints: [\n                    \"Entry timing after sweep completion\",\n                    \"Stop loss placement strategies\",\n                    \"Target setting for sweep trades\"\n                ]\n            },\n            {\n                id: 4,\n                title: \"Sweep Analysis Workshop\",\n                description: \"Analyze real SPY/QQQ liquidity sweeps with expert commentary\",\n                content: \"Review historical examples of successful sweep trades\",\n                type: \"interactive\",\n                duration: \"7 min\",\n                keyPoints: [\n                    \"Case study analysis\",\n                    \"Pattern recognition practice\",\n                    \"Risk management examples\"\n                ]\n            }\n        ]\n    },\n    {\n        id: 3,\n        title: \"Confirmation Stacking & Multi-Factor Analysis\",\n        description: \"Master the art of stacking multiple confirmations for high-probability trades. Learn to combine price action, volume, and technical analysis for professional-level precision.\",\n        icon: \"Layers\",\n        color: \"from-purple-500 to-purple-600\",\n        estimatedTime: \"85 minutes\",\n        difficulty: \"Advanced\",\n        prerequisites: \"Understanding of liquidity sweeps and Fair Value Gaps\",\n        learningObjectives: [\n            \"Stack 3+ confirmations for every trade setup\",\n            \"Master market structure confirmations (BOS/CHoCH)\",\n            \"Integrate volume profile and order flow analysis\",\n            \"Combine multiple timeframes for precision entries\",\n            \"Develop a systematic approach to trade validation\"\n        ],\n        lessons: [\n            {\n                id: 1,\n                title: \"Confirmation Stacking Fundamentals\",\n                description: \"Learn the professional approach to stacking multiple confirmations for high-probability trade setups\",\n                content: '\\n# Confirmation Stacking: The Professional Edge\\n\\nEven when you have a strong level or setup in mind (be it a liquidity sweep or an FVG), jumping in without confirmation can be risky. Confirmation stacking means waiting for multiple signals to line up in your favor before committing to a trade.\\n\\n## The Philosophy of Confluence\\n\\n**Core Principle:**\\nRather than relying on a single indicator or one pattern, you look for an agreement among several independent clues – what traders often call confluence. The idea is to filter out low-quality setups and only act when many things point to the same conclusion.\\n\\n**Think of it this way:**\\nEach confirmation is like a piece of a puzzle. One piece alone doesn\\'t show the whole picture, but when several pieces fit together, you have a clearer image of where price might go.\\n\\n## The Five Pillars of Confirmation\\n\\n### 1. Market Structure & Price Action\\nThis refers to analyzing how price swings (highs and lows) are behaving to confirm a trend change or continuation.\\n\\n**Break of Structure (BOS):**\\n- Price takes out a significant previous high or low in the direction of a trend\\n- Confirms that trend\\'s strength\\n- Shows institutional participation\\n\\n**Change of Character (CHoCH):**\\n- Early sign of a possible trend reversal\\n- First break of a minor swing level against the trend\\n- Indicates potential shift in market sentiment\\n\\n**Example:** If QQQ has been making higher highs and higher lows (uptrend) and then suddenly makes a lower low, that\\'s a bearish CHoCH signaling the uptrend may be done.\\n\\n### 2. Volume Profile & Range Context\\nVolume Profile shows how volume has been distributed at each price, giving insight into what prices the market deems \"fair\" vs \"extreme.\"\\n\\n**Key Elements:**\\n- **Point of Control (POC):** Price with highest traded volume\\n- **Value Area (VA):** Price range where ~70% of volume occurred\\n- **Value Area High/Low (VAH/VAL):** Boundaries of fair value\\n\\n**Application:** If SPY rejects from yesterday\\'s Value Area High after a liquidity sweep, that\\'s confluence supporting a reversal trade.\\n\\n### 3. Order Flow Tools (Advanced)\\nReal-time confirmation of what\\'s happening under the hood through futures DOM, time and sales, or heatmap platforms.\\n\\n**Signals to Watch:**\\n- Absorption of selling/buying at key levels\\n- Cumulative volume delta divergences\\n- Large limit orders on the book\\n- Aggressive vs passive order flow\\n\\n### 4. Indicators & Overlays\\nTraditional technical indicators can be part of your confirmation stack, especially ones that measure trend or mean reversion.\\n\\n**VWAP (Volume Weighted Average Price):**\\n- Intraday equilibrium level\\n- Reclaiming VWAP after a sweep adds confidence\\n- Acts as dynamic support/resistance\\n\\n**Moving Averages:**\\n- 21 EMA, 50 EMA for trend confirmation\\n- Dynamic support on pullbacks\\n- Confluence with other levels\\n\\n### 5. Liquidity & HTF Levels\\nCombining liquidity sweeps and HTF FVGs as part of confirmation checklist.\\n\\n**High-Probability Setup:**\\n- Liquidity sweep at HTF FVG\\n- + Market structure confirmation\\n- + Volume profile level\\n- + VWAP reclaim\\n- = Maximum confluence\\n\\n## The Professional Confirmation Checklist\\n\\n### Minimum Requirements:\\n**For Entry:** At least 2-3 solid confirmations\\n**For High-Conviction Trades:** 4+ confirmations aligned\\n\\n### Example Checklist:\\n1. ✓ Liquidity sweep occurred\\n2. ✓ Price action confirmation (CHoCH/BOS)\\n3. ✓ Volume profile level confluence\\n4. ✓ HTF FVG zone\\n5. ✓ VWAP reclaim/rejection\\n\\n## Real-World Confirmation Stacking Example\\n\\n### SPY Bearish Setup:\\n**Setup:** SPY approaches yesterday\\'s high at $450\\n\\n**Confirmations:**\\n1. **Liquidity Sweep:** Price hits $450.50, sweeps stops\\n2. **HTF Level:** Daily bearish FVG zone at $450-451\\n3. **Price Action:** 5-minute bearish engulfing + CHoCH\\n4. **Volume Profile:** Rejection from yesterday\\'s VAH\\n5. **Volume:** Spike on sweep, sustained on reversal\\n\\n**Entry:** Short at $449.50 after all confirmations align\\n**Stop:** $451 (above sweep high)\\n**Target:** $445 (previous support)\\n**Result:** 1% profit with 0.3% risk = 3.3:1 R/R\\n\\n## Avoiding Analysis Paralysis\\n\\n### Balance is Key:\\n- Too few confirmations = low probability\\n- Too many confirmations = missed opportunities\\n- Sweet spot: 2-3 strong confirmations\\n\\n### Weighting Confirmations:\\n**Primary (Must Have):**\\n- Price action signal (structure break)\\n- Key level confluence (liquidity/FVG)\\n\\n**Secondary (Nice to Have):**\\n- Volume confirmation\\n- Indicator alignment\\n- Higher timeframe context\\n\\n## Common Confirmation Mistakes\\n\\n1. **Forcing Confluence:** Seeing confirmations that aren\\'t really there\\n2. **Over-Analysis:** Requiring too many signals\\n3. **Ignoring Context:** Not considering overall market environment\\n4. **Static Thinking:** Not adapting to changing market conditions\\n5. **Confirmation Bias:** Only seeing signals that support your bias\\n- Unfilled orders create demand/supply\\n- Technical traders target gap fills\\n- Self-fulfilling prophecy effect\\n\\n## FVG vs. Regular Gaps\\n\\n### Fair Value Gaps:\\n- Formed by 3-candle pattern\\n- Represent order flow imbalance\\n- High probability of fill (70-80%)\\n- Can be traded in both directions\\n- Show institutional activity\\n\\n### Regular Price Gaps:\\n- Formed between sessions (overnight)\\n- Caused by news or events\\n- Lower probability of fill (40-60%)\\n- Often indicate trend continuation\\n- May not represent institutional flow\\n\\n## Psychological Aspects of FVG Trading\\n\\n### Institutional Perspective:\\n- \"We moved price too fast\"\\n- \"Need to fill remaining orders\"\\n- \"Better prices available in the gap\"\\n- \"Risk management requires rebalancing\"\\n\\n### Retail Perspective:\\n- \"Price gapped away from me\"\\n- \"I missed the move\"\\n- \"Will it come back?\"\\n- \"Should I chase or wait?\"\\n\\n## Real-World FVG Examples\\n\\n### Example 1: SPY Bullish FVG\\n**Setup:** Fed announcement creates buying surge\\n**Formation:** 3-candle bullish FVG at $445-$447\\n**Fill:** Price returns to gap 5 days later\\n**Outcome:** Perfect bounce from gap support\\n\\n### Example 2: QQQ Bearish FVG\\n**Setup:** Tech earnings disappointment\\n**Formation:** 3-candle bearish FVG at $380-$382\\n**Fill:** Price rallies to gap 2 weeks later\\n**Outcome:** Strong resistance at gap level\\n\\n## Advanced FVG Concepts\\n\\n### 1. Nested FVGs\\n- Multiple gaps within larger gaps\\n- Provide multiple trading opportunities\\n- Show sustained institutional activity\\n- Require careful order management\\n\\n### 2. FVG Clusters\\n- Multiple gaps in same price area\\n- Extremely high probability zones\\n- Often mark major support/resistance\\n- Institutional accumulation/distribution areas\\n\\n### 3. Partial vs. Full Fills\\n- **Partial Fill:** Price touches gap but doesn\\'t close it\\n- **Full Fill:** Price completely closes the gap\\n- **Overfill:** Price extends beyond the gap\\n- Each has different trading implications\\n\\n## Common FVG Mistakes\\n\\n1. **Trading Every Gap:** Not all FVGs are equal quality\\n2. **Ignoring Context:** Market structure matters\\n3. **Poor Risk Management:** Gaps can extend before filling\\n4. **Wrong Timeframe:** Match timeframe to trading style\\n5. **Emotional Trading:** FOMO on gap formations\\n        ',\n                type: \"theory\",\n                duration: \"25 min\",\n                keyPoints: [\n                    \"Fair Value Gaps represent institutional order flow imbalances created by overwhelming buying/selling pressure\",\n                    \"FVGs require exactly 3 candles with no overlap between outer candles' high/low\",\n                    \"SPY FVGs typically range 0.2-0.8% while QQQ ranges 0.3-1.2% due to higher volatility\",\n                    \"70-80% of FVGs get filled within 5-20 sessions as markets seek price efficiency\",\n                    \"Inversion FVGs become powerful support/resistance after being filled\"\n                ],\n                practicalExercises: [\n                    \"Identify 5 bullish and 5 bearish FVGs on SPY 30-minute chart from last month\",\n                    \"Measure the size of each FVG as percentage of price and compare to typical ranges\",\n                    \"Track which FVGs got filled and calculate the fill rate for your sample\",\n                    \"Practice distinguishing between FVGs and regular overnight gaps\"\n                ],\n                quiz: [\n                    {\n                        question: \"How many candles are required to form a Fair Value Gap?\",\n                        options: [\n                            \"2 candles\",\n                            \"3 candles\",\n                            \"4 candles\",\n                            \"5 candles\"\n                        ],\n                        correct: 1,\n                        explanation: \"A Fair Value Gap requires exactly 3 consecutive candles, with the middle candle creating the imbalance and no overlap between the outer candles.\"\n                    },\n                    {\n                        question: \"What percentage of Fair Value Gaps typically get filled?\",\n                        options: [\n                            \"30-40%\",\n                            \"50-60%\",\n                            \"70-80%\",\n                            \"90-100%\"\n                        ],\n                        correct: 2,\n                        explanation: \"Approximately 70-80% of Fair Value Gaps get filled as markets naturally seek price efficiency and institutional orders get completed.\"\n                    }\n                ]\n            },\n            {\n                id: 2,\n                title: \"FVG Classification System\",\n                description: \"Learn to classify FVGs by strength and probability\",\n                content: \"Not all FVGs are equal. Learn the classification system...\",\n                type: \"practical\",\n                duration: \"15 min\",\n                keyPoints: [\n                    \"High probability vs low probability gaps\",\n                    \"Size and context importance\",\n                    \"Multiple timeframe FVG analysis\"\n                ]\n            },\n            {\n                id: 3,\n                title: \"Trading FVG Fills\",\n                description: \"Strategies for trading when price returns to fill gaps\",\n                content: \"FVG fills often provide excellent trading opportunities...\",\n                type: \"strategy\",\n                duration: \"16 min\",\n                keyPoints: [\n                    \"Partial vs full gap fills\",\n                    \"Entry and exit strategies\",\n                    \"Combining FVGs with other confluences\"\n                ]\n            },\n            {\n                id: 4,\n                title: \"FVG Recognition Challenge\",\n                description: \"Test your ability to spot and classify FVGs in real-time\",\n                content: \"Interactive challenge to identify FVGs on live charts\",\n                type: \"interactive\",\n                duration: \"7 min\",\n                keyPoints: [\n                    \"Speed recognition drills\",\n                    \"Classification accuracy\",\n                    \"Real-time decision making\"\n                ]\n            }\n        ]\n    },\n    {\n        id: 4,\n        title: \"Volume Analysis & Confirmation\",\n        description: \"Use volume analysis to confirm your price action signals\",\n        icon: \"Activity\",\n        color: \"from-orange-500 to-orange-600\",\n        estimatedTime: \"40 minutes\",\n        difficulty: \"Beginner\",\n        lessons: [\n            {\n                id: 1,\n                title: \"Volume Fundamentals\",\n                description: \"Understanding volume and its relationship to price movement\",\n                content: \"Volume is the fuel that drives price movement...\",\n                type: \"theory\",\n                duration: \"10 min\",\n                keyPoints: [\n                    \"Volume precedes price\",\n                    \"Accumulation vs distribution patterns\",\n                    \"Volume profile concepts\"\n                ]\n            },\n            {\n                id: 2,\n                title: \"Volume at Key Levels\",\n                description: \"Analyzing volume behavior at support/resistance zones\",\n                content: \"How volume behaves at key levels tells us about market sentiment...\",\n                type: \"practical\",\n                duration: \"15 min\",\n                keyPoints: [\n                    \"Rising volume on approach to zones\",\n                    \"Fading volume and false breakouts\",\n                    \"Climactic volume patterns\"\n                ]\n            },\n            {\n                id: 3,\n                title: \"Volume Confirmation Strategies\",\n                description: \"Using volume to confirm your trading signals\",\n                content: \"Volume confirmation can significantly improve trade success rates...\",\n                type: \"strategy\",\n                duration: \"12 min\",\n                keyPoints: [\n                    \"Volume divergence signals\",\n                    \"Confirmation vs contradiction\",\n                    \"Multiple timeframe volume analysis\"\n                ]\n            },\n            {\n                id: 4,\n                title: \"Volume Analysis Practice\",\n                description: \"Practice reading volume patterns on SPY/QQQ charts\",\n                content: \"Hands-on practice with volume analysis techniques\",\n                type: \"interactive\",\n                duration: \"3 min\",\n                keyPoints: [\n                    \"Pattern recognition\",\n                    \"Signal confirmation practice\",\n                    \"Real-world application\"\n                ]\n            }\n        ]\n    },\n    {\n        id: 5,\n        title: \"Confirmation Stacking\",\n        description: \"Learn to stack multiple confirmations for high-probability trades\",\n        icon: \"Layers\",\n        color: \"from-red-500 to-red-600\",\n        estimatedTime: \"55 minutes\",\n        difficulty: \"Advanced\",\n        lessons: [\n            {\n                id: 1,\n                title: \"The Stacking Methodology\",\n                description: \"Understanding the concept of confirmation stacking\",\n                content: \"Confirmation stacking involves combining multiple technical signals...\",\n                type: \"theory\",\n                duration: \"12 min\",\n                keyPoints: [\n                    \"Quality over quantity in confirmations\",\n                    \"Weighted confirmation systems\",\n                    \"Avoiding analysis paralysis\"\n                ]\n            },\n            {\n                id: 2,\n                title: \"Building Your Stack\",\n                description: \"How to systematically build confirmation stacks\",\n                content: \"Learn the systematic approach to building robust confirmation stacks...\",\n                type: \"practical\",\n                duration: \"18 min\",\n                keyPoints: [\n                    \"Primary vs secondary confirmations\",\n                    \"Timeframe hierarchy\",\n                    \"Confluence zone identification\"\n                ]\n            },\n            {\n                id: 3,\n                title: \"Advanced Stacking Techniques\",\n                description: \"Professional-level confirmation stacking strategies\",\n                content: \"Advanced techniques used by professional traders...\",\n                type: \"strategy\",\n                duration: \"20 min\",\n                keyPoints: [\n                    \"Multi-timeframe stacking\",\n                    \"Intermarket confirmations\",\n                    \"Sentiment-based confirmations\"\n                ]\n            },\n            {\n                id: 4,\n                title: \"Stacking Mastery Challenge\",\n                description: \"Put your stacking skills to the test with complex scenarios\",\n                content: \"Advanced challenge scenarios to test your mastery\",\n                type: \"interactive\",\n                duration: \"5 min\",\n                keyPoints: [\n                    \"Complex scenario analysis\",\n                    \"Decision-making under pressure\",\n                    \"Professional-level execution\"\n                ]\n            }\n        ]\n    },\n    {\n        id: 6,\n        title: \"Risk Management & Psychology\",\n        description: \"Master the mental game and risk management for consistent profits\",\n        icon: \"Shield\",\n        color: \"from-indigo-500 to-indigo-600\",\n        estimatedTime: \"45 minutes\",\n        difficulty: \"Intermediate\",\n        lessons: [\n            {\n                id: 1,\n                title: \"Position Sizing Fundamentals\",\n                description: \"Calculate optimal position sizes for your account\",\n                content: \"Proper position sizing is the foundation of risk management...\",\n                type: \"theory\",\n                duration: \"12 min\",\n                keyPoints: [\n                    \"Risk percentage rules\",\n                    \"Account size considerations\",\n                    \"Volatility-adjusted sizing\"\n                ]\n            },\n            {\n                id: 2,\n                title: \"Stop Loss Strategies\",\n                description: \"Advanced stop loss placement and management techniques\",\n                content: \"Stop losses are your insurance policy in trading...\",\n                type: \"practical\",\n                duration: \"15 min\",\n                keyPoints: [\n                    \"Technical vs percentage stops\",\n                    \"Trailing stop strategies\",\n                    \"Stop loss psychology\"\n                ]\n            },\n            {\n                id: 3,\n                title: \"Trading Psychology Mastery\",\n                description: \"Develop the mental discipline required for consistent trading\",\n                content: \"Trading psychology often determines success more than technical skills...\",\n                type: \"strategy\",\n                duration: \"15 min\",\n                keyPoints: [\n                    \"Emotional regulation techniques\",\n                    \"Dealing with losses\",\n                    \"Maintaining discipline\"\n                ]\n            },\n            {\n                id: 4,\n                title: \"Psychology Assessment\",\n                description: \"Evaluate your trading psychology and identify areas for improvement\",\n                content: \"Self-assessment tools for trading psychology\",\n                type: \"interactive\",\n                duration: \"3 min\",\n                keyPoints: [\n                    \"Psychological profiling\",\n                    \"Weakness identification\",\n                    \"Improvement planning\"\n                ]\n            }\n        ]\n    },\n    {\n        id: 7,\n        title: \"Advanced Pattern Recognition\",\n        description: \"Identify complex patterns and market structures for professional-level trading\",\n        icon: \"Eye\",\n        color: \"from-teal-500 to-teal-600\",\n        estimatedTime: \"65 minutes\",\n        difficulty: \"Advanced\",\n        lessons: [\n            {\n                id: 1,\n                title: \"Complex Pattern Structures\",\n                description: \"Understanding advanced chart patterns and their implications\",\n                content: \"Advanced patterns often provide the highest probability setups...\",\n                type: \"theory\",\n                duration: \"18 min\",\n                keyPoints: [\n                    \"Multi-timeframe pattern analysis\",\n                    \"Pattern failure and continuation\",\n                    \"Context-dependent patterns\"\n                ]\n            },\n            {\n                id: 2,\n                title: \"Market Structure Shifts\",\n                description: \"Identifying when market structure changes and how to adapt\",\n                content: \"Market structure shifts signal major changes in sentiment...\",\n                type: \"practical\",\n                duration: \"20 min\",\n                keyPoints: [\n                    \"Break of structure signals\",\n                    \"Change of character patterns\",\n                    \"Trend transition identification\"\n                ]\n            },\n            {\n                id: 3,\n                title: \"Professional Pattern Trading\",\n                description: \"How professionals trade complex patterns for maximum profit\",\n                content: \"Professional trading strategies for advanced patterns...\",\n                type: \"strategy\",\n                duration: \"22 min\",\n                keyPoints: [\n                    \"Entry and exit optimization\",\n                    \"Risk-reward maximization\",\n                    \"Pattern-specific strategies\"\n                ]\n            },\n            {\n                id: 4,\n                title: \"Pattern Mastery Exam\",\n                description: \"Final examination of your pattern recognition abilities\",\n                content: \"Comprehensive test of all pattern recognition skills\",\n                type: \"interactive\",\n                duration: \"5 min\",\n                keyPoints: [\n                    \"Comprehensive pattern test\",\n                    \"Speed and accuracy assessment\",\n                    \"Professional certification\"\n                ]\n            }\n        ]\n    }\n];\nconst COURSE_ACHIEVEMENTS = [\n    {\n        id: \"first_lesson\",\n        title: \"Getting Started\",\n        description: \"Complete your first lesson\",\n        icon: \"Play\",\n        points: 10\n    },\n    {\n        id: \"first_module\",\n        title: \"Module Master\",\n        description: \"Complete your first module\",\n        icon: \"Award\",\n        points: 50\n    },\n    {\n        id: \"quiz_master\",\n        title: \"Quiz Master\",\n        description: \"Score 90% or higher on 5 quizzes\",\n        icon: \"Brain\",\n        points: 100\n    },\n    {\n        id: \"speed_learner\",\n        title: \"Speed Learner\",\n        description: \"Complete 3 lessons in one day\",\n        icon: \"Zap\",\n        points: 75\n    },\n    {\n        id: \"course_complete\",\n        title: \"Course Graduate\",\n        description: \"Complete the entire course\",\n        icon: \"GraduationCap\",\n        points: 500\n    }\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/data/courseData.js\n"));

/***/ })

});