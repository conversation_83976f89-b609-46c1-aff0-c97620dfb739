"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/data/courseData.js":
/*!********************************!*\
  !*** ./src/data/courseData.js ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   COURSE_ACHIEVEMENTS: function() { return /* binding */ COURSE_ACHIEVEMENTS; },\n/* harmony export */   COURSE_MODULES: function() { return /* binding */ COURSE_MODULES; }\n/* harmony export */ });\n/**\n * Advanced Price Action Course Data\n * Professional SPY/QQQ Trading Course - Liquidity Sweeps, FVGs, and Confirmation Stacking\n * Complete educational content with real trading strategies\n */ const COURSE_MODULES = [\n    {\n        id: 1,\n        title: \"Market Structure & Liquidity Fundamentals\",\n        description: \"Master the institutional approach to reading market structure. Learn how professional traders identify liquidity zones and predict price movement on SPY/QQQ.\",\n        icon: \"Target\",\n        color: \"from-blue-500 to-blue-600\",\n        estimatedTime: \"90 minutes\",\n        difficulty: \"Beginner\",\n        prerequisites: \"Basic understanding of candlestick charts and support/resistance levels\",\n        learningObjectives: [\n            \"Decode institutional market structure patterns with professional precision\",\n            \"Identify high-probability liquidity zones where smart money operates\",\n            \"Distinguish between genuine breakouts and institutional stop hunts\",\n            \"Apply market structure analysis to SPY/QQQ for consistent profits\",\n            \"Master the professional trader's mindset: 'liquidity drives direction'\"\n        ],\n        lessons: [\n            {\n                id: 1,\n                title: \"Institutional Market Structure: Reading the Smart Money Footprints\",\n                description: \"Discover how professional traders analyze market structure to predict institutional moves and identify high-probability trading opportunities\",\n                imageUrl: \"https://images.unsplash.com/photo-1611974789855-9c2a0a7236a3?w=800&h=400&fit=crop\",\n                content: \"\\n# Chapter 1: Institutional Market Structure Analysis\\n\\n*\\\"The market is a voting machine in the short run, but a weighing machine in the long run. Understanding who's voting and why they're weighing gives you the ultimate edge.\\\"* - Professional Trading Axiom\\n\\n---\\n\\n## Section 1.1: The Professional Trader's Perspective\\n\\n### What Separates Institutional Traders from Retail?\\n\\nProfessional institutional traders operate with a fundamentally different approach than retail traders. While retail traders often focus on indicators and patterns, institutions analyze **market structure** - the underlying framework that reveals where smart money is positioned and where they're likely to move next.\\n\\n**Key Institutional Advantages:**\\n- Access to order flow data showing real-time buying/selling pressure\\n- Understanding of where retail stops cluster (their liquidity targets)\\n- Ability to move markets through large position sizes\\n- Advanced risk management systems that retail traders lack\\n\\n### The Market Structure Hierarchy\\n\\nMarket structure operates on multiple levels, each providing different insights:\\n\\n**1. Primary Structure (Daily/Weekly)**\\n- Major swing highs and lows that define long-term trends\\n- Key institutional accumulation and distribution zones\\n- Primary support and resistance levels that matter to big money\\n\\n**2. Secondary Structure (4H/1H)**\\n- Intermediate swings that show institutional positioning\\n- Areas where institutions build or reduce positions\\n- Critical levels for swing trading opportunities\\n\\n**3. Tertiary Structure (15M/5M)**\\n- Short-term swings for precise entry and exit timing\\n- Scalping opportunities within larger institutional moves\\n- Fine-tuning entries for optimal risk/reward ratios\\n\\n---\\n\\n## Section 1.2: Market Structure Components\\n\\n### Higher Highs and Higher Lows (Uptrend Structure)\\n\\nIn a healthy uptrend, price creates a series of higher highs (HH) and higher lows (HL). This pattern indicates:\\n\\n- **Institutional Accumulation**: Smart money is building long positions\\n- **Retail FOMO**: Fear of missing out drives retail buying at higher prices\\n- **Momentum Continuation**: Each pullback finds buyers at higher levels\\n\\n**Professional Trading Insight**: The quality of higher lows is more important than higher highs. Strong higher lows with volume support indicate institutional backing.\\n\\n### Lower Highs and Lower Lows (Downtrend Structure)\\n\\nIn a bearish market structure, price creates lower highs (LH) and lower lows (LL):\\n\\n- **Institutional Distribution**: Smart money is reducing long positions or building shorts\\n- **Retail Denial**: Retail traders often buy \\\"dips\\\" that continue falling\\n- **Momentum Breakdown**: Each rally fails at lower levels\\n\\n### Sideways Structure (Accumulation/Distribution)\\n\\nWhen price moves sideways between defined levels:\\n\\n- **Accumulation Phase**: Institutions quietly build positions before markup\\n- **Distribution Phase**: Institutions exit positions before markdown\\n- **Retail Confusion**: Sideways action frustrates retail traders into poor decisions\\n\\n---\\n\\n## Section 1.3: SPY/QQQ Specific Characteristics\\n\\n### SPY (S&P 500 ETF) Structure Patterns\\n\\n**Market Hours Behavior:**\\n- **9:30-10:30 AM**: Initial balance formation, often with false breakouts\\n- **10:30 AM-3:00 PM**: Trending moves or range-bound consolidation\\n- **3:00-4:00 PM**: Institutional positioning for overnight holds\\n\\n**Key SPY Levels:**\\n- **Psychological Levels**: Round numbers (400, 450, 500) act as magnets\\n- **Previous Day Extremes**: High/low from prior session are critical\\n- **Weekly/Monthly Levels**: Major institutional reference points\\n\\n### QQQ (Nasdaq 100 ETF) Structure Patterns\\n\\n**Technology Sector Sensitivity:**\\n- More volatile than SPY due to growth stock concentration\\n- Reacts strongly to tech earnings and guidance changes\\n- Higher beta creates larger structure swings\\n\\n**QQQ-Specific Considerations:**\\n- **After-hours Impact**: Tech stocks trade actively after market close\\n- **Correlation Breaks**: Sometimes diverges from SPY during sector rotation\\n- **Momentum Extremes**: Can extend further than SPY in both directions\\n\\n---\\n\\n## Section 1.4: Professional Structure Analysis Techniques\\n\\n### The Three-Timeframe Approach\\n\\n**1. Higher Timeframe Context (Daily/4H)**\\n- Identifies the primary trend direction\\n- Shows major institutional positioning\\n- Provides overall market bias for trading decisions\\n\\n**2. Entry Timeframe Analysis (1H/30M)**\\n- Pinpoints specific entry and exit opportunities\\n- Shows intermediate structure breaks and confirmations\\n- Balances precision with broader market context\\n\\n**3. Execution Timeframe Precision (15M/5M)**\\n- Fine-tunes exact entry and exit points\\n- Manages risk with tight stop-loss placement\\n- Maximizes risk/reward ratios through precise timing\\n\\n### Structure Quality Assessment\\n\\n**Strong Structure Characteristics:**\\n- Clear, well-defined swing points with significant price separation\\n- Volume confirmation at key structural levels\\n- Multiple timeframe alignment showing consistent patterns\\n- Clean breaks with follow-through momentum\\n\\n**Weak Structure Warning Signs:**\\n- Overlapping swing points creating confusion\\n- Low volume at critical structural levels\\n- Conflicting signals across different timeframes\\n- Failed breaks with immediate reversals\\n\\n---\\n\\n## Section 1.5: Practical Application Framework\\n\\n### Daily Structure Analysis Routine\\n\\n**Morning Preparation (Pre-Market):**\\n1. Identify overnight structure changes in SPY/QQQ\\n2. Mark key levels from previous session's structure\\n3. Note any gaps or significant news that might affect structure\\n4. Plan potential scenarios based on structure analysis\\n\\n**Intraday Monitoring:**\\n1. Track real-time structure development\\n2. Identify structure breaks as they occur\\n3. Assess the quality and follow-through of breaks\\n4. Adjust trading bias based on evolving structure\\n\\n**End-of-Day Review:**\\n1. Analyze how structure played out during the session\\n2. Identify successful and failed structure predictions\\n3. Update key levels for next trading session\\n4. Document lessons learned for continuous improvement\\n\\n### Risk Management Through Structure\\n\\n**Structure-Based Stop Placement:**\\n- Place stops beyond significant structure levels\\n- Use structure to determine position sizing\\n- Adjust stops as structure evolves intraday\\n\\n**Profit Target Selection:**\\n- Target next significant structure level\\n- Use structure to determine risk/reward ratios\\n- Scale out at multiple structure-based targets\\n        \",\n                type: \"foundational\",\n                duration: \"35 min\",\n                sections: [\n                    {\n                        title: \"The Professional Trader's Perspective\",\n                        duration: \"8 min\",\n                        keyPoints: [\n                            \"Institutional vs retail trading approaches\",\n                            \"Market structure hierarchy and timeframe analysis\",\n                            \"Professional advantages in market analysis\"\n                        ]\n                    },\n                    {\n                        title: \"Market Structure Components\",\n                        duration: \"12 min\",\n                        keyPoints: [\n                            \"Higher highs/higher lows in uptrend analysis\",\n                            \"Lower highs/lower lows in downtrend identification\",\n                            \"Sideways structure and accumulation/distribution phases\"\n                        ]\n                    },\n                    {\n                        title: \"SPY/QQQ Specific Analysis\",\n                        duration: \"10 min\",\n                        keyPoints: [\n                            \"SPY market hours behavior and key levels\",\n                            \"QQQ technology sector sensitivity patterns\",\n                            \"ETF-specific structure characteristics\"\n                        ]\n                    },\n                    {\n                        title: \"Professional Application Framework\",\n                        duration: \"5 min\",\n                        keyPoints: [\n                            \"Three-timeframe analysis approach\",\n                            \"Daily structure analysis routine\",\n                            \"Risk management through structure\"\n                        ]\n                    }\n                ],\n                keyPoints: [\n                    \"Market structure reveals institutional positioning and smart money flow\",\n                    \"Three-timeframe analysis provides context, entry signals, and execution precision\",\n                    \"SPY/QQQ structure patterns differ due to sector composition and volatility\",\n                    \"Professional traders use structure for risk management and profit targeting\",\n                    \"Quality structure assessment separates high-probability from low-probability setups\"\n                ],\n                practicalExercises: [\n                    \"Analyze current SPY daily chart and identify primary, secondary, and tertiary structure levels\",\n                    \"Compare SPY vs QQQ structure patterns over the past week and note differences\",\n                    \"Practice the three-timeframe approach: Daily context → 1H entries → 15M execution\",\n                    \"Create a daily structure analysis routine and apply it for one full trading week\"\n                ],\n                quiz: [\n                    {\n                        question: \"What is the most important difference between institutional and retail trading approaches?\",\n                        options: [\n                            \"Institutions use more indicators and technical analysis tools\",\n                            \"Institutions focus on market structure while retail focuses on indicators\",\n                            \"Institutions trade larger position sizes with more capital\",\n                            \"Institutions have access to better charting software\"\n                        ],\n                        correct: 1,\n                        explanation: \"The fundamental difference is analytical approach: institutions analyze market structure to understand where smart money is positioned, while retail traders typically rely on lagging indicators and patterns.\"\n                    },\n                    {\n                        question: \"In the three-timeframe approach, what is the primary purpose of the higher timeframe analysis?\",\n                        options: [\n                            \"To find exact entry and exit points\",\n                            \"To identify the primary trend direction and institutional positioning\",\n                            \"To determine precise stop-loss placement\",\n                            \"To calculate position sizing for trades\"\n                        ],\n                        correct: 1,\n                        explanation: \"Higher timeframe analysis (Daily/4H) provides the overall market context, showing the primary trend direction and major institutional positioning that guides all trading decisions.\"\n                    },\n                    {\n                        question: \"What makes QQQ structure analysis different from SPY?\",\n                        options: [\n                            \"QQQ has lower volatility and smaller price movements\",\n                            \"QQQ only trades during regular market hours\",\n                            \"QQQ is more sensitive to technology sector news and has higher volatility\",\n                            \"QQQ structure patterns are identical to SPY patterns\"\n                        ],\n                        correct: 2,\n                        explanation: \"QQQ's concentration in technology stocks makes it more volatile than SPY and highly sensitive to tech sector news, earnings, and guidance changes, creating different structure patterns.\"\n                    },\n                    {\n                        question: \"What characterizes 'strong structure' in professional analysis?\",\n                        options: [\n                            \"Many overlapping swing points with frequent reversals\",\n                            \"Clear, well-defined swing points with volume confirmation and timeframe alignment\",\n                            \"Low volume at structural levels with minimal price separation\",\n                            \"Conflicting signals across different timeframes\"\n                        ],\n                        correct: 1,\n                        explanation: \"Strong structure features clear swing points with significant price separation, volume confirmation at key levels, multiple timeframe alignment, and clean breaks with follow-through.\"\n                    },\n                    {\n                        question: \"During SPY's typical trading day, when do the most significant institutional positioning moves occur?\",\n                        options: [\n                            \"During the first 30 minutes after market open\",\n                            \"During the lunch hour (12:00-1:00 PM)\",\n                            \"During the final hour (3:00-4:00 PM) for overnight positioning\",\n                            \"Institutional moves are evenly distributed throughout the day\"\n                        ],\n                        correct: 2,\n                        explanation: \"The final trading hour (3:00-4:00 PM) is when institutions make their most significant positioning moves, preparing for overnight holds and next-day strategies.\"\n                    }\n                ]\n            },\n            {\n                id: 2,\n                title: \"Liquidity Sweeps: The Professional's Guide to Reading Institutional Moves\",\n                description: \"Master the art of identifying and trading liquidity sweeps - the institutional strategy that moves markets and creates the highest probability trading opportunities\",\n                imageUrl: \"https://images.unsplash.com/photo-1590283603385-17ffb3a7f29f?w=800&h=400&fit=crop\",\n                content: \"\\n# Chapter 2: Liquidity Sweeps - Decoding Institutional Market Manipulation\\n\\n*\\\"The best trades come from understanding where the weak hands are positioned and how the strong hands will exploit them.\\\"* - Professional Trading Principle\\n\\n---\\n\\n## Section 2.1: The Institutional Liquidity Strategy\\n\\n### What Are Liquidity Sweeps?\\n\\nA **liquidity sweep** is a deliberate institutional strategy where large market participants drive price through obvious levels to trigger clusters of retail stop orders. This isn't random market movement - it's calculated exploitation of predictable retail behavior.\\n\\n**The Institutional Advantage:**\\n- Institutions know where retail stops cluster (obvious levels)\\n- They have the capital to move price temporarily\\n- They use retail liquidity to fill their large orders\\n- They profit from the subsequent reversal\\n\\n### Why Institutions Need Liquidity\\n\\n**The Large Order Problem:**\\nWhen institutions need to buy or sell millions of dollars worth of stock, they face a critical challenge:\\n\\n- **Market Impact**: Large orders move prices against them\\n- **Slippage**: Poor fills reduce profitability\\n- **Visibility**: Other institutions can front-run their orders\\n- **Timing**: They need liquidity when they want it, not when it's naturally available\\n\\n**The Liquidity Solution:**\\nBy triggering retail stops, institutions create artificial liquidity pools they can exploit for better fills.\\n\\n---\\n\\n## Section 2.2: The Anatomy of a Professional Liquidity Sweep\\n\\n### Phase 1: The Setup (Accumulation)\\n**Duration**: Hours to days\\n**Characteristics**:\\n- Price consolidates near a key level\\n- Retail traders place obvious stops just beyond the level\\n- Institutional algorithms identify the stop clusters\\n- Volume decreases as the setup develops\\n\\n**Professional Insight**: The longer the consolidation, the more stops accumulate, creating a larger liquidity pool for institutions to exploit.\\n\\n### Phase 2: The Hunt (Execution)\\n**Duration**: Minutes to hours\\n**Characteristics**:\\n- Price accelerates toward the target level\\n- Momentum builds as breakout traders join\\n- Volume increases as the level approaches\\n- Retail FOMO (fear of missing out) intensifies\\n\\n**Key Indicators**:\\n- Sudden volume spikes without news catalysts\\n- Price acceleration into obvious levels\\n- Breakout confirmation signals triggering\\n\\n### Phase 3: The Sweep (Liquidity Grab)\\n**Duration**: Seconds to minutes\\n**Characteristics**:\\n- Price penetrates the level by a small margin (typically 0.1-0.3% for SPY)\\n- Massive volume spike as stops trigger\\n- Institutions absorb the triggered orders\\n- Price immediately stalls or reverses\\n\\n**Critical Measurements**:\\n- **SPY Penetration**: Usually $0.50-$1.50 beyond level\\n- **QQQ Penetration**: Usually $0.75-$2.25 beyond level\\n- **Volume Spike**: 200-500% of average volume\\n- **Time Duration**: Rarely sustains beyond 5-15 minutes\\n\\n### Phase 4: The Reversal (Institutional Profit)\\n**Duration**: Minutes to hours\\n**Characteristics**:\\n- Sharp reversal back through the swept level\\n- Higher volume than the initial sweep\\n- Sustained momentum in the reversal direction\\n- Retail traders trapped in losing positions\\n\\n**Professional Recognition**:\\n- Volume on reversal exceeds sweep volume\\n- Price moves decisively away from swept level\\n- Previous support becomes resistance (or vice versa)\\n- Follow-through confirms institutional participation\\n\\n---\\n\\n## Section 2.3: SPY/QQQ Specific Sweep Patterns\\n\\n### SPY Liquidity Characteristics\\n\\n**High-Probability Sweep Levels:**\\n- **Previous Day High/Low**: Most reliable sweep targets\\n- **Round Numbers**: $400, $450, $500 act as psychological magnets\\n- **Weekly/Monthly Extremes**: Major institutional reference points\\n- **Gap Levels**: Overnight gaps create obvious targets\\n\\n**SPY Sweep Timing Patterns:**\\n- **9:30-10:00 AM**: Morning volatility creates sweep opportunities\\n- **11:30 AM-12:30 PM**: Pre-lunch positioning sweeps\\n- **3:00-4:00 PM**: End-of-day institutional positioning\\n- **Economic Releases**: News-driven sweeps during data releases\\n\\n### QQQ Unique Sweep Characteristics\\n\\n**Technology Sector Amplification:**\\n- Higher volatility creates larger sweep distances\\n- After-hours news impacts next-day sweep probability\\n- Earnings season increases sweep frequency\\n- Growth stock sensitivity amplifies reversal moves\\n\\n**QQQ-Specific Patterns:**\\n- **Tech Earnings Sweeps**: Pre/post earnings volatility\\n- **Fed Meeting Sweeps**: Interest rate sensitivity\\n- **Sector Rotation Sweeps**: Growth vs value transitions\\n- **Options Expiration Sweeps**: Monthly/weekly expiry effects\\n        \",\n                type: \"advanced\",\n                duration: \"40 min\",\n                sections: [\n                    {\n                        title: \"The Institutional Liquidity Strategy\",\n                        duration: \"10 min\",\n                        keyPoints: [\n                            \"Understanding why institutions need liquidity sweeps\",\n                            \"The large order problem and institutional solutions\",\n                            \"How institutions exploit predictable retail behavior\"\n                        ]\n                    },\n                    {\n                        title: \"Anatomy of Professional Liquidity Sweeps\",\n                        duration: \"15 min\",\n                        keyPoints: [\n                            \"Four-phase sweep analysis: Setup, Hunt, Sweep, Reversal\",\n                            \"Critical measurements for SPY/QQQ sweep identification\",\n                            \"Professional recognition techniques and timing\"\n                        ]\n                    },\n                    {\n                        title: \"SPY/QQQ Specific Sweep Patterns\",\n                        duration: \"15 min\",\n                        keyPoints: [\n                            \"High-probability sweep levels and timing patterns\",\n                            \"Technology sector amplification in QQQ sweeps\",\n                            \"Market hours behavior and institutional positioning\"\n                        ]\n                    }\n                ],\n                keyPoints: [\n                    \"Liquidity sweeps are calculated institutional strategies, not random market movements\",\n                    \"Four-phase anatomy: Setup → Hunt → Sweep → Reversal provides professional framework\",\n                    \"SPY penetrations typically $0.50-$1.50, QQQ $0.75-$2.25 beyond key levels\",\n                    \"Volume spikes of 200-500% average confirm institutional participation\",\n                    \"Timing patterns reveal institutional positioning strategies throughout trading day\"\n                ],\n                practicalExercises: [\n                    \"Identify and analyze 5 liquidity sweeps on SPY using the four-phase framework\",\n                    \"Measure penetration distances and volume spikes on recent QQQ sweeps\",\n                    \"Track sweep timing patterns during different market hours for one week\",\n                    \"Create alerts for high-probability sweep levels based on previous day extremes\"\n                ],\n                quiz: [\n                    {\n                        question: \"What is the primary reason institutions execute liquidity sweeps?\",\n                        options: [\n                            \"To create market volatility for profit\",\n                            \"To trigger retail stops and use that liquidity for large order fills\",\n                            \"To test technical support and resistance levels\",\n                            \"To signal major trend changes to other institutions\"\n                        ],\n                        correct: 1,\n                        explanation: \"Institutions execute liquidity sweeps to solve the large order problem - they trigger retail stops to create artificial liquidity pools they can use for better fills on their large orders.\"\n                    },\n                    {\n                        question: \"What is the typical penetration distance for SPY liquidity sweeps?\",\n                        options: [\n                            \"$0.10-$0.25 beyond the level\",\n                            \"$0.50-$1.50 beyond the level\",\n                            \"$2.00-$3.00 beyond the level\",\n                            \"$5.00+ beyond the level\"\n                        ],\n                        correct: 1,\n                        explanation: \"SPY liquidity sweeps typically penetrate $0.50-$1.50 beyond key levels - enough to trigger stops but not so much as to indicate a genuine breakout attempt.\"\n                    },\n                    {\n                        question: \"During which phase of a liquidity sweep does the highest volume typically occur?\",\n                        options: [\n                            \"Phase 1: The Setup\",\n                            \"Phase 2: The Hunt\",\n                            \"Phase 3: The Sweep\",\n                            \"Phase 4: The Reversal\"\n                        ],\n                        correct: 3,\n                        explanation: \"Phase 4 (The Reversal) typically shows the highest volume as institutional orders enter the market after stops are triggered, often exceeding the volume from the initial sweep.\"\n                    },\n                    {\n                        question: \"What makes QQQ sweeps different from SPY sweeps?\",\n                        options: [\n                            \"QQQ sweeps are smaller and less volatile\",\n                            \"QQQ sweeps only occur during regular trading hours\",\n                            \"QQQ sweeps have larger penetration distances due to higher volatility\",\n                            \"QQQ sweeps are less reliable than SPY sweeps\"\n                        ],\n                        correct: 2,\n                        explanation: \"QQQ's higher volatility due to technology sector concentration creates larger sweep penetration distances ($0.75-$2.25 vs SPY's $0.50-$1.50) and more dramatic reversal moves.\"\n                    },\n                    {\n                        question: \"When do the highest probability liquidity sweeps typically occur on SPY?\",\n                        options: [\n                            \"During overnight trading sessions\",\n                            \"During lunch hours (12:00-1:00 PM)\",\n                            \"During morning volatility (9:30-10:00 AM) and end-of-day positioning (3:00-4:00 PM)\",\n                            \"Sweeps occur randomly throughout the trading day\"\n                        ],\n                        correct: 2,\n                        explanation: \"The highest probability sweeps occur during morning volatility (9:30-10:00 AM) when stops accumulate overnight, and during end-of-day positioning (3:00-4:00 PM) when institutions position for overnight holds.\"\n                    }\n                ]\n            },\n            {\n                id: 3,\n                title: \"Zone Validation Techniques\",\n                description: \"How to validate the strength and reliability of your zones\",\n                content: \"Not all zones are created equal. Learn to identify the strongest...\",\n                type: \"practical\",\n                duration: \"12 min\",\n                keyPoints: [\n                    \"Volume confirmation at zones\",\n                    \"Multiple timeframe validation\",\n                    \"Age and frequency of tests\"\n                ]\n            },\n            {\n                id: 4,\n                title: \"Interactive Zone Drawing Exercise\",\n                description: \"Practice identifying and drawing zones on real SPY/QQQ charts\",\n                content: \"Apply your knowledge with guided practice on live market examples\",\n                type: \"interactive\",\n                duration: \"8 min\",\n                keyPoints: [\n                    \"Real-time chart analysis\",\n                    \"Immediate feedback on zone placement\",\n                    \"Common mistakes to avoid\"\n                ]\n            }\n        ]\n    },\n    {\n        id: 2,\n        title: \"Fair Value Gaps (FVGs) Mastery\",\n        description: \"Master Fair Value Gaps - the institutional footprints left by rapid price movements and how to trade them for consistent profits\",\n        icon: \"BarChart3\",\n        color: \"from-green-500 to-green-600\",\n        estimatedTime: \"80 minutes\",\n        difficulty: \"Intermediate\",\n        prerequisites: \"Understanding of liquidity sweeps and market structure\",\n        learningObjectives: [\n            \"Identify and mark Fair Value Gaps with 95% accuracy\",\n            \"Understand the difference between HTF and LTF FVGs\",\n            \"Trade FVG fills and rejections for high-probability setups\",\n            \"Master Inversion Fair Value Gaps (IFVGs) for advanced entries\",\n            \"Combine FVGs with liquidity sweeps for confluence trading\"\n        ],\n        lessons: [\n            {\n                id: 1,\n                title: \"Fair Value Gap Theory & Formation\",\n                description: \"Deep dive into FVG formation, institutional causes, and market inefficiency concepts\",\n                content: \"\\n# Fair Value Gaps: Institutional Footprints in Price Action\\n\\nA Fair Value Gap (FVG) is a price range on a chart where an inefficient move occurred – essentially, a section where little or no trading took place. These gaps represent areas where fair value may have temporarily changed, and markets tend to revert to fill these inefficiencies over time.\\n\\n## What is a Fair Value Gap?\\n\\n**Definition:**\\nA Fair Value Gap appears within a three-candle sequence where one large momentum candle creates a \\\"void\\\" between the wick of the first candle and the wick of the third candle. The second candle (the big move) is so large that the third candle's low (in an up move) is still above the first candle's high, leaving a gap in between.\\n\\n**Key Concept:**\\nThis gap represents a price area where fair value may have temporarily changed – price zoomed in one direction without adequate two-way trading at those levels.\\n\\n## The Three-Candle Rule\\n\\n### Bullish FVG Formation:\\n1. **Candle 1**: Creates a high at a certain level\\n2. **Candle 2**: Large bullish candle that gaps up significantly\\n3. **Candle 3**: Low is still above Candle 1's high\\n4. **Result**: Gap between Candle 1 high and Candle 3 low = Bullish FVG\\n\\n### Bearish FVG Formation:\\n1. **Candle 1**: Creates a low at a certain level\\n2. **Candle 2**: Large bearish candle that gaps down significantly\\n3. **Candle 3**: High is still below Candle 1's low\\n4. **Result**: Gap between Candle 1 low and Candle 3 high = Bearish FVG\\n\\n## Why Do FVGs Matter?\\n\\n### Market Inefficiency Theory\\n- FVGs highlight areas where price moved too fast\\n- Represent zones with insufficient two-way trading\\n- Markets have \\\"memory\\\" of unfilled orders in these ranges\\n- Price often returns to rebalance these inefficiencies\\n\\n### Institutional Perspective\\n- Large orders couldn't be filled during rapid moves\\n- Institutions may have unfilled orders in FVG zones\\n- Smart money often waits for price to return to these levels\\n- FVGs act like magnets for future price action\\n\\n## Types of Fair Value Gaps\\n\\n### 1. Bullish FVG (Buy Side Imbalance)\\n- **Formation**: Left by strong upward movement\\n- **Expectation**: Acts as support when price returns\\n- **Trading**: Look for buying opportunities on first touch\\n- **Invalidation**: Price closes below the gap\\n\\n### 2. Bearish FVG (Sell Side Imbalance)\\n- **Formation**: Left by strong downward movement\\n- **Expectation**: Acts as resistance when price returns\\n- **Trading**: Look for selling opportunities on first touch\\n- **Invalidation**: Price closes above the gap\\n\\n## SPY/QQQ FVG Characteristics\\n\\n### SPY FVG Patterns:\\n- Often form during earnings reactions\\n- News-driven gaps create large FVGs\\n- Options expiration can trigger FVG formation\\n- Market open gaps frequently leave FVGs\\n\\n### QQQ FVG Patterns:\\n- Tech sector news creates significant FVGs\\n- Higher volatility = larger gap formations\\n- After-hours trading often leaves gaps\\n- Correlation with NASDAQ futures gaps\\n\\n## FVG Validation Criteria\\n\\n### Strong FVGs Have:\\n1. **Clean Formation**: Clear three-candle pattern\\n2. **Significant Size**: Gap represents meaningful price range\\n3. **Volume Context**: High volume on the gap-creating candle\\n4. **Timeframe Relevance**: Higher timeframes = stronger FVGs\\n\\n### Weak FVGs Show:\\n- Overlapping wicks between candles\\n- Very small gap size\\n- Low volume on formation\\n- Multiple gaps in same area\\n\\n## Real-World FVG Example\\n\\n**SPY Bullish FVG Formation:**\\n1. **9:30 AM**: SPY opens at $450, creates high at $450.50\\n2. **9:31 AM**: Strong buying pushes SPY from $450.75 to $452.25\\n3. **9:32 AM**: Pullback finds support at $451.00\\n4. **Result**: Bullish FVG from $450.50 to $451.00\\n\\n**Expected Behavior:**\\n- Price may return to $450.50-$451.00 zone\\n- First touch often provides buying opportunity\\n- Zone acts as support for future moves\\n- Invalidated if price closes below $450.50\\n        \",\n                type: \"theory\",\n                duration: \"22 min\",\n                keyPoints: [\n                    \"Fair Value Gaps represent price inefficiencies where insufficient two-way trading occurred\",\n                    \"FVGs form through the three-candle rule with clear gap between first and third candle wicks\",\n                    \"Bullish FVGs act as support zones, bearish FVGs act as resistance zones\",\n                    \"Higher timeframe FVGs are more significant and reliable than lower timeframe gaps\",\n                    \"SPY/QQQ FVGs often form during news events, market opens, and earnings reactions\"\n                ],\n                practicalExercises: [\n                    \"Identify 5 Fair Value Gaps on SPY daily chart using the three-candle rule\",\n                    \"Mark bullish and bearish FVGs with different colors on your charts\",\n                    \"Observe how price reacts when returning to previously identified FVG zones\"\n                ],\n                quiz: [\n                    {\n                        question: \"What defines a valid Fair Value Gap formation?\",\n                        options: [\n                            \"Any gap between two candles\",\n                            \"A three-candle pattern where the middle candle creates a gap between the first and third candle wicks\",\n                            \"A gap that forms at market open\",\n                            \"Any price movement with high volume\"\n                        ],\n                        correct: 1,\n                        explanation: \"A valid FVG requires a three-candle pattern where the large middle candle creates a clear gap between the first candle's high/low and the third candle's low/high.\"\n                    },\n                    {\n                        question: \"How should a bullish FVG behave when price returns to it?\",\n                        options: [\n                            \"Price should break through immediately\",\n                            \"Price should act as resistance\",\n                            \"Price should find support and potentially bounce\",\n                            \"Price should create more gaps\"\n                        ],\n                        correct: 2,\n                        explanation: \"A bullish FVG should act as a support zone when price returns to it, as the gap represents an area where buyers may step in to fill the inefficiency.\"\n                    },\n                    {\n                        question: \"When is a Fair Value Gap considered invalidated?\",\n                        options: [\n                            \"After one touch\",\n                            \"When price closes through the gap completely\",\n                            \"After 24 hours\",\n                            \"When volume decreases\"\n                        ],\n                        correct: 1,\n                        explanation: \"An FVG is invalidated when price closes completely through the gap, indicating the inefficiency has been filled and the zone no longer holds significance.\"\n                    }\n                ]\n            },\n            {\n                id: 2,\n                title: \"Higher Timeframe FVGs & Multi-Timeframe Analysis\",\n                description: \"Master the power of Higher Timeframe Fair Value Gaps and learn to combine multiple timeframes for precision entries\",\n                content: \"\\n# Higher Timeframe FVGs: The Institutional Magnets\\n\\nHigher timeframe Fair Value Gaps are among the most powerful tools in a professional trader's arsenal. These gaps act like magnets, drawing price back to fill inefficiencies left by rapid institutional moves.\\n\\n## Why Higher Timeframes Matter\\n\\n### Significance Hierarchy:\\n- **Weekly FVGs**: Extremely powerful, may take months to fill\\n- **Daily FVGs**: Very significant, often filled within days/weeks\\n- **4-Hour FVGs**: Strong levels, usually filled within days\\n- **1-Hour FVGs**: Moderate significance, filled within hours/days\\n- **15-Min FVGs**: Lower significance, often filled quickly\\n\\n### Volume and Participation:\\nHigher timeframe moves involve:\\n- More institutional participation\\n- Larger order sizes\\n- Greater market impact\\n- Broader market awareness\\n- Stronger magnetic effect\\n\\n## HTF FVG vs LTF FVG Comparison\\n\\n### Higher Timeframe FVGs (Daily+):\\n**Advantages:**\\n- Higher probability of being filled\\n- Stronger support/resistance when reached\\n- Better risk/reward opportunities\\n- Less noise and false signals\\n- Institutional relevance\\n\\n**Characteristics:**\\n- Take longer to reach\\n- Provide major turning points\\n- Often align with other key levels\\n- Create significant price reactions\\n\\n### Lower Timeframe FVGs (1H and below):\\n**Advantages:**\\n- More frequent opportunities\\n- Faster fills and reactions\\n- Good for scalping strategies\\n- Quick feedback on trades\\n\\n**Disadvantages:**\\n- Higher noise ratio\\n- More false signals\\n- Weaker reactions\\n- Less institutional relevance\\n\\n## Multi-Timeframe FVG Analysis\\n\\n### The Professional Approach:\\n1. **Mark HTF FVGs first** (Daily, 4H, 1H)\\n2. **Use LTF for entry timing** (15M, 5M)\\n3. **Combine with other confluences**\\n4. **Prioritize HTF over LTF**\\n\\n### Confluence Stacking:\\n**High-Probability Setup:**\\n- Daily FVG zone\\n- + Previous day high/low\\n- + Volume profile level\\n- + Liquidity sweep area\\n- = Maximum confluence\\n\\n## SPY/QQQ HTF FVG Patterns\\n\\n### SPY Daily FVG Characteristics:\\n- **Formation**: Often during earnings, Fed announcements, major news\\n- **Size**: Typically $2-8 gaps on daily charts\\n- **Fill Rate**: 85-90% eventually get filled\\n- **Timeframe**: Usually filled within 1-4 weeks\\n- **Reaction**: Strong bounces/rejections on first touch\\n\\n### QQQ Daily FVG Characteristics:\\n- **Formation**: Tech earnings, guidance changes, sector rotation\\n- **Size**: Typically $3-12 gaps due to higher volatility\\n- **Fill Rate**: 80-85% eventually get filled\\n- **Timeframe**: May take longer due to trend strength\\n- **Reaction**: More volatile reactions, wider zones needed\\n\\n## Trading HTF FVGs: The Professional Method\\n\\n### Step 1: Identification\\n- Scan daily/4H charts for clean FVG formations\\n- Mark gap boundaries clearly\\n- Note the context (news, earnings, etc.)\\n- Assess gap size and significance\\n\\n### Step 2: Patience\\n- Wait for price to approach the HTF FVG\\n- Don't chase - let the gap come to you\\n- Monitor lower timeframes for entry signals\\n- Prepare for potential strong reactions\\n\\n### Step 3: Entry Timing\\n- Use 15M/5M charts for precise entries\\n- Look for additional confirmations:\\n  - Lower timeframe structure breaks\\n  - Volume increases\\n  - Candlestick patterns\\n  - Momentum divergences\\n\\n### Step 4: Risk Management\\n- Stop loss beyond the FVG zone\\n- Take profits at logical levels\\n- Trail stops as trade develops\\n- Respect the power of HTF levels\\n\\n## Real-World HTF FVG Example\\n\\n**SPY Daily Bullish FVG Setup:**\\n- **Formation**: Fed announcement creates gap from $445-$448\\n- **Wait Period**: 2 weeks for price to return\\n- **Entry Signal**: 15M bullish engulfing at $446 (within FVG)\\n- **Confirmation**: Volume spike + break of 15M structure\\n- **Result**: Bounce to $452 for 1.3% profit\\n- **Risk**: Stop at $444 (below FVG) for 0.4% risk\\n- **R:R Ratio**: 3.25:1\\n\\n## Key HTF FVG Rules\\n\\n1. **Higher timeframe always wins** - HTF FVG overrides LTF signals\\n2. **First touch is strongest** - Best reactions occur on initial contact\\n3. **Partial fills are common** - Price may only fill 50-70% of gap\\n4. **Context matters** - Consider overall market trend and sentiment\\n5. **Patience pays** - Wait for proper setups, don't force trades\\n        \",\n                type: \"practical\",\n                duration: \"25 min\",\n                keyPoints: [\n                    \"Perfect liquidity sweeps follow a 4-phase pattern: approach, penetration, reversal, follow-through\",\n                    \"SPY sweeps typically extend 0.1-0.3% beyond levels with 20-50% above average volume\",\n                    \"Visual recognition includes wick formations, volume spikes, and immediate reversals\",\n                    \"Time-based patterns show highest probability during market open and close\",\n                    \"Multi-timeframe analysis provides confirmation and precise entry timing\"\n                ],\n                practicalExercises: [\n                    \"Identify and analyze 3 historical liquidity sweeps on SPY using the 4-phase pattern\",\n                    \"Mark 5 potential liquidity levels on current QQQ chart and monitor for sweep patterns\",\n                    \"Practice distinguishing between true sweeps and genuine breakouts using volume analysis\",\n                    \"Create alerts for price approaching identified liquidity levels for real-time practice\"\n                ],\n                quiz: [\n                    {\n                        question: \"What is the typical penetration distance for SPY liquidity sweeps?\",\n                        options: [\n                            \"1-2% beyond the level\",\n                            \"0.1-0.3% beyond the level\",\n                            \"5-10% beyond the level\",\n                            \"Exactly to the level\"\n                        ],\n                        correct: 1,\n                        explanation: \"SPY liquidity sweeps typically penetrate 0.1-0.3% beyond key levels - enough to trigger stops but not so much as to indicate a genuine breakout.\"\n                    },\n                    {\n                        question: \"Which phase of a liquidity sweep shows the highest volume?\",\n                        options: [\n                            \"The approach phase\",\n                            \"The penetration phase\",\n                            \"The reversal phase\",\n                            \"The follow-through phase\"\n                        ],\n                        correct: 2,\n                        explanation: \"The reversal phase typically shows the highest volume as institutional orders enter the market after stops are triggered.\"\n                    }\n                ]\n            },\n            {\n                id: 3,\n                title: \"Trading Liquidity Sweeps\",\n                description: \"How to position yourself to profit from sweep reversals\",\n                content: \"Once you identify a liquidity sweep, the next step is positioning...\",\n                type: \"strategy\",\n                duration: \"18 min\",\n                keyPoints: [\n                    \"Entry timing after sweep completion\",\n                    \"Stop loss placement strategies\",\n                    \"Target setting for sweep trades\"\n                ]\n            },\n            {\n                id: 4,\n                title: \"Sweep Analysis Workshop\",\n                description: \"Analyze real SPY/QQQ liquidity sweeps with expert commentary\",\n                content: \"Review historical examples of successful sweep trades\",\n                type: \"interactive\",\n                duration: \"7 min\",\n                keyPoints: [\n                    \"Case study analysis\",\n                    \"Pattern recognition practice\",\n                    \"Risk management examples\"\n                ]\n            }\n        ]\n    },\n    {\n        id: 3,\n        title: \"Confirmation Stacking & Multi-Factor Analysis\",\n        description: \"Master the art of stacking multiple confirmations for high-probability trades. Learn to combine price action, volume, and technical analysis for professional-level precision.\",\n        icon: \"Layers\",\n        color: \"from-purple-500 to-purple-600\",\n        estimatedTime: \"85 minutes\",\n        difficulty: \"Advanced\",\n        prerequisites: \"Understanding of liquidity sweeps and Fair Value Gaps\",\n        learningObjectives: [\n            \"Stack 3+ confirmations for every trade setup\",\n            \"Master market structure confirmations (BOS/CHoCH)\",\n            \"Integrate volume profile and order flow analysis\",\n            \"Combine multiple timeframes for precision entries\",\n            \"Develop a systematic approach to trade validation\"\n        ],\n        lessons: [\n            {\n                id: 1,\n                title: \"Confirmation Stacking Fundamentals\",\n                description: \"Learn the professional approach to stacking multiple confirmations for high-probability trade setups\",\n                content: '\\n# Confirmation Stacking: The Professional Edge\\n\\nEven when you have a strong level or setup in mind (be it a liquidity sweep or an FVG), jumping in without confirmation can be risky. Confirmation stacking means waiting for multiple signals to line up in your favor before committing to a trade.\\n\\n## The Philosophy of Confluence\\n\\n**Core Principle:**\\nRather than relying on a single indicator or one pattern, you look for an agreement among several independent clues – what traders often call confluence. The idea is to filter out low-quality setups and only act when many things point to the same conclusion.\\n\\n**Think of it this way:**\\nEach confirmation is like a piece of a puzzle. One piece alone doesn\\'t show the whole picture, but when several pieces fit together, you have a clearer image of where price might go.\\n\\n## The Five Pillars of Confirmation\\n\\n### 1. Market Structure & Price Action\\nThis refers to analyzing how price swings (highs and lows) are behaving to confirm a trend change or continuation.\\n\\n**Break of Structure (BOS):**\\n- Price takes out a significant previous high or low in the direction of a trend\\n- Confirms that trend\\'s strength\\n- Shows institutional participation\\n\\n**Change of Character (CHoCH):**\\n- Early sign of a possible trend reversal\\n- First break of a minor swing level against the trend\\n- Indicates potential shift in market sentiment\\n\\n**Example:** If QQQ has been making higher highs and higher lows (uptrend) and then suddenly makes a lower low, that\\'s a bearish CHoCH signaling the uptrend may be done.\\n\\n### 2. Volume Profile & Range Context\\nVolume Profile shows how volume has been distributed at each price, giving insight into what prices the market deems \"fair\" vs \"extreme.\"\\n\\n**Key Elements:**\\n- **Point of Control (POC):** Price with highest traded volume\\n- **Value Area (VA):** Price range where ~70% of volume occurred\\n- **Value Area High/Low (VAH/VAL):** Boundaries of fair value\\n\\n**Application:** If SPY rejects from yesterday\\'s Value Area High after a liquidity sweep, that\\'s confluence supporting a reversal trade.\\n\\n### 3. Order Flow Tools (Advanced)\\nReal-time confirmation of what\\'s happening under the hood through futures DOM, time and sales, or heatmap platforms.\\n\\n**Signals to Watch:**\\n- Absorption of selling/buying at key levels\\n- Cumulative volume delta divergences\\n- Large limit orders on the book\\n- Aggressive vs passive order flow\\n\\n### 4. Indicators & Overlays\\nTraditional technical indicators can be part of your confirmation stack, especially ones that measure trend or mean reversion.\\n\\n**VWAP (Volume Weighted Average Price):**\\n- Intraday equilibrium level\\n- Reclaiming VWAP after a sweep adds confidence\\n- Acts as dynamic support/resistance\\n\\n**Moving Averages:**\\n- 21 EMA, 50 EMA for trend confirmation\\n- Dynamic support on pullbacks\\n- Confluence with other levels\\n\\n### 5. Liquidity & HTF Levels\\nCombining liquidity sweeps and HTF FVGs as part of confirmation checklist.\\n\\n**High-Probability Setup:**\\n- Liquidity sweep at HTF FVG\\n- + Market structure confirmation\\n- + Volume profile level\\n- + VWAP reclaim\\n- = Maximum confluence\\n\\n## The Professional Confirmation Checklist\\n\\n### Minimum Requirements:\\n**For Entry:** At least 2-3 solid confirmations\\n**For High-Conviction Trades:** 4+ confirmations aligned\\n\\n### Example Checklist:\\n1. ✓ Liquidity sweep occurred\\n2. ✓ Price action confirmation (CHoCH/BOS)\\n3. ✓ Volume profile level confluence\\n4. ✓ HTF FVG zone\\n5. ✓ VWAP reclaim/rejection\\n\\n## Real-World Confirmation Stacking Example\\n\\n### SPY Bearish Setup:\\n**Setup:** SPY approaches yesterday\\'s high at $450\\n\\n**Confirmations:**\\n1. **Liquidity Sweep:** Price hits $450.50, sweeps stops\\n2. **HTF Level:** Daily bearish FVG zone at $450-451\\n3. **Price Action:** 5-minute bearish engulfing + CHoCH\\n4. **Volume Profile:** Rejection from yesterday\\'s VAH\\n5. **Volume:** Spike on sweep, sustained on reversal\\n\\n**Entry:** Short at $449.50 after all confirmations align\\n**Stop:** $451 (above sweep high)\\n**Target:** $445 (previous support)\\n**Result:** 1% profit with 0.3% risk = 3.3:1 R/R\\n\\n## Avoiding Analysis Paralysis\\n\\n### Balance is Key:\\n- Too few confirmations = low probability\\n- Too many confirmations = missed opportunities\\n- Sweet spot: 2-3 strong confirmations\\n\\n### Weighting Confirmations:\\n**Primary (Must Have):**\\n- Price action signal (structure break)\\n- Key level confluence (liquidity/FVG)\\n\\n**Secondary (Nice to Have):**\\n- Volume confirmation\\n- Indicator alignment\\n- Higher timeframe context\\n\\n## Common Confirmation Mistakes\\n\\n1. **Forcing Confluence:** Seeing confirmations that aren\\'t really there\\n2. **Over-Analysis:** Requiring too many signals\\n3. **Ignoring Context:** Not considering overall market environment\\n4. **Static Thinking:** Not adapting to changing market conditions\\n5. **Confirmation Bias:** Only seeing signals that support your bias\\n- Unfilled orders create demand/supply\\n- Technical traders target gap fills\\n- Self-fulfilling prophecy effect\\n\\n## FVG vs. Regular Gaps\\n\\n### Fair Value Gaps:\\n- Formed by 3-candle pattern\\n- Represent order flow imbalance\\n- High probability of fill (70-80%)\\n- Can be traded in both directions\\n- Show institutional activity\\n\\n### Regular Price Gaps:\\n- Formed between sessions (overnight)\\n- Caused by news or events\\n- Lower probability of fill (40-60%)\\n- Often indicate trend continuation\\n- May not represent institutional flow\\n\\n## Psychological Aspects of FVG Trading\\n\\n### Institutional Perspective:\\n- \"We moved price too fast\"\\n- \"Need to fill remaining orders\"\\n- \"Better prices available in the gap\"\\n- \"Risk management requires rebalancing\"\\n\\n### Retail Perspective:\\n- \"Price gapped away from me\"\\n- \"I missed the move\"\\n- \"Will it come back?\"\\n- \"Should I chase or wait?\"\\n\\n## Real-World FVG Examples\\n\\n### Example 1: SPY Bullish FVG\\n**Setup:** Fed announcement creates buying surge\\n**Formation:** 3-candle bullish FVG at $445-$447\\n**Fill:** Price returns to gap 5 days later\\n**Outcome:** Perfect bounce from gap support\\n\\n### Example 2: QQQ Bearish FVG\\n**Setup:** Tech earnings disappointment\\n**Formation:** 3-candle bearish FVG at $380-$382\\n**Fill:** Price rallies to gap 2 weeks later\\n**Outcome:** Strong resistance at gap level\\n\\n## Advanced FVG Concepts\\n\\n### 1. Nested FVGs\\n- Multiple gaps within larger gaps\\n- Provide multiple trading opportunities\\n- Show sustained institutional activity\\n- Require careful order management\\n\\n### 2. FVG Clusters\\n- Multiple gaps in same price area\\n- Extremely high probability zones\\n- Often mark major support/resistance\\n- Institutional accumulation/distribution areas\\n\\n### 3. Partial vs. Full Fills\\n- **Partial Fill:** Price touches gap but doesn\\'t close it\\n- **Full Fill:** Price completely closes the gap\\n- **Overfill:** Price extends beyond the gap\\n- Each has different trading implications\\n\\n## Common FVG Mistakes\\n\\n1. **Trading Every Gap:** Not all FVGs are equal quality\\n2. **Ignoring Context:** Market structure matters\\n3. **Poor Risk Management:** Gaps can extend before filling\\n4. **Wrong Timeframe:** Match timeframe to trading style\\n5. **Emotional Trading:** FOMO on gap formations\\n        ',\n                type: \"theory\",\n                duration: \"25 min\",\n                keyPoints: [\n                    \"Fair Value Gaps represent institutional order flow imbalances created by overwhelming buying/selling pressure\",\n                    \"FVGs require exactly 3 candles with no overlap between outer candles' high/low\",\n                    \"SPY FVGs typically range 0.2-0.8% while QQQ ranges 0.3-1.2% due to higher volatility\",\n                    \"70-80% of FVGs get filled within 5-20 sessions as markets seek price efficiency\",\n                    \"Inversion FVGs become powerful support/resistance after being filled\"\n                ],\n                practicalExercises: [\n                    \"Identify 5 bullish and 5 bearish FVGs on SPY 30-minute chart from last month\",\n                    \"Measure the size of each FVG as percentage of price and compare to typical ranges\",\n                    \"Track which FVGs got filled and calculate the fill rate for your sample\",\n                    \"Practice distinguishing between FVGs and regular overnight gaps\"\n                ],\n                quiz: [\n                    {\n                        question: \"How many candles are required to form a Fair Value Gap?\",\n                        options: [\n                            \"2 candles\",\n                            \"3 candles\",\n                            \"4 candles\",\n                            \"5 candles\"\n                        ],\n                        correct: 1,\n                        explanation: \"A Fair Value Gap requires exactly 3 consecutive candles, with the middle candle creating the imbalance and no overlap between the outer candles.\"\n                    },\n                    {\n                        question: \"What percentage of Fair Value Gaps typically get filled?\",\n                        options: [\n                            \"30-40%\",\n                            \"50-60%\",\n                            \"70-80%\",\n                            \"90-100%\"\n                        ],\n                        correct: 2,\n                        explanation: \"Approximately 70-80% of Fair Value Gaps get filled as markets naturally seek price efficiency and institutional orders get completed.\"\n                    }\n                ]\n            },\n            {\n                id: 2,\n                title: \"FVG Classification System\",\n                description: \"Learn to classify FVGs by strength and probability\",\n                content: \"Not all FVGs are equal. Learn the classification system...\",\n                type: \"practical\",\n                duration: \"15 min\",\n                keyPoints: [\n                    \"High probability vs low probability gaps\",\n                    \"Size and context importance\",\n                    \"Multiple timeframe FVG analysis\"\n                ]\n            },\n            {\n                id: 3,\n                title: \"Trading FVG Fills\",\n                description: \"Strategies for trading when price returns to fill gaps\",\n                content: \"FVG fills often provide excellent trading opportunities...\",\n                type: \"strategy\",\n                duration: \"16 min\",\n                keyPoints: [\n                    \"Partial vs full gap fills\",\n                    \"Entry and exit strategies\",\n                    \"Combining FVGs with other confluences\"\n                ]\n            },\n            {\n                id: 4,\n                title: \"FVG Recognition Challenge\",\n                description: \"Test your ability to spot and classify FVGs in real-time\",\n                content: \"Interactive challenge to identify FVGs on live charts\",\n                type: \"interactive\",\n                duration: \"7 min\",\n                keyPoints: [\n                    \"Speed recognition drills\",\n                    \"Classification accuracy\",\n                    \"Real-time decision making\"\n                ]\n            }\n        ]\n    },\n    {\n        id: 4,\n        title: \"Volume Analysis & Confirmation\",\n        description: \"Use volume analysis to confirm your price action signals\",\n        icon: \"Activity\",\n        color: \"from-orange-500 to-orange-600\",\n        estimatedTime: \"40 minutes\",\n        difficulty: \"Beginner\",\n        lessons: [\n            {\n                id: 1,\n                title: \"Volume Fundamentals\",\n                description: \"Understanding volume and its relationship to price movement\",\n                content: \"Volume is the fuel that drives price movement...\",\n                type: \"theory\",\n                duration: \"10 min\",\n                keyPoints: [\n                    \"Volume precedes price\",\n                    \"Accumulation vs distribution patterns\",\n                    \"Volume profile concepts\"\n                ]\n            },\n            {\n                id: 2,\n                title: \"Volume at Key Levels\",\n                description: \"Analyzing volume behavior at support/resistance zones\",\n                content: \"How volume behaves at key levels tells us about market sentiment...\",\n                type: \"practical\",\n                duration: \"15 min\",\n                keyPoints: [\n                    \"Rising volume on approach to zones\",\n                    \"Fading volume and false breakouts\",\n                    \"Climactic volume patterns\"\n                ]\n            },\n            {\n                id: 3,\n                title: \"Volume Confirmation Strategies\",\n                description: \"Using volume to confirm your trading signals\",\n                content: \"Volume confirmation can significantly improve trade success rates...\",\n                type: \"strategy\",\n                duration: \"12 min\",\n                keyPoints: [\n                    \"Volume divergence signals\",\n                    \"Confirmation vs contradiction\",\n                    \"Multiple timeframe volume analysis\"\n                ]\n            },\n            {\n                id: 4,\n                title: \"Volume Analysis Practice\",\n                description: \"Practice reading volume patterns on SPY/QQQ charts\",\n                content: \"Hands-on practice with volume analysis techniques\",\n                type: \"interactive\",\n                duration: \"3 min\",\n                keyPoints: [\n                    \"Pattern recognition\",\n                    \"Signal confirmation practice\",\n                    \"Real-world application\"\n                ]\n            }\n        ]\n    },\n    {\n        id: 5,\n        title: \"Confirmation Stacking\",\n        description: \"Learn to stack multiple confirmations for high-probability trades\",\n        icon: \"Layers\",\n        color: \"from-red-500 to-red-600\",\n        estimatedTime: \"55 minutes\",\n        difficulty: \"Advanced\",\n        lessons: [\n            {\n                id: 1,\n                title: \"The Stacking Methodology\",\n                description: \"Understanding the concept of confirmation stacking\",\n                content: \"Confirmation stacking involves combining multiple technical signals...\",\n                type: \"theory\",\n                duration: \"12 min\",\n                keyPoints: [\n                    \"Quality over quantity in confirmations\",\n                    \"Weighted confirmation systems\",\n                    \"Avoiding analysis paralysis\"\n                ]\n            },\n            {\n                id: 2,\n                title: \"Building Your Stack\",\n                description: \"How to systematically build confirmation stacks\",\n                content: \"Learn the systematic approach to building robust confirmation stacks...\",\n                type: \"practical\",\n                duration: \"18 min\",\n                keyPoints: [\n                    \"Primary vs secondary confirmations\",\n                    \"Timeframe hierarchy\",\n                    \"Confluence zone identification\"\n                ]\n            },\n            {\n                id: 3,\n                title: \"Advanced Stacking Techniques\",\n                description: \"Professional-level confirmation stacking strategies\",\n                content: \"Advanced techniques used by professional traders...\",\n                type: \"strategy\",\n                duration: \"20 min\",\n                keyPoints: [\n                    \"Multi-timeframe stacking\",\n                    \"Intermarket confirmations\",\n                    \"Sentiment-based confirmations\"\n                ]\n            },\n            {\n                id: 4,\n                title: \"Stacking Mastery Challenge\",\n                description: \"Put your stacking skills to the test with complex scenarios\",\n                content: \"Advanced challenge scenarios to test your mastery\",\n                type: \"interactive\",\n                duration: \"5 min\",\n                keyPoints: [\n                    \"Complex scenario analysis\",\n                    \"Decision-making under pressure\",\n                    \"Professional-level execution\"\n                ]\n            }\n        ]\n    },\n    {\n        id: 6,\n        title: \"Risk Management & Psychology\",\n        description: \"Master the mental game and risk management for consistent profits\",\n        icon: \"Shield\",\n        color: \"from-indigo-500 to-indigo-600\",\n        estimatedTime: \"45 minutes\",\n        difficulty: \"Intermediate\",\n        lessons: [\n            {\n                id: 1,\n                title: \"Position Sizing Fundamentals\",\n                description: \"Calculate optimal position sizes for your account\",\n                content: \"Proper position sizing is the foundation of risk management...\",\n                type: \"theory\",\n                duration: \"12 min\",\n                keyPoints: [\n                    \"Risk percentage rules\",\n                    \"Account size considerations\",\n                    \"Volatility-adjusted sizing\"\n                ]\n            },\n            {\n                id: 2,\n                title: \"Stop Loss Strategies\",\n                description: \"Advanced stop loss placement and management techniques\",\n                content: \"Stop losses are your insurance policy in trading...\",\n                type: \"practical\",\n                duration: \"15 min\",\n                keyPoints: [\n                    \"Technical vs percentage stops\",\n                    \"Trailing stop strategies\",\n                    \"Stop loss psychology\"\n                ]\n            },\n            {\n                id: 3,\n                title: \"Trading Psychology Mastery\",\n                description: \"Develop the mental discipline required for consistent trading\",\n                content: \"Trading psychology often determines success more than technical skills...\",\n                type: \"strategy\",\n                duration: \"15 min\",\n                keyPoints: [\n                    \"Emotional regulation techniques\",\n                    \"Dealing with losses\",\n                    \"Maintaining discipline\"\n                ]\n            },\n            {\n                id: 4,\n                title: \"Psychology Assessment\",\n                description: \"Evaluate your trading psychology and identify areas for improvement\",\n                content: \"Self-assessment tools for trading psychology\",\n                type: \"interactive\",\n                duration: \"3 min\",\n                keyPoints: [\n                    \"Psychological profiling\",\n                    \"Weakness identification\",\n                    \"Improvement planning\"\n                ]\n            }\n        ]\n    },\n    {\n        id: 7,\n        title: \"Advanced Pattern Recognition\",\n        description: \"Identify complex patterns and market structures for professional-level trading\",\n        icon: \"Eye\",\n        color: \"from-teal-500 to-teal-600\",\n        estimatedTime: \"65 minutes\",\n        difficulty: \"Advanced\",\n        lessons: [\n            {\n                id: 1,\n                title: \"Complex Pattern Structures\",\n                description: \"Understanding advanced chart patterns and their implications\",\n                content: \"Advanced patterns often provide the highest probability setups...\",\n                type: \"theory\",\n                duration: \"18 min\",\n                keyPoints: [\n                    \"Multi-timeframe pattern analysis\",\n                    \"Pattern failure and continuation\",\n                    \"Context-dependent patterns\"\n                ]\n            },\n            {\n                id: 2,\n                title: \"Market Structure Shifts\",\n                description: \"Identifying when market structure changes and how to adapt\",\n                content: \"Market structure shifts signal major changes in sentiment...\",\n                type: \"practical\",\n                duration: \"20 min\",\n                keyPoints: [\n                    \"Break of structure signals\",\n                    \"Change of character patterns\",\n                    \"Trend transition identification\"\n                ]\n            },\n            {\n                id: 3,\n                title: \"Professional Pattern Trading\",\n                description: \"How professionals trade complex patterns for maximum profit\",\n                content: \"Professional trading strategies for advanced patterns...\",\n                type: \"strategy\",\n                duration: \"22 min\",\n                keyPoints: [\n                    \"Entry and exit optimization\",\n                    \"Risk-reward maximization\",\n                    \"Pattern-specific strategies\"\n                ]\n            },\n            {\n                id: 4,\n                title: \"Pattern Mastery Exam\",\n                description: \"Final examination of your pattern recognition abilities\",\n                content: \"Comprehensive test of all pattern recognition skills\",\n                type: \"interactive\",\n                duration: \"5 min\",\n                keyPoints: [\n                    \"Comprehensive pattern test\",\n                    \"Speed and accuracy assessment\",\n                    \"Professional certification\"\n                ]\n            }\n        ]\n    }\n];\nconst COURSE_ACHIEVEMENTS = [\n    {\n        id: \"first_lesson\",\n        title: \"Getting Started\",\n        description: \"Complete your first lesson\",\n        icon: \"Play\",\n        points: 10\n    },\n    {\n        id: \"first_module\",\n        title: \"Module Master\",\n        description: \"Complete your first module\",\n        icon: \"Award\",\n        points: 50\n    },\n    {\n        id: \"quiz_master\",\n        title: \"Quiz Master\",\n        description: \"Score 90% or higher on 5 quizzes\",\n        icon: \"Brain\",\n        points: 100\n    },\n    {\n        id: \"speed_learner\",\n        title: \"Speed Learner\",\n        description: \"Complete 3 lessons in one day\",\n        icon: \"Zap\",\n        points: 75\n    },\n    {\n        id: \"course_complete\",\n        title: \"Course Graduate\",\n        description: \"Complete the entire course\",\n        icon: \"GraduationCap\",\n        points: 500\n    }\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/data/courseData.js\n"));

/***/ })

});