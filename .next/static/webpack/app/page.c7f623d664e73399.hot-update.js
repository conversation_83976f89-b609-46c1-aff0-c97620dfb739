"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.js":
/*!*************************!*\
  !*** ./src/app/page.js ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HomePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Header */ \"(app-pages-browser)/./src/components/Header.js\");\n/* harmony import */ var _components_DailyChecklist__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/DailyChecklist */ \"(app-pages-browser)/./src/components/DailyChecklist.js\");\n/* harmony import */ var _components_JournalEntry__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/JournalEntry */ \"(app-pages-browser)/./src/components/JournalEntry.js\");\n/* harmony import */ var _components_analytics_TradingAnalytics__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/analytics/TradingAnalytics */ \"(app-pages-browser)/./src/components/analytics/TradingAnalytics.js\");\n/* harmony import */ var _components_modals_SettingsModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/modals/SettingsModal */ \"(app-pages-browser)/./src/components/modals/SettingsModal.js\");\n/* harmony import */ var _components_features_SessionTimer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/features/SessionTimer */ \"(app-pages-browser)/./src/components/features/SessionTimer.js\");\n/* harmony import */ var _components_course_Course__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/course/Course */ \"(app-pages-browser)/./src/components/course/Course.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,BookOpen,Calendar,Download,Settings,Star,Target,Timer,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,BookOpen,Calendar,Download,Settings,Star,Target,Timer,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,BookOpen,Calendar,Download,Settings,Star,Target,Timer,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,BookOpen,Calendar,Download,Settings,Star,Target,Timer,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,BookOpen,Calendar,Download,Settings,Star,Target,Timer,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,BookOpen,Calendar,Download,Settings,Star,Target,Timer,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,BookOpen,Calendar,Download,Settings,Star,Target,Timer,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,BookOpen,Calendar,Download,Settings,Star,Target,Timer,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,BookOpen,Calendar,Download,Settings,Star,Target,Timer,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _utils_storage__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/utils/storage */ \"(app-pages-browser)/./src/utils/storage.js\");\n/* harmony import */ var _store_useStore__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/store/useStore */ \"(app-pages-browser)/./src/store/useStore.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.js\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction HomePage() {\n    var _tabs_find;\n    _s();\n    const { ui, setActiveTab, updateStats, toggleModal } = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_10__.useStore)();\n    const stats = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_10__.useStats)();\n    const [localStats, setLocalStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalChecklistDays: 0,\n        totalJournalEntries: 0,\n        currentStreak: 0,\n        weeklyGoal: 5,\n        completionRate: 0\n    });\n    const [showSessionTimer, setShowSessionTimer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Initialize cloud database and load data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeApp = async ()=>{\n            try {\n                // Initialize database\n                const { initializeDatabase, loadCourseFromCloud } = _store_useStore__WEBPACK_IMPORTED_MODULE_10__.useStore.getState();\n                await initializeDatabase();\n                // Load course progress from cloud\n                await loadCourseFromCloud();\n                // Load real statistics from cloud and local data\n                await loadRealStats();\n                updateStats();\n                console.log(\"App initialized with cloud storage\");\n            } catch (error) {\n                console.error(\"App initialization error:\", error);\n                // Continue with localStorage only\n                await loadRealStats();\n                updateStats();\n            }\n        };\n        initializeApp();\n    }, [\n        updateStats\n    ]);\n    // Load real statistics from API or fallback to localStorage\n    const loadRealStats = async ()=>{\n        try {\n            const response = await fetch(\"/api/analytics?days=30\");\n            if (response.ok) {\n                const result = await response.json();\n                if (result.success) {\n                    const { summary } = result.data;\n                    setLocalStats({\n                        totalChecklistDays: summary.checklist.totalDays,\n                        totalJournalEntries: summary.journal.totalDays,\n                        currentStreak: summary.combined.streak,\n                        weeklyGoal: 5,\n                        completionRate: summary.combined.goalAchievementRate\n                    });\n                    return;\n                }\n            }\n        } catch (error) {\n            console.error(\"Error loading real stats:\", error);\n        }\n        // Fallback to localStorage calculation\n        const checklistDates = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_9__.getAllChecklistDates)();\n        const journalDates = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_9__.getAllJournalDates)();\n        setLocalStats({\n            totalChecklistDays: checklistDates.length,\n            totalJournalEntries: journalDates.length,\n            currentStreak: calculateStreak(checklistDates),\n            weeklyGoal: 5,\n            completionRate: checklistDates.length > 0 ? Math.round(journalDates.length / checklistDates.length * 100) : 0\n        });\n    };\n    const calculateStreak = (dates)=>{\n        if (dates.length === 0) return 0;\n        const sortedDates = dates.sort().reverse();\n        let streak = 0;\n        const today = new Date();\n        for(let i = 0; i < sortedDates.length; i++){\n            const date = new Date(sortedDates[i]);\n            const daysDiff = Math.floor((today - date) / (1000 * 60 * 60 * 24));\n            if (daysDiff === i) {\n                streak++;\n            } else {\n                break;\n            }\n        }\n        return streak;\n    };\n    const handleExportData = ()=>{\n        try {\n            const data = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_9__.exportAllData)();\n            if (data) {\n                const blob = new Blob([\n                    JSON.stringify(data, null, 2)\n                ], {\n                    type: \"application/json\"\n                });\n                const url = URL.createObjectURL(blob);\n                const a = document.createElement(\"a\");\n                a.href = url;\n                a.download = \"limitless-options-backup-\".concat(new Date().toISOString().split(\"T\")[0], \".json\");\n                document.body.appendChild(a);\n                a.click();\n                document.body.removeChild(a);\n                URL.revokeObjectURL(url);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_13__[\"default\"].success(\"Data exported successfully!\");\n            }\n        } catch (error) {\n            console.error(\"Export error:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_13__[\"default\"].error(\"Failed to export data\");\n        }\n    };\n    const tabs = [\n        {\n            id: \"checklist\",\n            label: \"Trading Checklist\",\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            description: \"Pre-trade analysis and criteria\",\n            color: \"from-primary-500 to-primary-600\"\n        },\n        {\n            id: \"journal\",\n            label: \"Daily Journal\",\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n            description: \"Trading thoughts and analysis\",\n            color: \"from-accent-500 to-accent-600\"\n        },\n        {\n            id: \"course\",\n            label: \"Education\",\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n            description: \"Advanced price action course and learning materials\",\n            color: \"from-purple-500 to-purple-600\"\n        },\n        {\n            id: \"analytics\",\n            label: \"Analytics\",\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n            description: \"Performance insights and trends\",\n            color: \"from-success-500 to-success-600\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-[#0A0F0F] via-[#0C1612] to-[#0E1D1A]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                lineNumber: 182,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 lg:py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.h1, {\n                                        className: \"text-4xl md:text-5xl lg:text-6xl font-heading font-bold gradient-text mb-4\",\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            delay: 0.1\n                                        },\n                                        children: \"Your Trading Command Center\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                        lineNumber: 192,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.div, {\n                                        className: \"mb-6\",\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            delay: 0.15\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/landing\",\n                                            className: \"inline-flex items-center px-6 py-3 bg-gradient-to-r from-brand-emerald to-green-600 text-black font-semibold rounded-xl shadow-lg hover:shadow-brand-emerald/30 hover:scale-105 transition-all duration-300\",\n                                            children: \"✨ View Premium Landing Page\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                            lineNumber: 208,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                        lineNumber: 202,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.p, {\n                                        className: \"text-xl text-[#9CA3AF] max-w-3xl mx-auto\",\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            delay: 0.2\n                                        },\n                                        children: \"Master your trading discipline with our comprehensive checklist system, rich journal platform, and advanced analytics dashboard.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                        lineNumber: 215,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            delay: 0.3\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_12__.StatsCard, {\n                                            title: \"Trading Days\",\n                                            value: localStats.totalChecklistDays,\n                                            change: localStats.currentStreak > 0 ? \"\".concat(localStats.currentStreak, \" day streak\") : \"Start your streak!\",\n                                            changeType: localStats.currentStreak > 0 ? \"positive\" : \"neutral\",\n                                            icon: _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                            lineNumber: 233,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                        lineNumber: 228,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            delay: 0.4\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_12__.StatsCard, {\n                                            title: \"Journal Entries\",\n                                            value: localStats.totalJournalEntries,\n                                            change: \"\".concat(Math.round(localStats.completionRate), \"% completion rate\"),\n                                            changeType: localStats.completionRate >= 80 ? \"positive\" : localStats.completionRate >= 50 ? \"neutral\" : \"negative\",\n                                            icon: _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                            lineNumber: 247,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                        lineNumber: 242,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            delay: 0.5\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_12__.StatsCard, {\n                                            title: \"Current Streak\",\n                                            value: \"\".concat(localStats.currentStreak, \" days\"),\n                                            change: localStats.currentStreak >= localStats.weeklyGoal ? \"Weekly goal achieved!\" : \"\".concat(localStats.weeklyGoal - localStats.currentStreak, \" to weekly goal\"),\n                                            changeType: localStats.currentStreak >= localStats.weeklyGoal ? \"positive\" : \"neutral\",\n                                            icon: _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                            lineNumber: 261,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                        lineNumber: 256,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            delay: 0.6\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_12__.StatsCard, {\n                                            title: \"Performance\",\n                                            value: \"\".concat(Math.round(localStats.completionRate), \"%\"),\n                                            change: \"Journal completion\",\n                                            changeType: localStats.completionRate >= 80 ? \"positive\" : \"neutral\",\n                                            icon: _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"]\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                            lineNumber: 275,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                        lineNumber: 270,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                lineNumber: 227,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                        lineNumber: 186,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 0.7\n                        },\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            variant: \"glass\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col lg:flex-row items-center justify-between p-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-2 bg-black/20 backdrop-blur-xl border border-[#F4C46A]/20 rounded-xl p-1\",\n                                            children: tabs.map((tab)=>{\n                                                const Icon = tab.icon;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.button, {\n                                                    onClick: ()=>setActiveTab(tab.id),\n                                                    className: \"\\n                        relative flex items-center space-x-3 px-6 py-3 rounded-lg font-semibold transition-all duration-300\\n                        \".concat(ui.activeTab === tab.id ? \"text-white shadow-lg\" : \"text-gray-600 hover:text-gray-900 hover:bg-gray-200\", \"\\n                      \"),\n                                                    whileHover: {\n                                                        scale: 1.02\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.98\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                            lineNumber: 312,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"hidden sm:inline\",\n                                                            children: tab.label\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                            lineNumber: 313,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"sm:hidden\",\n                                                            children: tab.label.split(\" \")[0]\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                            lineNumber: 314,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        ui.activeTab === tab.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.div, {\n                                                            layoutId: \"activeTabBg\",\n                                                            className: \"absolute inset-0 bg-gradient-to-r \".concat(tab.color, \" rounded-lg\"),\n                                                            transition: {\n                                                                type: \"spring\",\n                                                                bounce: 0.2,\n                                                                duration: 0.6\n                                                            },\n                                                            style: {\n                                                                zIndex: -1\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                            lineNumber: 317,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, tab.id, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 21\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                            lineNumber: 295,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 mt-4 lg:mt-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    icon: _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n                                                    onClick: handleExportData,\n                                                    title: \"Export all data as backup\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"hidden sm:inline\",\n                                                        children: \"Export\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    icon: _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n                                                    onClick: ()=>react_hot_toast__WEBPACK_IMPORTED_MODULE_13__[\"default\"].info(\"Settings coming soon!\"),\n                                                    title: \"Settings\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"hidden sm:inline\",\n                                                        children: \"Settings\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                            lineNumber: 330,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                    lineNumber: 294,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-6 pb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.p, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 10\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        className: \"text-center text-gray-600\",\n                                        children: (_tabs_find = tabs.find((tab)=>tab.id === ui.activeTab)) === null || _tabs_find === void 0 ? void 0 : _tabs_find.description\n                                    }, ui.activeTab, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                        lineNumber: 355,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                    lineNumber: 354,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                            lineNumber: 293,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                        lineNumber: 287,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_23__.AnimatePresence, {\n                        mode: \"wait\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            exit: {\n                                opacity: 0,\n                                y: -20\n                            },\n                            transition: {\n                                duration: 0.3\n                            },\n                            children: [\n                                ui.activeTab === \"checklist\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 xl:grid-cols-4 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"xl:col-span-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DailyChecklist__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                lineNumber: 379,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                            lineNumber: 378,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"xl:col-span-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_features_SessionTimer__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                lineNumber: 382,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                            lineNumber: 381,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                    lineNumber: 377,\n                                    columnNumber: 15\n                                }, this),\n                                ui.activeTab === \"journal\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_JournalEntry__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                    lineNumber: 386,\n                                    columnNumber: 44\n                                }, this),\n                                ui.activeTab === \"course\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_course_Course__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                    lineNumber: 387,\n                                    columnNumber: 43\n                                }, this),\n                                ui.activeTab === \"analytics\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_analytics_TradingAnalytics__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                    lineNumber: 388,\n                                    columnNumber: 46\n                                }, this)\n                            ]\n                        }, ui.activeTab, true, {\n                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                            lineNumber: 369,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                        lineNumber: 368,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.footer, {\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        transition: {\n                            delay: 0.8\n                        },\n                        className: \"mt-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            variant: \"glass\",\n                            size: \"sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center space-x-2 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-6 h-6 rounded-lg bg-gradient-to-r from-primary-500 to-accent-500 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    className: \"w-3 h-3 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                lineNumber: 402,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold text-gray-900\",\n                                                children: \"Limitless Options\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                lineNumber: 405,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                        lineNumber: 401,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"Built for professional traders who demand excellence.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                        lineNumber: 409,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center space-x-4 mt-3 text-xs text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"\\xa9 2024 Limitless Options\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                lineNumber: 413,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"•\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                lineNumber: 414,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Trading Hub v1.0\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                lineNumber: 415,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"•\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                lineNumber: 416,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Made with ❤️ for traders\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                lineNumber: 417,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                        lineNumber: 412,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                lineNumber: 400,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                            lineNumber: 399,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                        lineNumber: 393,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_SettingsModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        isOpen: ui.modals.settings,\n                        onClose: ()=>toggleModal(\"settings\")\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                        lineNumber: 424,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                lineNumber: 184,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n        lineNumber: 181,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"GeUC4p0yWf5RsgBD74SsXwzaTyE=\", false, function() {\n    return [\n        _store_useStore__WEBPACK_IMPORTED_MODULE_10__.useStore,\n        _store_useStore__WEBPACK_IMPORTED_MODULE_10__.useStats\n    ];\n});\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.js\n"));

/***/ })

});