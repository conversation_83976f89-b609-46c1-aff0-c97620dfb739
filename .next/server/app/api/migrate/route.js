"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/migrate/route";
exports.ids = ["app/api/migrate/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("pg");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fmigrate%2Froute&page=%2Fapi%2Fmigrate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmigrate%2Froute.js&appDir=%2FUsers%2Fedwardaver%2FDesktop%2FLimitless%20Checklist%20Project%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fedwardaver%2FDesktop%2FLimitless%20Checklist%20Project&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fmigrate%2Froute&page=%2Fapi%2Fmigrate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmigrate%2Froute.js&appDir=%2FUsers%2Fedwardaver%2FDesktop%2FLimitless%20Checklist%20Project%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fedwardaver%2FDesktop%2FLimitless%20Checklist%20Project&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_edwardaver_Desktop_Limitless_Checklist_Project_src_app_api_migrate_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/migrate/route.js */ \"(rsc)/./src/app/api/migrate/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/migrate/route\",\n        pathname: \"/api/migrate\",\n        filename: \"route\",\n        bundlePath: \"app/api/migrate/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/api/migrate/route.js\",\n    nextConfigOutput,\n    userland: _Users_edwardaver_Desktop_Limitless_Checklist_Project_src_app_api_migrate_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/migrate/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fmigrate%2Froute&page=%2Fapi%2Fmigrate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmigrate%2Froute.js&appDir=%2FUsers%2Fedwardaver%2FDesktop%2FLimitless%20Checklist%20Project%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fedwardaver%2FDesktop%2FLimitless%20Checklist%20Project&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/migrate/route.js":
/*!**************************************!*\
  !*** ./src/app/api/migrate/route.js ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var _lib_migration__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/migration */ \"(rsc)/./src/lib/migration.js\");\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/**\n * API Route for database migration\n * Handles creating tables and migrating localStorage data\n */ \n\nasync function POST() {\n    try {\n        console.log(\"\\uD83D\\uDE80 Starting database migration...\");\n        const migrationResult = await (0,_lib_migration__WEBPACK_IMPORTED_MODULE_0__.migrateLocalStorageToCloud)();\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_1__[\"default\"].json({\n            success: true,\n            message: \"Migration completed successfully\",\n            data: migrationResult\n        });\n    } catch (error) {\n        console.error(\"Migration error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_1__[\"default\"].json({\n            success: false,\n            error: error.message,\n            details: \"Failed to migrate data to cloud database\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET() {\n    try {\n        const status = await (0,_lib_migration__WEBPACK_IMPORTED_MODULE_0__.checkMigrationStatus)();\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_1__[\"default\"].json({\n            success: true,\n            data: status\n        });\n    } catch (error) {\n        console.error(\"Migration status check error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_1__[\"default\"].json({\n            success: false,\n            error: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/migrate/route.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/cloudStorage.js":
/*!*********************************!*\
  !*** ./src/lib/cloudStorage.js ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ensureUser: () => (/* binding */ ensureUser),\n/* harmony export */   getAllChecklistDatesFromCloud: () => (/* binding */ getAllChecklistDatesFromCloud),\n/* harmony export */   getAllJournalDatesFromCloud: () => (/* binding */ getAllJournalDatesFromCloud),\n/* harmony export */   loadChecklistFromCloud: () => (/* binding */ loadChecklistFromCloud),\n/* harmony export */   loadCourseProgressFromCloud: () => (/* binding */ loadCourseProgressFromCloud),\n/* harmony export */   loadJournalFromCloud: () => (/* binding */ loadJournalFromCloud),\n/* harmony export */   saveChecklistToCloud: () => (/* binding */ saveChecklistToCloud),\n/* harmony export */   saveCourseProgressToCloud: () => (/* binding */ saveCourseProgressToCloud),\n/* harmony export */   saveJournalToCloud: () => (/* binding */ saveJournalToCloud)\n/* harmony export */ });\n/* harmony import */ var _db_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./db.js */ \"(rsc)/./src/lib/db.js\");\n/**\n * Cloud Storage Service for Neon PostgreSQL\n * Handles all database operations with simple, clean API\n */ \n// Simple user ID for now (in production, use proper authentication)\nconst DEFAULT_USER_ID = 1;\n/**\n * Ensure user exists in database\n */ const ensureUser = async (userId = DEFAULT_USER_ID)=>{\n    try {\n        const result = await (0,_db_js__WEBPACK_IMPORTED_MODULE_0__.query)(\"SELECT id FROM users WHERE id = $1\", [\n            userId\n        ]);\n        if (result.rows.length === 0) {\n            await (0,_db_js__WEBPACK_IMPORTED_MODULE_0__.query)(\"INSERT INTO users (id, username) VALUES ($1, $2) ON CONFLICT (id) DO NOTHING\", [\n                userId,\n                \"default_user\"\n            ]);\n        }\n        return userId;\n    } catch (error) {\n        console.error(\"Error ensuring user:\", error);\n        throw error;\n    }\n};\n/**\n * Save checklist data to cloud\n */ const saveChecklistToCloud = async (date, data, userId = DEFAULT_USER_ID)=>{\n    try {\n        await ensureUser(userId);\n        const result = await (0,_db_js__WEBPACK_IMPORTED_MODULE_0__.query)(`\n      INSERT INTO checklists (user_id, date, items, notes, confidence_rating, session_start_time, is_session_active, updated_at)\n      VALUES ($1, $2, $3, $4, $5, $6, $7, CURRENT_TIMESTAMP)\n      ON CONFLICT (user_id, date) \n      DO UPDATE SET \n        items = $3,\n        notes = $4,\n        confidence_rating = $5,\n        session_start_time = $6,\n        is_session_active = $7,\n        updated_at = CURRENT_TIMESTAMP\n    `, [\n            userId,\n            date,\n            JSON.stringify(data.items || {}),\n            JSON.stringify(data.notes || {}),\n            data.confidenceRating || 5,\n            data.sessionStartTime || null,\n            data.isSessionActive || false\n        ]);\n        return true;\n    } catch (error) {\n        console.error(\"Error saving checklist to cloud:\", error);\n        return false;\n    }\n};\n/**\n * Load checklist data from cloud\n */ const loadChecklistFromCloud = async (date, userId = DEFAULT_USER_ID)=>{\n    try {\n        const result = await (0,_db_js__WEBPACK_IMPORTED_MODULE_0__.query)(\"SELECT * FROM checklists WHERE user_id = $1 AND date = $2\", [\n            userId,\n            date\n        ]);\n        if (result.rows.length === 0) {\n            return null;\n        }\n        const row = result.rows[0];\n        return {\n            items: row.items || {},\n            notes: row.notes || {},\n            confidenceRating: row.confidence_rating || 5,\n            sessionStartTime: row.session_start_time,\n            isSessionActive: row.is_session_active || false,\n            lastUpdated: row.updated_at\n        };\n    } catch (error) {\n        console.error(\"Error loading checklist from cloud:\", error);\n        return null;\n    }\n};\n/**\n * Save journal entry to cloud\n */ const saveJournalToCloud = async (date, content, tags = [], userId = DEFAULT_USER_ID)=>{\n    try {\n        await ensureUser(userId);\n        await (0,_db_js__WEBPACK_IMPORTED_MODULE_0__.query)(`\n      INSERT INTO journal_entries (user_id, date, content, tags, updated_at)\n      VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP)\n      ON CONFLICT (user_id, date)\n      DO UPDATE SET \n        content = $3,\n        tags = $4,\n        updated_at = CURRENT_TIMESTAMP\n    `, [\n            userId,\n            date,\n            content,\n            JSON.stringify(tags)\n        ]);\n        return true;\n    } catch (error) {\n        console.error(\"Error saving journal to cloud:\", error);\n        return false;\n    }\n};\n/**\n * Load journal entry from cloud\n */ const loadJournalFromCloud = async (date, userId = DEFAULT_USER_ID)=>{\n    try {\n        const result = await (0,_db_js__WEBPACK_IMPORTED_MODULE_0__.query)(\"SELECT * FROM journal_entries WHERE user_id = $1 AND date = $2\", [\n            userId,\n            date\n        ]);\n        if (result.rows.length === 0) {\n            return null;\n        }\n        const row = result.rows[0];\n        return {\n            content: row.content || \"\",\n            tags: row.tags || [],\n            lastUpdated: row.updated_at\n        };\n    } catch (error) {\n        console.error(\"Error loading journal from cloud:\", error);\n        return null;\n    }\n};\n/**\n * Save course progress to cloud\n */ const saveCourseProgressToCloud = async (courseData, userId = DEFAULT_USER_ID)=>{\n    try {\n        await ensureUser(userId);\n        await (0,_db_js__WEBPACK_IMPORTED_MODULE_0__.query)(`\n      INSERT INTO course_progress (\n        user_id, current_module, current_lesson, completed_lessons, \n        completed_modules, progress, achievements, quiz_scores, \n        bookmarks, notes, last_accessed, updated_at\n      )\n      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, CURRENT_TIMESTAMP)\n      ON CONFLICT (user_id)\n      DO UPDATE SET \n        current_module = $2,\n        current_lesson = $3,\n        completed_lessons = $4,\n        completed_modules = $5,\n        progress = $6,\n        achievements = $7,\n        quiz_scores = $8,\n        bookmarks = $9,\n        notes = $10,\n        last_accessed = $11,\n        updated_at = CURRENT_TIMESTAMP\n    `, [\n            userId,\n            courseData.currentModule,\n            courseData.currentLesson,\n            JSON.stringify(courseData.completedLessons),\n            JSON.stringify(courseData.completedModules),\n            courseData.progress,\n            JSON.stringify(courseData.achievements),\n            JSON.stringify(courseData.quizScores),\n            JSON.stringify(courseData.bookmarks),\n            JSON.stringify(courseData.notes),\n            courseData.lastAccessed\n        ]);\n        return true;\n    } catch (error) {\n        console.error(\"Error saving course progress to cloud:\", error);\n        return false;\n    }\n};\n/**\n * Load course progress from cloud\n */ const loadCourseProgressFromCloud = async (userId = DEFAULT_USER_ID)=>{\n    try {\n        const result = await (0,_db_js__WEBPACK_IMPORTED_MODULE_0__.query)(\"SELECT * FROM course_progress WHERE user_id = $1\", [\n            userId\n        ]);\n        if (result.rows.length === 0) {\n            return null;\n        }\n        const row = result.rows[0];\n        return {\n            currentModule: row.current_module,\n            currentLesson: row.current_lesson,\n            completedLessons: row.completed_lessons || [],\n            completedModules: row.completed_modules || [],\n            progress: row.progress || 0,\n            achievements: row.achievements || [],\n            quizScores: row.quiz_scores || {},\n            bookmarks: row.bookmarks || [],\n            notes: row.notes || {},\n            lastAccessed: row.last_accessed\n        };\n    } catch (error) {\n        console.error(\"Error loading course progress from cloud:\", error);\n        return null;\n    }\n};\n/**\n * Get all checklist dates from cloud\n */ const getAllChecklistDatesFromCloud = async (userId = DEFAULT_USER_ID)=>{\n    try {\n        const result = await (0,_db_js__WEBPACK_IMPORTED_MODULE_0__.query)(\"SELECT date FROM checklists WHERE user_id = $1 ORDER BY date DESC\", [\n            userId\n        ]);\n        return result.rows.map((row)=>row.date.toISOString().split(\"T\")[0]);\n    } catch (error) {\n        console.error(\"Error getting checklist dates from cloud:\", error);\n        return [];\n    }\n};\n/**\n * Get all journal dates from cloud\n */ const getAllJournalDatesFromCloud = async (userId = DEFAULT_USER_ID)=>{\n    try {\n        const result = await (0,_db_js__WEBPACK_IMPORTED_MODULE_0__.query)(\"SELECT date FROM journal_entries WHERE user_id = $1 ORDER BY date DESC\", [\n            userId\n        ]);\n        return result.rows.map((row)=>row.date.toISOString().split(\"T\")[0]);\n    } catch (error) {\n        console.error(\"Error getting journal dates from cloud:\", error);\n        return [];\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/cloudStorage.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.js":
/*!***********************!*\
  !*** ./src/lib/db.js ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getClient: () => (/* binding */ getClient),\n/* harmony export */   initializeDatabase: () => (/* binding */ initializeDatabase),\n/* harmony export */   query: () => (/* binding */ query)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(pg__WEBPACK_IMPORTED_MODULE_0__);\n/**\n * Database connection utility for Neon PostgreSQL\n * Simple, minimal setup for cloud storage\n */ \n// Create a connection pool for better performance\nconst pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool({\n    connectionString: process.env.DATABASE_URL,\n    ssl: {\n        rejectUnauthorized: false\n    },\n    max: 20,\n    idleTimeoutMillis: 30000,\n    connectionTimeoutMillis: 2000\n});\n/**\n * Execute a database query\n * @param {string} text - SQL query\n * @param {Array} params - Query parameters\n * @returns {Promise} Query result\n */ const query = async (text, params)=>{\n    const start = Date.now();\n    try {\n        const res = await pool.query(text, params);\n        const duration = Date.now() - start;\n        console.log(\"Executed query\", {\n            text,\n            duration,\n            rows: res.rowCount\n        });\n        return res;\n    } catch (error) {\n        console.error(\"Database query error:\", error);\n        throw error;\n    }\n};\n/**\n * Get a client from the pool for transactions\n * @returns {Promise} Database client\n */ const getClient = async ()=>{\n    return await pool.connect();\n};\n/**\n * Initialize database tables\n * Creates tables if they don't exist\n */ const initializeDatabase = async ()=>{\n    try {\n        // Users table for basic user management\n        await query(`\n      CREATE TABLE IF NOT EXISTS users (\n        id SERIAL PRIMARY KEY,\n        email VARCHAR(255) UNIQUE,\n        username VARCHAR(100),\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Checklists table\n        await query(`\n      CREATE TABLE IF NOT EXISTS checklists (\n        id SERIAL PRIMARY KEY,\n        user_id INTEGER REFERENCES users(id),\n        date DATE NOT NULL,\n        items JSONB DEFAULT '{}',\n        notes JSONB DEFAULT '{}',\n        confidence_rating INTEGER DEFAULT 5,\n        session_start_time TIMESTAMP,\n        is_session_active BOOLEAN DEFAULT FALSE,\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        UNIQUE(user_id, date)\n      )\n    `);\n        // Journal entries table\n        await query(`\n      CREATE TABLE IF NOT EXISTS journal_entries (\n        id SERIAL PRIMARY KEY,\n        user_id INTEGER REFERENCES users(id),\n        date DATE NOT NULL,\n        content TEXT,\n        tags JSONB DEFAULT '[]',\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        UNIQUE(user_id, date)\n      )\n    `);\n        // Course progress table\n        await query(`\n      CREATE TABLE IF NOT EXISTS course_progress (\n        id SERIAL PRIMARY KEY,\n        user_id INTEGER REFERENCES users(id),\n        current_module INTEGER DEFAULT 1,\n        current_lesson INTEGER DEFAULT 1,\n        completed_lessons JSONB DEFAULT '[]',\n        completed_modules JSONB DEFAULT '[]',\n        progress INTEGER DEFAULT 0,\n        achievements JSONB DEFAULT '[]',\n        quiz_scores JSONB DEFAULT '{}',\n        bookmarks JSONB DEFAULT '[]',\n        notes JSONB DEFAULT '{}',\n        last_accessed TIMESTAMP,\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        UNIQUE(user_id)\n      )\n    `);\n        // User preferences table\n        await query(`\n      CREATE TABLE IF NOT EXISTS user_preferences (\n        id SERIAL PRIMARY KEY,\n        user_id INTEGER REFERENCES users(id),\n        theme VARCHAR(20) DEFAULT 'light',\n        language VARCHAR(10) DEFAULT 'en',\n        notifications BOOLEAN DEFAULT TRUE,\n        auto_save BOOLEAN DEFAULT TRUE,\n        confidence_rating BOOLEAN DEFAULT TRUE,\n        session_timer BOOLEAN DEFAULT FALSE,\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        UNIQUE(user_id)\n      )\n    `);\n        console.log(\"Database tables initialized successfully\");\n        return true;\n    } catch (error) {\n        console.error(\"Error initializing database:\", error);\n        throw error;\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (pool);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/migration.js":
/*!******************************!*\
  !*** ./src/lib/migration.js ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkMigrationStatus: () => (/* binding */ checkMigrationStatus),\n/* harmony export */   migrateLocalStorageToCloud: () => (/* binding */ migrateLocalStorageToCloud)\n/* harmony export */ });\n/* harmony import */ var _db_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./db.js */ \"(rsc)/./src/lib/db.js\");\n/* harmony import */ var _cloudStorage_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./cloudStorage.js */ \"(rsc)/./src/lib/cloudStorage.js\");\n/**\n * Database Migration Utility\n * Creates tables and migrates localStorage data to Neon PostgreSQL\n */ \n\n/**\n * Migrate localStorage data to cloud database\n */ const migrateLocalStorageToCloud = async ()=>{\n    console.log(\"\\uD83D\\uDE80 Starting migration from localStorage to cloud...\");\n    try {\n        // Step 1: Initialize database tables\n        console.log(\"\\uD83D\\uDCCB Creating database tables...\");\n        await (0,_db_js__WEBPACK_IMPORTED_MODULE_0__.initializeDatabase)();\n        // Step 2: Ensure default user exists\n        console.log(\"\\uD83D\\uDC64 Creating default user...\");\n        await (0,_cloudStorage_js__WEBPACK_IMPORTED_MODULE_1__.ensureUser)(1);\n        // Step 3: Migrate checklist data\n        console.log(\"✅ Migrating checklist data...\");\n        const checklistMigrated = await migrateChecklistData();\n        // Step 4: Migrate journal data\n        console.log(\"\\uD83D\\uDCDD Migrating journal data...\");\n        const journalMigrated = await migrateJournalData();\n        // Step 5: Migrate course progress\n        console.log(\"\\uD83C\\uDF93 Migrating course progress...\");\n        const courseMigrated = await migrateCourseProgress();\n        // Step 6: Migrate user preferences\n        console.log(\"⚙️ Migrating user preferences...\");\n        const preferencesMigrated = await migrateUserPreferences();\n        const summary = {\n            success: true,\n            checklistEntries: checklistMigrated,\n            journalEntries: journalMigrated,\n            courseProgress: courseMigrated,\n            preferences: preferencesMigrated,\n            timestamp: new Date().toISOString()\n        };\n        console.log(\"\\uD83C\\uDF89 Migration completed successfully!\");\n        console.log(\"\\uD83D\\uDCCA Migration Summary:\", summary);\n        return summary;\n    } catch (error) {\n        console.error(\"❌ Migration failed:\", error);\n        throw error;\n    }\n};\n/**\n * Migrate checklist data from localStorage\n */ const migrateChecklistData = async ()=>{\n    let migratedCount = 0;\n    try {\n        // Get all localStorage keys for checklists\n        const checklistKeys = [];\n        for(let i = 0; i < localStorage.length; i++){\n            const key = localStorage.key(i);\n            if (key && key.startsWith(\"limitless_checklist_\")) {\n                checklistKeys.push(key);\n            }\n        }\n        console.log(`📋 Found ${checklistKeys.length} checklist entries to migrate`);\n        for (const key of checklistKeys){\n            try {\n                const data = localStorage.getItem(key);\n                if (data) {\n                    const parsedData = JSON.parse(data);\n                    const date = key.replace(\"limitless_checklist_\", \"\");\n                    // Transform localStorage format to cloud format\n                    const cloudData = {\n                        items: parsedData.checkedItems || parsedData.items || {},\n                        notes: parsedData.notes || {},\n                        confidenceRating: parsedData.confidenceRating || 5,\n                        sessionStartTime: parsedData.sessionStartTime || null,\n                        isSessionActive: parsedData.isSessionActive || false\n                    };\n                    const success = await (0,_cloudStorage_js__WEBPACK_IMPORTED_MODULE_1__.saveChecklistToCloud)(date, cloudData);\n                    if (success) {\n                        migratedCount++;\n                        console.log(`✅ Migrated checklist for ${date}`);\n                    } else {\n                        console.warn(`⚠️ Failed to migrate checklist for ${date}`);\n                    }\n                }\n            } catch (error) {\n                console.error(`❌ Error migrating checklist ${key}:`, error);\n            }\n        }\n        return migratedCount;\n    } catch (error) {\n        console.error(\"❌ Error in checklist migration:\", error);\n        return migratedCount;\n    }\n};\n/**\n * Migrate journal data from localStorage\n */ const migrateJournalData = async ()=>{\n    let migratedCount = 0;\n    try {\n        // Get all localStorage keys for journals\n        const journalKeys = [];\n        for(let i = 0; i < localStorage.length; i++){\n            const key = localStorage.key(i);\n            if (key && key.startsWith(\"limitless_journal_\")) {\n                journalKeys.push(key);\n            }\n        }\n        console.log(`📝 Found ${journalKeys.length} journal entries to migrate`);\n        for (const key of journalKeys){\n            try {\n                const data = localStorage.getItem(key);\n                if (data) {\n                    const parsedData = JSON.parse(data);\n                    const date = key.replace(\"limitless_journal_\", \"\");\n                    const success = await (0,_cloudStorage_js__WEBPACK_IMPORTED_MODULE_1__.saveJournalToCloud)(date, parsedData.content || \"\", parsedData.tags || []);\n                    if (success) {\n                        migratedCount++;\n                        console.log(`✅ Migrated journal for ${date}`);\n                    } else {\n                        console.warn(`⚠️ Failed to migrate journal for ${date}`);\n                    }\n                }\n            } catch (error) {\n                console.error(`❌ Error migrating journal ${key}:`, error);\n            }\n        }\n        return migratedCount;\n    } catch (error) {\n        console.error(\"❌ Error in journal migration:\", error);\n        return migratedCount;\n    }\n};\n/**\n * Migrate course progress from localStorage/Zustand store\n */ const migrateCourseProgress = async ()=>{\n    try {\n        // Get course data from Zustand store (localStorage)\n        const storeData = localStorage.getItem(\"limitless-options-store\");\n        if (!storeData) {\n            console.log(\"\\uD83D\\uDCDA No course progress found to migrate\");\n            return false;\n        }\n        const parsedStore = JSON.parse(storeData);\n        const courseData = parsedStore.state?.course;\n        if (!courseData) {\n            console.log(\"\\uD83D\\uDCDA No course data in store to migrate\");\n            return false;\n        }\n        const success = await (0,_cloudStorage_js__WEBPACK_IMPORTED_MODULE_1__.saveCourseProgressToCloud)(courseData);\n        if (success) {\n            console.log(\"✅ Migrated course progress\");\n            return true;\n        } else {\n            console.warn(\"⚠️ Failed to migrate course progress\");\n            return false;\n        }\n    } catch (error) {\n        console.error(\"❌ Error migrating course progress:\", error);\n        return false;\n    }\n};\n/**\n * Migrate user preferences\n */ const migrateUserPreferences = async ()=>{\n    try {\n        // Get preferences from Zustand store\n        const storeData = localStorage.getItem(\"limitless-options-store\");\n        if (!storeData) {\n            console.log(\"⚙️ No preferences found to migrate\");\n            return false;\n        }\n        const parsedStore = JSON.parse(storeData);\n        const theme = parsedStore.state?.theme || \"light\";\n        const preferences = parsedStore.state?.preferences || {};\n        // Save preferences to database\n        await (0,_db_js__WEBPACK_IMPORTED_MODULE_0__.query)(`\n      INSERT INTO user_preferences (\n        user_id, theme, language, notifications, auto_save, \n        confidence_rating, session_timer\n      )\n      VALUES ($1, $2, $3, $4, $5, $6, $7)\n      ON CONFLICT (user_id)\n      DO UPDATE SET \n        theme = $2,\n        language = $3,\n        notifications = $4,\n        auto_save = $5,\n        confidence_rating = $6,\n        session_timer = $7,\n        updated_at = CURRENT_TIMESTAMP\n    `, [\n            1,\n            theme,\n            preferences.language || \"en\",\n            preferences.notifications !== false,\n            preferences.autoSave !== false,\n            preferences.confidenceRating !== false,\n            preferences.sessionTimer || false\n        ]);\n        console.log(\"✅ Migrated user preferences\");\n        return true;\n    } catch (error) {\n        console.error(\"❌ Error migrating preferences:\", error);\n        return false;\n    }\n};\n/**\n * Check migration status\n */ const checkMigrationStatus = async ()=>{\n    try {\n        // Check if tables exist and have data\n        const checklistCount = await (0,_db_js__WEBPACK_IMPORTED_MODULE_0__.query)(\"SELECT COUNT(*) FROM checklists WHERE user_id = 1\");\n        const journalCount = await (0,_db_js__WEBPACK_IMPORTED_MODULE_0__.query)(\"SELECT COUNT(*) FROM journal_entries WHERE user_id = 1\");\n        const courseExists = await (0,_db_js__WEBPACK_IMPORTED_MODULE_0__.query)(\"SELECT COUNT(*) FROM course_progress WHERE user_id = 1\");\n        const preferencesExists = await (0,_db_js__WEBPACK_IMPORTED_MODULE_0__.query)(\"SELECT COUNT(*) FROM user_preferences WHERE user_id = 1\");\n        return {\n            tablesExist: true,\n            checklistEntries: parseInt(checklistCount.rows[0].count),\n            journalEntries: parseInt(journalCount.rows[0].count),\n            courseProgress: parseInt(courseExists.rows[0].count) > 0,\n            preferences: parseInt(preferencesExists.rows[0].count) > 0\n        };\n    } catch (error) {\n        console.error(\"Error checking migration status:\", error);\n        return {\n            tablesExist: false,\n            checklistEntries: 0,\n            journalEntries: 0,\n            courseProgress: false,\n            preferences: false\n        };\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL21pZ3JhdGlvbi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQUE7OztDQUdDLEdBRWtEO0FBTXpCO0FBRTFCOztDQUVDLEdBQ00sTUFBTU0sNkJBQTZCO0lBQ3hDQyxRQUFRQyxHQUFHLENBQUM7SUFFWixJQUFJO1FBQ0YscUNBQXFDO1FBQ3JDRCxRQUFRQyxHQUFHLENBQUM7UUFDWixNQUFNUCwwREFBa0JBO1FBRXhCLHFDQUFxQztRQUNyQ00sUUFBUUMsR0FBRyxDQUFDO1FBQ1osTUFBTUgsNERBQVVBLENBQUM7UUFFakIsaUNBQWlDO1FBQ2pDRSxRQUFRQyxHQUFHLENBQUM7UUFDWixNQUFNQyxvQkFBb0IsTUFBTUM7UUFFaEMsK0JBQStCO1FBQy9CSCxRQUFRQyxHQUFHLENBQUM7UUFDWixNQUFNRyxrQkFBa0IsTUFBTUM7UUFFOUIsa0NBQWtDO1FBQ2xDTCxRQUFRQyxHQUFHLENBQUM7UUFDWixNQUFNSyxpQkFBaUIsTUFBTUM7UUFFN0IsbUNBQW1DO1FBQ25DUCxRQUFRQyxHQUFHLENBQUM7UUFDWixNQUFNTyxzQkFBc0IsTUFBTUM7UUFFbEMsTUFBTUMsVUFBVTtZQUNkQyxTQUFTO1lBQ1RDLGtCQUFrQlY7WUFDbEJXLGdCQUFnQlQ7WUFDaEJVLGdCQUFnQlI7WUFDaEJTLGFBQWFQO1lBQ2JRLFdBQVcsSUFBSUMsT0FBT0MsV0FBVztRQUNuQztRQUVBbEIsUUFBUUMsR0FBRyxDQUFDO1FBQ1pELFFBQVFDLEdBQUcsQ0FBQyxtQ0FBeUJTO1FBRXJDLE9BQU9BO0lBRVQsRUFBRSxPQUFPUyxPQUFPO1FBQ2RuQixRQUFRbUIsS0FBSyxDQUFDLHVCQUF1QkE7UUFDckMsTUFBTUE7SUFDUjtBQUNGLEVBQUM7QUFFRDs7Q0FFQyxHQUNELE1BQU1oQix1QkFBdUI7SUFDM0IsSUFBSWlCLGdCQUFnQjtJQUVwQixJQUFJO1FBQ0YsMkNBQTJDO1FBQzNDLE1BQU1DLGdCQUFnQixFQUFFO1FBQ3hCLElBQUssSUFBSUMsSUFBSSxHQUFHQSxJQUFJQyxhQUFhQyxNQUFNLEVBQUVGLElBQUs7WUFDNUMsTUFBTUcsTUFBTUYsYUFBYUUsR0FBRyxDQUFDSDtZQUM3QixJQUFJRyxPQUFPQSxJQUFJQyxVQUFVLENBQUMseUJBQXlCO2dCQUNqREwsY0FBY00sSUFBSSxDQUFDRjtZQUNyQjtRQUNGO1FBRUF6QixRQUFRQyxHQUFHLENBQUMsQ0FBQyxTQUFTLEVBQUVvQixjQUFjRyxNQUFNLENBQUMsNkJBQTZCLENBQUM7UUFFM0UsS0FBSyxNQUFNQyxPQUFPSixjQUFlO1lBQy9CLElBQUk7Z0JBQ0YsTUFBTU8sT0FBT0wsYUFBYU0sT0FBTyxDQUFDSjtnQkFDbEMsSUFBSUcsTUFBTTtvQkFDUixNQUFNRSxhQUFhQyxLQUFLQyxLQUFLLENBQUNKO29CQUM5QixNQUFNSyxPQUFPUixJQUFJUyxPQUFPLENBQUMsd0JBQXdCO29CQUVqRCxnREFBZ0Q7b0JBQ2hELE1BQU1DLFlBQVk7d0JBQ2hCQyxPQUFPTixXQUFXTyxZQUFZLElBQUlQLFdBQVdNLEtBQUssSUFBSSxDQUFDO3dCQUN2REUsT0FBT1IsV0FBV1EsS0FBSyxJQUFJLENBQUM7d0JBQzVCQyxrQkFBa0JULFdBQVdTLGdCQUFnQixJQUFJO3dCQUNqREMsa0JBQWtCVixXQUFXVSxnQkFBZ0IsSUFBSTt3QkFDakRDLGlCQUFpQlgsV0FBV1csZUFBZSxJQUFJO29CQUNqRDtvQkFFQSxNQUFNOUIsVUFBVSxNQUFNaEIsc0VBQW9CQSxDQUFDc0MsTUFBTUU7b0JBQ2pELElBQUl4QixTQUFTO3dCQUNYUzt3QkFDQXBCLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLHlCQUF5QixFQUFFZ0MsS0FBSyxDQUFDO29CQUNoRCxPQUFPO3dCQUNMakMsUUFBUTBDLElBQUksQ0FBQyxDQUFDLG1DQUFtQyxFQUFFVCxLQUFLLENBQUM7b0JBQzNEO2dCQUNGO1lBQ0YsRUFBRSxPQUFPZCxPQUFPO2dCQUNkbkIsUUFBUW1CLEtBQUssQ0FBQyxDQUFDLDRCQUE0QixFQUFFTSxJQUFJLENBQUMsQ0FBQyxFQUFFTjtZQUN2RDtRQUNGO1FBRUEsT0FBT0M7SUFDVCxFQUFFLE9BQU9ELE9BQU87UUFDZG5CLFFBQVFtQixLQUFLLENBQUMsbUNBQW1DQTtRQUNqRCxPQUFPQztJQUNUO0FBQ0Y7QUFFQTs7Q0FFQyxHQUNELE1BQU1mLHFCQUFxQjtJQUN6QixJQUFJZSxnQkFBZ0I7SUFFcEIsSUFBSTtRQUNGLHlDQUF5QztRQUN6QyxNQUFNdUIsY0FBYyxFQUFFO1FBQ3RCLElBQUssSUFBSXJCLElBQUksR0FBR0EsSUFBSUMsYUFBYUMsTUFBTSxFQUFFRixJQUFLO1lBQzVDLE1BQU1HLE1BQU1GLGFBQWFFLEdBQUcsQ0FBQ0g7WUFDN0IsSUFBSUcsT0FBT0EsSUFBSUMsVUFBVSxDQUFDLHVCQUF1QjtnQkFDL0NpQixZQUFZaEIsSUFBSSxDQUFDRjtZQUNuQjtRQUNGO1FBRUF6QixRQUFRQyxHQUFHLENBQUMsQ0FBQyxTQUFTLEVBQUUwQyxZQUFZbkIsTUFBTSxDQUFDLDJCQUEyQixDQUFDO1FBRXZFLEtBQUssTUFBTUMsT0FBT2tCLFlBQWE7WUFDN0IsSUFBSTtnQkFDRixNQUFNZixPQUFPTCxhQUFhTSxPQUFPLENBQUNKO2dCQUNsQyxJQUFJRyxNQUFNO29CQUNSLE1BQU1FLGFBQWFDLEtBQUtDLEtBQUssQ0FBQ0o7b0JBQzlCLE1BQU1LLE9BQU9SLElBQUlTLE9BQU8sQ0FBQyxzQkFBc0I7b0JBRS9DLE1BQU12QixVQUFVLE1BQU1mLG9FQUFrQkEsQ0FDdENxQyxNQUNBSCxXQUFXYyxPQUFPLElBQUksSUFDdEJkLFdBQVdlLElBQUksSUFBSSxFQUFFO29CQUd2QixJQUFJbEMsU0FBUzt3QkFDWFM7d0JBQ0FwQixRQUFRQyxHQUFHLENBQUMsQ0FBQyx1QkFBdUIsRUFBRWdDLEtBQUssQ0FBQztvQkFDOUMsT0FBTzt3QkFDTGpDLFFBQVEwQyxJQUFJLENBQUMsQ0FBQyxpQ0FBaUMsRUFBRVQsS0FBSyxDQUFDO29CQUN6RDtnQkFDRjtZQUNGLEVBQUUsT0FBT2QsT0FBTztnQkFDZG5CLFFBQVFtQixLQUFLLENBQUMsQ0FBQywwQkFBMEIsRUFBRU0sSUFBSSxDQUFDLENBQUMsRUFBRU47WUFDckQ7UUFDRjtRQUVBLE9BQU9DO0lBQ1QsRUFBRSxPQUFPRCxPQUFPO1FBQ2RuQixRQUFRbUIsS0FBSyxDQUFDLGlDQUFpQ0E7UUFDL0MsT0FBT0M7SUFDVDtBQUNGO0FBRUE7O0NBRUMsR0FDRCxNQUFNYix3QkFBd0I7SUFDNUIsSUFBSTtRQUNGLG9EQUFvRDtRQUNwRCxNQUFNdUMsWUFBWXZCLGFBQWFNLE9BQU8sQ0FBQztRQUN2QyxJQUFJLENBQUNpQixXQUFXO1lBQ2Q5QyxRQUFRQyxHQUFHLENBQUM7WUFDWixPQUFPO1FBQ1Q7UUFFQSxNQUFNOEMsY0FBY2hCLEtBQUtDLEtBQUssQ0FBQ2M7UUFDL0IsTUFBTUUsYUFBYUQsWUFBWUUsS0FBSyxFQUFFQztRQUV0QyxJQUFJLENBQUNGLFlBQVk7WUFDZmhELFFBQVFDLEdBQUcsQ0FBQztZQUNaLE9BQU87UUFDVDtRQUVBLE1BQU1VLFVBQVUsTUFBTWQsMkVBQXlCQSxDQUFDbUQ7UUFDaEQsSUFBSXJDLFNBQVM7WUFDWFgsUUFBUUMsR0FBRyxDQUFDO1lBQ1osT0FBTztRQUNULE9BQU87WUFDTEQsUUFBUTBDLElBQUksQ0FBQztZQUNiLE9BQU87UUFDVDtJQUNGLEVBQUUsT0FBT3ZCLE9BQU87UUFDZG5CLFFBQVFtQixLQUFLLENBQUMsc0NBQXNDQTtRQUNwRCxPQUFPO0lBQ1Q7QUFDRjtBQUVBOztDQUVDLEdBQ0QsTUFBTVYseUJBQXlCO0lBQzdCLElBQUk7UUFDRixxQ0FBcUM7UUFDckMsTUFBTXFDLFlBQVl2QixhQUFhTSxPQUFPLENBQUM7UUFDdkMsSUFBSSxDQUFDaUIsV0FBVztZQUNkOUMsUUFBUUMsR0FBRyxDQUFDO1lBQ1osT0FBTztRQUNUO1FBRUEsTUFBTThDLGNBQWNoQixLQUFLQyxLQUFLLENBQUNjO1FBQy9CLE1BQU1LLFFBQVFKLFlBQVlFLEtBQUssRUFBRUUsU0FBUztRQUMxQyxNQUFNcEMsY0FBY2dDLFlBQVlFLEtBQUssRUFBRWxDLGVBQWUsQ0FBQztRQUV2RCwrQkFBK0I7UUFDL0IsTUFBTXRCLDZDQUFLQSxDQUFDLENBQUM7Ozs7Ozs7Ozs7Ozs7OztJQWViLENBQUMsRUFBRTtZQUNEO1lBQ0EwRDtZQUNBcEMsWUFBWXFDLFFBQVEsSUFBSTtZQUN4QnJDLFlBQVlzQyxhQUFhLEtBQUs7WUFDOUJ0QyxZQUFZdUMsUUFBUSxLQUFLO1lBQ3pCdkMsWUFBWXdCLGdCQUFnQixLQUFLO1lBQ2pDeEIsWUFBWXdDLFlBQVksSUFBSTtTQUM3QjtRQUVEdkQsUUFBUUMsR0FBRyxDQUFDO1FBQ1osT0FBTztJQUNULEVBQUUsT0FBT2tCLE9BQU87UUFDZG5CLFFBQVFtQixLQUFLLENBQUMsa0NBQWtDQTtRQUNoRCxPQUFPO0lBQ1Q7QUFDRjtBQUVBOztDQUVDLEdBQ00sTUFBTXFDLHVCQUF1QjtJQUNsQyxJQUFJO1FBQ0Ysc0NBQXNDO1FBQ3RDLE1BQU1DLGlCQUFpQixNQUFNaEUsNkNBQUtBLENBQUM7UUFDbkMsTUFBTWlFLGVBQWUsTUFBTWpFLDZDQUFLQSxDQUFDO1FBQ2pDLE1BQU1rRSxlQUFlLE1BQU1sRSw2Q0FBS0EsQ0FBQztRQUNqQyxNQUFNbUUsb0JBQW9CLE1BQU1uRSw2Q0FBS0EsQ0FBQztRQUV0QyxPQUFPO1lBQ0xvRSxhQUFhO1lBQ2JqRCxrQkFBa0JrRCxTQUFTTCxlQUFlTSxJQUFJLENBQUMsRUFBRSxDQUFDQyxLQUFLO1lBQ3ZEbkQsZ0JBQWdCaUQsU0FBU0osYUFBYUssSUFBSSxDQUFDLEVBQUUsQ0FBQ0MsS0FBSztZQUNuRGxELGdCQUFnQmdELFNBQVNILGFBQWFJLElBQUksQ0FBQyxFQUFFLENBQUNDLEtBQUssSUFBSTtZQUN2RGpELGFBQWErQyxTQUFTRixrQkFBa0JHLElBQUksQ0FBQyxFQUFFLENBQUNDLEtBQUssSUFBSTtRQUMzRDtJQUNGLEVBQUUsT0FBTzdDLE9BQU87UUFDZG5CLFFBQVFtQixLQUFLLENBQUMsb0NBQW9DQTtRQUNsRCxPQUFPO1lBQ0wwQyxhQUFhO1lBQ2JqRCxrQkFBa0I7WUFDbEJDLGdCQUFnQjtZQUNoQkMsZ0JBQWdCO1lBQ2hCQyxhQUFhO1FBQ2Y7SUFDRjtBQUNGLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9saW1pdGxlc3Mtb3B0aW9ucy1jaGVja2xpc3QvLi9zcmMvbGliL21pZ3JhdGlvbi5qcz84NGE0Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogRGF0YWJhc2UgTWlncmF0aW9uIFV0aWxpdHlcbiAqIENyZWF0ZXMgdGFibGVzIGFuZCBtaWdyYXRlcyBsb2NhbFN0b3JhZ2UgZGF0YSB0byBOZW9uIFBvc3RncmVTUUxcbiAqL1xuXG5pbXBvcnQgeyBxdWVyeSwgaW5pdGlhbGl6ZURhdGFiYXNlIH0gZnJvbSAnLi9kYi5qcydcbmltcG9ydCB7IFxuICBzYXZlQ2hlY2tsaXN0VG9DbG91ZCwgXG4gIHNhdmVKb3VybmFsVG9DbG91ZCwgXG4gIHNhdmVDb3Vyc2VQcm9ncmVzc1RvQ2xvdWQsXG4gIGVuc3VyZVVzZXIgXG59IGZyb20gJy4vY2xvdWRTdG9yYWdlLmpzJ1xuXG4vKipcbiAqIE1pZ3JhdGUgbG9jYWxTdG9yYWdlIGRhdGEgdG8gY2xvdWQgZGF0YWJhc2VcbiAqL1xuZXhwb3J0IGNvbnN0IG1pZ3JhdGVMb2NhbFN0b3JhZ2VUb0Nsb3VkID0gYXN5bmMgKCkgPT4ge1xuICBjb25zb2xlLmxvZygn8J+agCBTdGFydGluZyBtaWdyYXRpb24gZnJvbSBsb2NhbFN0b3JhZ2UgdG8gY2xvdWQuLi4nKVxuICBcbiAgdHJ5IHtcbiAgICAvLyBTdGVwIDE6IEluaXRpYWxpemUgZGF0YWJhc2UgdGFibGVzXG4gICAgY29uc29sZS5sb2coJ/Cfk4sgQ3JlYXRpbmcgZGF0YWJhc2UgdGFibGVzLi4uJylcbiAgICBhd2FpdCBpbml0aWFsaXplRGF0YWJhc2UoKVxuICAgIFxuICAgIC8vIFN0ZXAgMjogRW5zdXJlIGRlZmF1bHQgdXNlciBleGlzdHNcbiAgICBjb25zb2xlLmxvZygn8J+RpCBDcmVhdGluZyBkZWZhdWx0IHVzZXIuLi4nKVxuICAgIGF3YWl0IGVuc3VyZVVzZXIoMSlcbiAgICBcbiAgICAvLyBTdGVwIDM6IE1pZ3JhdGUgY2hlY2tsaXN0IGRhdGFcbiAgICBjb25zb2xlLmxvZygn4pyFIE1pZ3JhdGluZyBjaGVja2xpc3QgZGF0YS4uLicpXG4gICAgY29uc3QgY2hlY2tsaXN0TWlncmF0ZWQgPSBhd2FpdCBtaWdyYXRlQ2hlY2tsaXN0RGF0YSgpXG4gICAgXG4gICAgLy8gU3RlcCA0OiBNaWdyYXRlIGpvdXJuYWwgZGF0YVxuICAgIGNvbnNvbGUubG9nKCfwn5OdIE1pZ3JhdGluZyBqb3VybmFsIGRhdGEuLi4nKVxuICAgIGNvbnN0IGpvdXJuYWxNaWdyYXRlZCA9IGF3YWl0IG1pZ3JhdGVKb3VybmFsRGF0YSgpXG4gICAgXG4gICAgLy8gU3RlcCA1OiBNaWdyYXRlIGNvdXJzZSBwcm9ncmVzc1xuICAgIGNvbnNvbGUubG9nKCfwn46TIE1pZ3JhdGluZyBjb3Vyc2UgcHJvZ3Jlc3MuLi4nKVxuICAgIGNvbnN0IGNvdXJzZU1pZ3JhdGVkID0gYXdhaXQgbWlncmF0ZUNvdXJzZVByb2dyZXNzKClcbiAgICBcbiAgICAvLyBTdGVwIDY6IE1pZ3JhdGUgdXNlciBwcmVmZXJlbmNlc1xuICAgIGNvbnNvbGUubG9nKCfimpnvuI8gTWlncmF0aW5nIHVzZXIgcHJlZmVyZW5jZXMuLi4nKVxuICAgIGNvbnN0IHByZWZlcmVuY2VzTWlncmF0ZWQgPSBhd2FpdCBtaWdyYXRlVXNlclByZWZlcmVuY2VzKClcbiAgICBcbiAgICBjb25zdCBzdW1tYXJ5ID0ge1xuICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgIGNoZWNrbGlzdEVudHJpZXM6IGNoZWNrbGlzdE1pZ3JhdGVkLFxuICAgICAgam91cm5hbEVudHJpZXM6IGpvdXJuYWxNaWdyYXRlZCxcbiAgICAgIGNvdXJzZVByb2dyZXNzOiBjb3Vyc2VNaWdyYXRlZCxcbiAgICAgIHByZWZlcmVuY2VzOiBwcmVmZXJlbmNlc01pZ3JhdGVkLFxuICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICB9XG4gICAgXG4gICAgY29uc29sZS5sb2coJ/CfjokgTWlncmF0aW9uIGNvbXBsZXRlZCBzdWNjZXNzZnVsbHkhJylcbiAgICBjb25zb2xlLmxvZygn8J+TiiBNaWdyYXRpb24gU3VtbWFyeTonLCBzdW1tYXJ5KVxuICAgIFxuICAgIHJldHVybiBzdW1tYXJ5XG4gICAgXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcign4p2MIE1pZ3JhdGlvbiBmYWlsZWQ6JywgZXJyb3IpXG4gICAgdGhyb3cgZXJyb3JcbiAgfVxufVxuXG4vKipcbiAqIE1pZ3JhdGUgY2hlY2tsaXN0IGRhdGEgZnJvbSBsb2NhbFN0b3JhZ2VcbiAqL1xuY29uc3QgbWlncmF0ZUNoZWNrbGlzdERhdGEgPSBhc3luYyAoKSA9PiB7XG4gIGxldCBtaWdyYXRlZENvdW50ID0gMFxuICBcbiAgdHJ5IHtcbiAgICAvLyBHZXQgYWxsIGxvY2FsU3RvcmFnZSBrZXlzIGZvciBjaGVja2xpc3RzXG4gICAgY29uc3QgY2hlY2tsaXN0S2V5cyA9IFtdXG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBsb2NhbFN0b3JhZ2UubGVuZ3RoOyBpKyspIHtcbiAgICAgIGNvbnN0IGtleSA9IGxvY2FsU3RvcmFnZS5rZXkoaSlcbiAgICAgIGlmIChrZXkgJiYga2V5LnN0YXJ0c1dpdGgoJ2xpbWl0bGVzc19jaGVja2xpc3RfJykpIHtcbiAgICAgICAgY2hlY2tsaXN0S2V5cy5wdXNoKGtleSlcbiAgICAgIH1cbiAgICB9XG4gICAgXG4gICAgY29uc29sZS5sb2coYPCfk4sgRm91bmQgJHtjaGVja2xpc3RLZXlzLmxlbmd0aH0gY2hlY2tsaXN0IGVudHJpZXMgdG8gbWlncmF0ZWApXG4gICAgXG4gICAgZm9yIChjb25zdCBrZXkgb2YgY2hlY2tsaXN0S2V5cykge1xuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3QgZGF0YSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKGtleSlcbiAgICAgICAgaWYgKGRhdGEpIHtcbiAgICAgICAgICBjb25zdCBwYXJzZWREYXRhID0gSlNPTi5wYXJzZShkYXRhKVxuICAgICAgICAgIGNvbnN0IGRhdGUgPSBrZXkucmVwbGFjZSgnbGltaXRsZXNzX2NoZWNrbGlzdF8nLCAnJylcbiAgICAgICAgICBcbiAgICAgICAgICAvLyBUcmFuc2Zvcm0gbG9jYWxTdG9yYWdlIGZvcm1hdCB0byBjbG91ZCBmb3JtYXRcbiAgICAgICAgICBjb25zdCBjbG91ZERhdGEgPSB7XG4gICAgICAgICAgICBpdGVtczogcGFyc2VkRGF0YS5jaGVja2VkSXRlbXMgfHwgcGFyc2VkRGF0YS5pdGVtcyB8fCB7fSxcbiAgICAgICAgICAgIG5vdGVzOiBwYXJzZWREYXRhLm5vdGVzIHx8IHt9LFxuICAgICAgICAgICAgY29uZmlkZW5jZVJhdGluZzogcGFyc2VkRGF0YS5jb25maWRlbmNlUmF0aW5nIHx8IDUsXG4gICAgICAgICAgICBzZXNzaW9uU3RhcnRUaW1lOiBwYXJzZWREYXRhLnNlc3Npb25TdGFydFRpbWUgfHwgbnVsbCxcbiAgICAgICAgICAgIGlzU2Vzc2lvbkFjdGl2ZTogcGFyc2VkRGF0YS5pc1Nlc3Npb25BY3RpdmUgfHwgZmFsc2VcbiAgICAgICAgICB9XG4gICAgICAgICAgXG4gICAgICAgICAgY29uc3Qgc3VjY2VzcyA9IGF3YWl0IHNhdmVDaGVja2xpc3RUb0Nsb3VkKGRhdGUsIGNsb3VkRGF0YSlcbiAgICAgICAgICBpZiAoc3VjY2Vzcykge1xuICAgICAgICAgICAgbWlncmF0ZWRDb3VudCsrXG4gICAgICAgICAgICBjb25zb2xlLmxvZyhg4pyFIE1pZ3JhdGVkIGNoZWNrbGlzdCBmb3IgJHtkYXRlfWApXG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIGNvbnNvbGUud2Fybihg4pqg77iPIEZhaWxlZCB0byBtaWdyYXRlIGNoZWNrbGlzdCBmb3IgJHtkYXRlfWApXG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKGDinYwgRXJyb3IgbWlncmF0aW5nIGNoZWNrbGlzdCAke2tleX06YCwgZXJyb3IpXG4gICAgICB9XG4gICAgfVxuICAgIFxuICAgIHJldHVybiBtaWdyYXRlZENvdW50XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcign4p2MIEVycm9yIGluIGNoZWNrbGlzdCBtaWdyYXRpb246JywgZXJyb3IpXG4gICAgcmV0dXJuIG1pZ3JhdGVkQ291bnRcbiAgfVxufVxuXG4vKipcbiAqIE1pZ3JhdGUgam91cm5hbCBkYXRhIGZyb20gbG9jYWxTdG9yYWdlXG4gKi9cbmNvbnN0IG1pZ3JhdGVKb3VybmFsRGF0YSA9IGFzeW5jICgpID0+IHtcbiAgbGV0IG1pZ3JhdGVkQ291bnQgPSAwXG4gIFxuICB0cnkge1xuICAgIC8vIEdldCBhbGwgbG9jYWxTdG9yYWdlIGtleXMgZm9yIGpvdXJuYWxzXG4gICAgY29uc3Qgam91cm5hbEtleXMgPSBbXVxuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgbG9jYWxTdG9yYWdlLmxlbmd0aDsgaSsrKSB7XG4gICAgICBjb25zdCBrZXkgPSBsb2NhbFN0b3JhZ2Uua2V5KGkpXG4gICAgICBpZiAoa2V5ICYmIGtleS5zdGFydHNXaXRoKCdsaW1pdGxlc3Nfam91cm5hbF8nKSkge1xuICAgICAgICBqb3VybmFsS2V5cy5wdXNoKGtleSlcbiAgICAgIH1cbiAgICB9XG4gICAgXG4gICAgY29uc29sZS5sb2coYPCfk50gRm91bmQgJHtqb3VybmFsS2V5cy5sZW5ndGh9IGpvdXJuYWwgZW50cmllcyB0byBtaWdyYXRlYClcbiAgICBcbiAgICBmb3IgKGNvbnN0IGtleSBvZiBqb3VybmFsS2V5cykge1xuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3QgZGF0YSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKGtleSlcbiAgICAgICAgaWYgKGRhdGEpIHtcbiAgICAgICAgICBjb25zdCBwYXJzZWREYXRhID0gSlNPTi5wYXJzZShkYXRhKVxuICAgICAgICAgIGNvbnN0IGRhdGUgPSBrZXkucmVwbGFjZSgnbGltaXRsZXNzX2pvdXJuYWxfJywgJycpXG4gICAgICAgICAgXG4gICAgICAgICAgY29uc3Qgc3VjY2VzcyA9IGF3YWl0IHNhdmVKb3VybmFsVG9DbG91ZChcbiAgICAgICAgICAgIGRhdGUsIFxuICAgICAgICAgICAgcGFyc2VkRGF0YS5jb250ZW50IHx8ICcnLCBcbiAgICAgICAgICAgIHBhcnNlZERhdGEudGFncyB8fCBbXVxuICAgICAgICAgIClcbiAgICAgICAgICBcbiAgICAgICAgICBpZiAoc3VjY2Vzcykge1xuICAgICAgICAgICAgbWlncmF0ZWRDb3VudCsrXG4gICAgICAgICAgICBjb25zb2xlLmxvZyhg4pyFIE1pZ3JhdGVkIGpvdXJuYWwgZm9yICR7ZGF0ZX1gKVxuICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBjb25zb2xlLndhcm4oYOKaoO+4jyBGYWlsZWQgdG8gbWlncmF0ZSBqb3VybmFsIGZvciAke2RhdGV9YClcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoYOKdjCBFcnJvciBtaWdyYXRpbmcgam91cm5hbCAke2tleX06YCwgZXJyb3IpXG4gICAgICB9XG4gICAgfVxuICAgIFxuICAgIHJldHVybiBtaWdyYXRlZENvdW50XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcign4p2MIEVycm9yIGluIGpvdXJuYWwgbWlncmF0aW9uOicsIGVycm9yKVxuICAgIHJldHVybiBtaWdyYXRlZENvdW50XG4gIH1cbn1cblxuLyoqXG4gKiBNaWdyYXRlIGNvdXJzZSBwcm9ncmVzcyBmcm9tIGxvY2FsU3RvcmFnZS9adXN0YW5kIHN0b3JlXG4gKi9cbmNvbnN0IG1pZ3JhdGVDb3Vyc2VQcm9ncmVzcyA9IGFzeW5jICgpID0+IHtcbiAgdHJ5IHtcbiAgICAvLyBHZXQgY291cnNlIGRhdGEgZnJvbSBadXN0YW5kIHN0b3JlIChsb2NhbFN0b3JhZ2UpXG4gICAgY29uc3Qgc3RvcmVEYXRhID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2xpbWl0bGVzcy1vcHRpb25zLXN0b3JlJylcbiAgICBpZiAoIXN0b3JlRGF0YSkge1xuICAgICAgY29uc29sZS5sb2coJ/Cfk5ogTm8gY291cnNlIHByb2dyZXNzIGZvdW5kIHRvIG1pZ3JhdGUnKVxuICAgICAgcmV0dXJuIGZhbHNlXG4gICAgfVxuICAgIFxuICAgIGNvbnN0IHBhcnNlZFN0b3JlID0gSlNPTi5wYXJzZShzdG9yZURhdGEpXG4gICAgY29uc3QgY291cnNlRGF0YSA9IHBhcnNlZFN0b3JlLnN0YXRlPy5jb3Vyc2VcbiAgICBcbiAgICBpZiAoIWNvdXJzZURhdGEpIHtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5OaIE5vIGNvdXJzZSBkYXRhIGluIHN0b3JlIHRvIG1pZ3JhdGUnKVxuICAgICAgcmV0dXJuIGZhbHNlXG4gICAgfVxuICAgIFxuICAgIGNvbnN0IHN1Y2Nlc3MgPSBhd2FpdCBzYXZlQ291cnNlUHJvZ3Jlc3NUb0Nsb3VkKGNvdXJzZURhdGEpXG4gICAgaWYgKHN1Y2Nlc3MpIHtcbiAgICAgIGNvbnNvbGUubG9nKCfinIUgTWlncmF0ZWQgY291cnNlIHByb2dyZXNzJylcbiAgICAgIHJldHVybiB0cnVlXG4gICAgfSBlbHNlIHtcbiAgICAgIGNvbnNvbGUud2Fybign4pqg77iPIEZhaWxlZCB0byBtaWdyYXRlIGNvdXJzZSBwcm9ncmVzcycpXG4gICAgICByZXR1cm4gZmFsc2VcbiAgICB9XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcign4p2MIEVycm9yIG1pZ3JhdGluZyBjb3Vyc2UgcHJvZ3Jlc3M6JywgZXJyb3IpXG4gICAgcmV0dXJuIGZhbHNlXG4gIH1cbn1cblxuLyoqXG4gKiBNaWdyYXRlIHVzZXIgcHJlZmVyZW5jZXNcbiAqL1xuY29uc3QgbWlncmF0ZVVzZXJQcmVmZXJlbmNlcyA9IGFzeW5jICgpID0+IHtcbiAgdHJ5IHtcbiAgICAvLyBHZXQgcHJlZmVyZW5jZXMgZnJvbSBadXN0YW5kIHN0b3JlXG4gICAgY29uc3Qgc3RvcmVEYXRhID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2xpbWl0bGVzcy1vcHRpb25zLXN0b3JlJylcbiAgICBpZiAoIXN0b3JlRGF0YSkge1xuICAgICAgY29uc29sZS5sb2coJ+Kame+4jyBObyBwcmVmZXJlbmNlcyBmb3VuZCB0byBtaWdyYXRlJylcbiAgICAgIHJldHVybiBmYWxzZVxuICAgIH1cbiAgICBcbiAgICBjb25zdCBwYXJzZWRTdG9yZSA9IEpTT04ucGFyc2Uoc3RvcmVEYXRhKVxuICAgIGNvbnN0IHRoZW1lID0gcGFyc2VkU3RvcmUuc3RhdGU/LnRoZW1lIHx8ICdsaWdodCdcbiAgICBjb25zdCBwcmVmZXJlbmNlcyA9IHBhcnNlZFN0b3JlLnN0YXRlPy5wcmVmZXJlbmNlcyB8fCB7fVxuICAgIFxuICAgIC8vIFNhdmUgcHJlZmVyZW5jZXMgdG8gZGF0YWJhc2VcbiAgICBhd2FpdCBxdWVyeShgXG4gICAgICBJTlNFUlQgSU5UTyB1c2VyX3ByZWZlcmVuY2VzIChcbiAgICAgICAgdXNlcl9pZCwgdGhlbWUsIGxhbmd1YWdlLCBub3RpZmljYXRpb25zLCBhdXRvX3NhdmUsIFxuICAgICAgICBjb25maWRlbmNlX3JhdGluZywgc2Vzc2lvbl90aW1lclxuICAgICAgKVxuICAgICAgVkFMVUVTICgkMSwgJDIsICQzLCAkNCwgJDUsICQ2LCAkNylcbiAgICAgIE9OIENPTkZMSUNUICh1c2VyX2lkKVxuICAgICAgRE8gVVBEQVRFIFNFVCBcbiAgICAgICAgdGhlbWUgPSAkMixcbiAgICAgICAgbGFuZ3VhZ2UgPSAkMyxcbiAgICAgICAgbm90aWZpY2F0aW9ucyA9ICQ0LFxuICAgICAgICBhdXRvX3NhdmUgPSAkNSxcbiAgICAgICAgY29uZmlkZW5jZV9yYXRpbmcgPSAkNixcbiAgICAgICAgc2Vzc2lvbl90aW1lciA9ICQ3LFxuICAgICAgICB1cGRhdGVkX2F0ID0gQ1VSUkVOVF9USU1FU1RBTVBcbiAgICBgLCBbXG4gICAgICAxLCAvLyB1c2VyIElEXG4gICAgICB0aGVtZSxcbiAgICAgIHByZWZlcmVuY2VzLmxhbmd1YWdlIHx8ICdlbicsXG4gICAgICBwcmVmZXJlbmNlcy5ub3RpZmljYXRpb25zICE9PSBmYWxzZSxcbiAgICAgIHByZWZlcmVuY2VzLmF1dG9TYXZlICE9PSBmYWxzZSxcbiAgICAgIHByZWZlcmVuY2VzLmNvbmZpZGVuY2VSYXRpbmcgIT09IGZhbHNlLFxuICAgICAgcHJlZmVyZW5jZXMuc2Vzc2lvblRpbWVyIHx8IGZhbHNlXG4gICAgXSlcbiAgICBcbiAgICBjb25zb2xlLmxvZygn4pyFIE1pZ3JhdGVkIHVzZXIgcHJlZmVyZW5jZXMnKVxuICAgIHJldHVybiB0cnVlXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcign4p2MIEVycm9yIG1pZ3JhdGluZyBwcmVmZXJlbmNlczonLCBlcnJvcilcbiAgICByZXR1cm4gZmFsc2VcbiAgfVxufVxuXG4vKipcbiAqIENoZWNrIG1pZ3JhdGlvbiBzdGF0dXNcbiAqL1xuZXhwb3J0IGNvbnN0IGNoZWNrTWlncmF0aW9uU3RhdHVzID0gYXN5bmMgKCkgPT4ge1xuICB0cnkge1xuICAgIC8vIENoZWNrIGlmIHRhYmxlcyBleGlzdCBhbmQgaGF2ZSBkYXRhXG4gICAgY29uc3QgY2hlY2tsaXN0Q291bnQgPSBhd2FpdCBxdWVyeSgnU0VMRUNUIENPVU5UKCopIEZST00gY2hlY2tsaXN0cyBXSEVSRSB1c2VyX2lkID0gMScpXG4gICAgY29uc3Qgam91cm5hbENvdW50ID0gYXdhaXQgcXVlcnkoJ1NFTEVDVCBDT1VOVCgqKSBGUk9NIGpvdXJuYWxfZW50cmllcyBXSEVSRSB1c2VyX2lkID0gMScpXG4gICAgY29uc3QgY291cnNlRXhpc3RzID0gYXdhaXQgcXVlcnkoJ1NFTEVDVCBDT1VOVCgqKSBGUk9NIGNvdXJzZV9wcm9ncmVzcyBXSEVSRSB1c2VyX2lkID0gMScpXG4gICAgY29uc3QgcHJlZmVyZW5jZXNFeGlzdHMgPSBhd2FpdCBxdWVyeSgnU0VMRUNUIENPVU5UKCopIEZST00gdXNlcl9wcmVmZXJlbmNlcyBXSEVSRSB1c2VyX2lkID0gMScpXG4gICAgXG4gICAgcmV0dXJuIHtcbiAgICAgIHRhYmxlc0V4aXN0OiB0cnVlLFxuICAgICAgY2hlY2tsaXN0RW50cmllczogcGFyc2VJbnQoY2hlY2tsaXN0Q291bnQucm93c1swXS5jb3VudCksXG4gICAgICBqb3VybmFsRW50cmllczogcGFyc2VJbnQoam91cm5hbENvdW50LnJvd3NbMF0uY291bnQpLFxuICAgICAgY291cnNlUHJvZ3Jlc3M6IHBhcnNlSW50KGNvdXJzZUV4aXN0cy5yb3dzWzBdLmNvdW50KSA+IDAsXG4gICAgICBwcmVmZXJlbmNlczogcGFyc2VJbnQocHJlZmVyZW5jZXNFeGlzdHMucm93c1swXS5jb3VudCkgPiAwXG4gICAgfVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNoZWNraW5nIG1pZ3JhdGlvbiBzdGF0dXM6JywgZXJyb3IpXG4gICAgcmV0dXJuIHtcbiAgICAgIHRhYmxlc0V4aXN0OiBmYWxzZSxcbiAgICAgIGNoZWNrbGlzdEVudHJpZXM6IDAsXG4gICAgICBqb3VybmFsRW50cmllczogMCxcbiAgICAgIGNvdXJzZVByb2dyZXNzOiBmYWxzZSxcbiAgICAgIHByZWZlcmVuY2VzOiBmYWxzZVxuICAgIH1cbiAgfVxufVxuIl0sIm5hbWVzIjpbInF1ZXJ5IiwiaW5pdGlhbGl6ZURhdGFiYXNlIiwic2F2ZUNoZWNrbGlzdFRvQ2xvdWQiLCJzYXZlSm91cm5hbFRvQ2xvdWQiLCJzYXZlQ291cnNlUHJvZ3Jlc3NUb0Nsb3VkIiwiZW5zdXJlVXNlciIsIm1pZ3JhdGVMb2NhbFN0b3JhZ2VUb0Nsb3VkIiwiY29uc29sZSIsImxvZyIsImNoZWNrbGlzdE1pZ3JhdGVkIiwibWlncmF0ZUNoZWNrbGlzdERhdGEiLCJqb3VybmFsTWlncmF0ZWQiLCJtaWdyYXRlSm91cm5hbERhdGEiLCJjb3Vyc2VNaWdyYXRlZCIsIm1pZ3JhdGVDb3Vyc2VQcm9ncmVzcyIsInByZWZlcmVuY2VzTWlncmF0ZWQiLCJtaWdyYXRlVXNlclByZWZlcmVuY2VzIiwic3VtbWFyeSIsInN1Y2Nlc3MiLCJjaGVja2xpc3RFbnRyaWVzIiwiam91cm5hbEVudHJpZXMiLCJjb3Vyc2VQcm9ncmVzcyIsInByZWZlcmVuY2VzIiwidGltZXN0YW1wIiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwiZXJyb3IiLCJtaWdyYXRlZENvdW50IiwiY2hlY2tsaXN0S2V5cyIsImkiLCJsb2NhbFN0b3JhZ2UiLCJsZW5ndGgiLCJrZXkiLCJzdGFydHNXaXRoIiwicHVzaCIsImRhdGEiLCJnZXRJdGVtIiwicGFyc2VkRGF0YSIsIkpTT04iLCJwYXJzZSIsImRhdGUiLCJyZXBsYWNlIiwiY2xvdWREYXRhIiwiaXRlbXMiLCJjaGVja2VkSXRlbXMiLCJub3RlcyIsImNvbmZpZGVuY2VSYXRpbmciLCJzZXNzaW9uU3RhcnRUaW1lIiwiaXNTZXNzaW9uQWN0aXZlIiwid2FybiIsImpvdXJuYWxLZXlzIiwiY29udGVudCIsInRhZ3MiLCJzdG9yZURhdGEiLCJwYXJzZWRTdG9yZSIsImNvdXJzZURhdGEiLCJzdGF0ZSIsImNvdXJzZSIsInRoZW1lIiwibGFuZ3VhZ2UiLCJub3RpZmljYXRpb25zIiwiYXV0b1NhdmUiLCJzZXNzaW9uVGltZXIiLCJjaGVja01pZ3JhdGlvblN0YXR1cyIsImNoZWNrbGlzdENvdW50Iiwiam91cm5hbENvdW50IiwiY291cnNlRXhpc3RzIiwicHJlZmVyZW5jZXNFeGlzdHMiLCJ0YWJsZXNFeGlzdCIsInBhcnNlSW50Iiwicm93cyIsImNvdW50Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/migration.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fmigrate%2Froute&page=%2Fapi%2Fmigrate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmigrate%2Froute.js&appDir=%2FUsers%2Fedwardaver%2FDesktop%2FLimitless%20Checklist%20Project%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fedwardaver%2FDesktop%2FLimitless%20Checklist%20Project&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();